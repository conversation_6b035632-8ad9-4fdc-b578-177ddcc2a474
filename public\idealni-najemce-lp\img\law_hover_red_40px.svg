<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 40 40">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_23" data-name="Rectangle 23" width="40" height="40"/>
    </clipPath>
  </defs>
  <g id="law_hover_red_40px" clip-path="url(#clip-path)">
    <g id="Group_299" data-name="Group 299" transform="translate(11.549 242.488)">
      <path id="Path_412" data-name="Path 412" d="M1379.967,22.786v-.953l-1.077-1.085-.224-1.073V16.73l2.119-1.742,9.872.443-.485,3.471-1.712,1.848-.889,1.522v1Z" transform="translate(-1375.549 -242.488)" fill="#fff"/>
      <path id="Path_270" data-name="Path 270" d="M1.444,0h.09A1.679,1.679,0,0,1,2.977,1.841V4.8A1.679,1.679,0,0,1,1.534,6.641h-.09A1.679,1.679,0,0,1,0,4.8V1.841A1.679,1.679,0,0,1,1.444,0Z" transform="translate(2.203 -232.055)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_274" data-name="Path 274" d="M1.527,0h.217A1.664,1.664,0,0,1,3.271,1.772v5A1.664,1.664,0,0,1,1.744,8.539H1.527A1.664,1.664,0,0,1,0,6.767V1.772A1.664,1.664,0,0,1,1.527,0Z" transform="translate(5.181 -233.637)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_278" data-name="Path 278" d="M1.488,0h.221A1.693,1.693,0,0,1,3.2,1.841V6.7A1.693,1.693,0,0,1,1.708,8.539h-.22A1.693,1.693,0,0,1,0,6.7V1.841A1.693,1.693,0,0,1,1.488,0Z" transform="translate(8.451 -233.637)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_282" data-name="Path 282" d="M1.41,0h.074a1.623,1.623,0,0,1,1.41,1.772v4.2a1.623,1.623,0,0,1-1.41,1.772H1.41A1.623,1.623,0,0,1,0,5.976v-4.2A1.623,1.623,0,0,1,1.41,0Z" transform="translate(11.647 -232.846)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_288" data-name="Path 288" d="M139.74-184.555q.613-1.947,1.889-1.947h2.29q-3.962.232-3.651-2.5l4.429-.042q1.37.119,1.37,2.016a8.36,8.36,0,0,1-.556,3.228q-2.262,3.086-2.445,3.25a2.584,2.584,0,0,0,.036.344l-.036.866" transform="translate(-131.052 -39.688)" fill="#fff" fill-rule="evenodd"/>
      <g id="Group_257" data-name="Group 257" transform="translate(14.167 -228.418)">
        <path id="Path_260" data-name="Path 260" d="M0,.58H5.665" transform="translate(0 -0.58)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_258" data-name="Group 258" transform="translate(-2.621 -228.418)">
        <path id="Path_261" data-name="Path 261" d="M1.023.659,5.848.58" transform="translate(-1.023 -0.58)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_259" data-name="Group 259" transform="translate(-7.288 -228.418)">
        <path id="Path_262" data-name="Path 262" d="M0,0Q-.016,2.53,2.2,2.53T4.429,0" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_260" data-name="Group 260" transform="translate(24.19 -225.888) rotate(180)">
        <path id="Path_263" data-name="Path 263" d="M0,2.53Q-.016,0,2.2,0T4.43,2.53" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_261" data-name="Group 261" transform="translate(-9.819 -211.498)">
        <path id="Path_264" data-name="Path 264" d="M.006,0H8.848q.223,5.851-4.392,5.851T.006,0Z" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_262" data-name="Group 262" transform="translate(-9.819 -225.414)">
        <path id="Path_265" data-name="Path 265" d="M.041,13.937,0,13.163,4.46,0,8.836,13.193l.022,1.2" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_263" data-name="Group 263" transform="translate(17.863 -211.498)">
        <path id="Path_266" data-name="Path 266" d="M.006,0h8.84q.243,5.851-4.38,5.851T.006,0Z" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_264" data-name="Group 264" transform="translate(17.863 -225.414)">
        <path id="Path_267" data-name="Path 267" d="M.041,13.937,0,13.163,4.46,0,8.836,13.193l.022,1.2" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_265" data-name="Group 265" transform="translate(3.152 -225.414)">
        <path id="Path_268" data-name="Path 268" d="M0,0V2.523A3.5,3.5,0,0,0,1.21,4.781V6.167" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_293" data-name="Group 293" transform="translate(8.689 -228.735)">
        <path id="Path_289" data-name="Path 289" d="M0,4.391q.613-1.9,1.889-1.9H4.18Q.217,2.714.529.041L4.958,0q1.37.116,1.37,1.971a8.013,8.013,0,0,1-.556,3.156Q3.509,8.145,3.326,8.3a2.475,2.475,0,0,0,.036.336l-.036.847" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_295" data-name="Group 295" transform="translate(3.152 -219.247)">
        <path id="Path_291" data-name="Path 291" d="M0,0H10.282V16.287H0Z" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <path id="Path_295" data-name="Path 295" d="M116.642-98.53a1.228,1.228,0,0,0,1.265-1.186,1.228,1.228,0,0,0-1.265-1.186,1.228,1.228,0,0,0-1.265,1.186,1.228,1.228,0,0,0,1.265,1.186Z" transform="translate(-110.01 -115.815)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_299-2" data-name="Group 299" transform="translate(11.549 242.488)">
      <g id="Group_257-2" data-name="Group 257" transform="translate(14.167 -228.418)">
        <path id="Path_260-2" data-name="Path 260" d="M0,.58H5.665" transform="translate(0 -0.58)" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_258-2" data-name="Group 258" transform="translate(-2.621 -228.418)">
        <path id="Path_261-2" data-name="Path 261" d="M1.023.659,5.848.58" transform="translate(-1.023 -0.58)" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_259-2" data-name="Group 259" transform="translate(-7.288 -228.418)">
        <path id="Path_262-2" data-name="Path 262" d="M0,0Q-.016,2.53,2.2,2.53T4.429,0" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_260-2" data-name="Group 260" transform="translate(24.19 -225.888) rotate(180)">
        <path id="Path_263-2" data-name="Path 263" d="M0,2.53Q-.016,0,2.2,0T4.43,2.53" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_262-2" data-name="Group 262" transform="translate(-9.819 -225.414)">
        <path id="Path_265-2" data-name="Path 265" d="M.041,13.937,0,13.163,4.46,0,8.836,13.193l.022,1.2" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_264-2" data-name="Group 264" transform="translate(17.863 -225.414)">
        <path id="Path_267-2" data-name="Path 267" d="M.041,13.937,0,13.163,4.46,0,8.836,13.193l.022,1.2" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_265-2" data-name="Group 265" transform="translate(3.152 -225.414)">
        <path id="Path_268-2" data-name="Path 268" d="M0,0V2.523A3.5,3.5,0,0,0,1.21,4.781V6.167" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <path id="Path_269" data-name="Path 269" d="M-12-201.6H28.9V-235H-12Z" fill="none"/>
      <path id="Path_270-2" data-name="Path 270" d="M1.444,0h.09A1.679,1.679,0,0,1,2.977,1.841V4.8A1.679,1.679,0,0,1,1.534,6.641h-.09A1.679,1.679,0,0,1,0,4.8V1.841A1.679,1.679,0,0,1,1.444,0Z" transform="translate(2.203 -232.055)" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_272" data-name="Path 272" d="M-12-201.6H28.9V-235H-12Z" fill="none"/>
      <path id="Path_273" data-name="Path 273" d="M94.014-213.4h.115a1.841,1.841,0,0,1,1.841,1.841v2.96a1.841,1.841,0,0,1-1.841,1.841h-.115a1.841,1.841,0,0,1-1.841-1.841v-2.96a1.841,1.841,0,0,1,1.841-1.841Z" transform="translate(-89.97 -18.653)" fill="none" fill-rule="evenodd"/>
      <path id="Path_274-2" data-name="Path 274" d="M1.527,0h.217A1.664,1.664,0,0,1,3.271,1.772v5A1.664,1.664,0,0,1,1.744,8.539H1.527A1.664,1.664,0,0,1,0,6.767V1.772A1.664,1.664,0,0,1,1.527,0Z" transform="translate(5.181 -233.637)" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_276" data-name="Path 276" d="M-12-201.6H28.9V-235H-12Z" fill="none"/>
      <path id="Path_277" data-name="Path 277" d="M114.829-225h.251a1.772,1.772,0,0,1,1.772,1.772v4.994a1.772,1.772,0,0,1-1.772,1.772h-.251a1.772,1.772,0,0,1-1.772-1.772v-4.994A1.772,1.772,0,0,1,114.829-225Z" transform="translate(-108.006 -8.637)" fill="none" fill-rule="evenodd"/>
      <path id="Path_278-2" data-name="Path 278" d="M1.488,0h.221A1.693,1.693,0,0,1,3.2,1.841V6.7A1.693,1.693,0,0,1,1.708,8.539h-.22A1.693,1.693,0,0,1,0,6.7V1.841A1.693,1.693,0,0,1,1.488,0Z" transform="translate(8.451 -233.637)" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_280" data-name="Path 280" d="M-12-201.6H28.9V-235H-12Z" fill="none"/>
      <path id="Path_281" data-name="Path 281" d="M135.78-225h.273a1.841,1.841,0,0,1,1.841,1.841v4.858a1.841,1.841,0,0,1-1.841,1.841h-.273a1.841,1.841,0,0,1-1.841-1.841v-4.858A1.841,1.841,0,0,1,135.78-225Z" transform="translate(-126.042 -8.637)" fill="none" fill-rule="evenodd"/>
      <path id="Path_282-2" data-name="Path 282" d="M1.41,0h.074a1.623,1.623,0,0,1,1.41,1.772v4.2a1.623,1.623,0,0,1-1.41,1.772H1.41A1.623,1.623,0,0,1,0,5.976v-4.2A1.623,1.623,0,0,1,1.41,0Z" transform="translate(11.647 -232.846)" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_284" data-name="Path 284" d="M-12-201.6H28.9V-235H-12Z" fill="none"/>
      <path id="Path_285" data-name="Path 285" d="M157.755-219.2h.093a1.773,1.773,0,0,1,1.772,1.772v4.2a1.773,1.773,0,0,1-1.772,1.772h-.093a1.772,1.772,0,0,1-1.772-1.772v-4.2a1.772,1.772,0,0,1,1.772-1.772Z" transform="translate(-145.079 -13.645)" fill="none" fill-rule="evenodd"/>
      <path id="Path_287" data-name="Path 287" d="M-12-201.6H28.9V-235H-12Z" fill="none"/>
      <path id="Path_288-2" data-name="Path 288" d="M139.74-184.555q.613-1.947,1.889-1.947h2.29q-3.962.232-3.651-2.5l4.429-.042q1.37.119,1.37,2.016a8.36,8.36,0,0,1-.556,3.228q-2.262,3.086-2.445,3.25a2.584,2.584,0,0,0,.036.344l-.036.866" transform="translate(-131.052 -39.688)" fill="none" fill-rule="evenodd"/>
      <g id="Group_293-2" data-name="Group 293" transform="translate(8.689 -228.735)">
        <path id="Path_289-2" data-name="Path 289" d="M0,4.391q.613-1.9,1.889-1.9H4.18Q.217,2.714.529.041L4.958,0q1.37.116,1.37,1.971a8.013,8.013,0,0,1-.556,3.156Q3.509,8.145,3.326,8.3a2.475,2.475,0,0,0,.036.336l-.036.847" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <path id="Path_290" data-name="Path 290" d="M-12-201.6H28.9V-235H-12Z" fill="none"/>
      <g id="Group_295-2" data-name="Group 295" transform="translate(3.152 -219.247)">
        <path id="Path_291-2" data-name="Path 291" d="M0,0H10.282V16.287H0Z" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <path id="Path_294" data-name="Path 294" d="M-12-201.6H28.9V-235H-12Z" fill="none"/>
      <path id="Path_295-2" data-name="Path 295" d="M116.642-98.53a1.228,1.228,0,0,0,1.265-1.186,1.228,1.228,0,0,0-1.265-1.186,1.228,1.228,0,0,0-1.265,1.186,1.228,1.228,0,0,0,1.265,1.186Z" transform="translate(-110.01 -115.815)" fill="#212121" fill-rule="evenodd"/>
      <g id="Group_263-2" data-name="Group 263" transform="translate(16.751 -212.233)">
        <path id="Path_266-2" data-name="Path 266" d="M.007,0h11.06q.3,7.32-5.48,7.32T.007,0Z" fill="#e74c4c" stroke="#e74c4c" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_261-2" data-name="Group 261" transform="translate(-10.93 -212.233)">
        <path id="Path_264-2" data-name="Path 264" d="M.007,0H11.069q.28,7.32-5.494,7.32T.007,0Z" fill="#e74c4c" stroke="#e74c4c" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
    </g>
  </g>
</svg>
