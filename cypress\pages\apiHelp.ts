import { faker } from "@faker-js/faker";

import { getUdBeApiUrl } from "../helper/helper";

export function createOffer(user: any) {
  cy.then(() => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/offer`,
      headers: {
        authorization: `Bear<PERSON> ${sessionStorage.getItem("ACCESS_TOKEN")}`,
      },
      body: {
        offer_type_id: "1",
        disposition_id: "2",
        village_string: "Slaný",
        village: { id: "v4549" },
        street_and_number_string: "Soukenická",
        street_and_number: { id: "s74454" },
        acreage: 12,
        price_rental: faker.number.int({ min: 5000, max: 20000 }),
        price_monthly_fee: 1000,
        price_commission: false,
        price_deposit: 5000,
        photos: null,
        related: null,
        available: "date",
        available_from: null,
        furnishing_id: "MEDIUM",
        conveniences: [],
        description: "",
        user_id: null,
        user_name: null,
        user_type_id: 1,
        user_email: user.mail,
        user_phone: "",
        user_password: "",
        user_first_name: user.name,
        user_surname: user.surname,
        company_name: "",
        available_date: "",
        energy_efficiency_rating_id: "2",
        floor_level: "p1",
      },
    }).then((resp) => {
      expect(resp.status).to.equal(200);
      expect(resp.body).to.have.property("id");
      localStorage.setItem("offerId", resp.body.id);
    });
  });
}

export function activateOffer(offerIdData: string) {
  cy.request({
    method: "POST",
    url: `${getUdBeApiUrl()}fe-api/offer/${offerIdData}/activate`,
    headers: {
      authorization: `Bearer ${sessionStorage.getItem("ACCESS_TOKEN")}`,
    },
    body: { offerId: offerIdData },
  }).then((resp) => {
    expect(resp.status).to.equal(200);
  });
}

export function apiLoginForTests(user: any) {
  cy.request({
    method: "POST",
    url: `${getUdBeApiUrl()}fe-api/auth/login`,
    body: {
      email: user.login,
      password: user.password,
    },
  }).then((resp) => {
    expect(resp.status).equal(200);
    localStorage.setItem("test-accessToken", resp.body.accessToken);
  });
}

export function sendMessage(
  token: string,
  offerId: string,
  phone: string,
  message: string,
  name?: string,
  surname?: string,
) {
  cy.request({
    method: "POST",
    url: `${getUdBeApiUrl()}fe-api/feed/add`,
    headers: { authorization: `Bearer ${token}` },
    body: {
      first_name: name === undefined ? "" : name,
      last_name: surname === undefined ? "" : surname,
      phone,
      rent_id: offerId,
      msg: message,
      display_card: true,
    },
  });
}

export function deleteOffer(offerId: string) {
  cy.then(() => {
    cy.request({
      method: "DELETE",
      url: `${getUdBeApiUrl()}fe-api/offer/${offerId}`,
      headers: {
        authorization: `Bearer ${sessionStorage.getItem("ACCESS_TOKEN")}`,
      },
    }).then((resp) => {
      expect(resp.status).to.be.equal(200);
    });
  });
}

export function promoCode(orderId: string) {
  cy.then(() => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/payment/promo-code`,
      headers: {
        authorization: `Bearer ${sessionStorage.getItem("ACCESS_TOKEN")}`,
      },
      body: { code: "theonecode", order_type_id: orderId },
    }).then((resp) => {
      expect(resp.status).to.be.equal(200);
    });
  });
}

export function changePassword(newPass: string, oldPass: string) {
  cy.then(() => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/user/new-password`,
      headers: {
        authorization: `Bearer ${sessionStorage.getItem("ACCESS_TOKEN")}`,
      },
      body: { password: newPass, old_password: oldPass },
    }).then((resp) => {
      expect(resp.status).to.be.equal(200);
    });
  });
}
