import { fakerCS_CZ } from "@faker-js/faker";

import { urls } from "../constants/ulrs";
import * as ud from "../pages/inlanding";

const findMore = "in.landing.scrollButton";
const profitButton = "in.landing.profitBtn";
const balanceButton = "in.landing.balancBtn";
const fixButton = "in.landing.fixBtn";
const meetUp = "in.landing.meetUp";

const badData = {
  name: fakerCS_CZ.person.fullName(),
  phone: "000000000",
  email: fakerCS_CZ.internet.email(),
};

const data = {
  name: fakerCS_CZ.person.fullName(),
  phone: fakerCS_CZ.phone.number(),
  email: fakerCS_CZ.internet.email(),
};

describe("In Landing Page Test", () => {
  context("Desktop View", () => {
    beforeEach(() => {
      cy.visit(urls.IN_LANDING_IFRAME);
    });

    it("Check Page Elements", () => {
      ud.checkMainPageElements();
      ud.checkFormElements();
      ud.checkGridWapperElements();
      ud.checkVariantsElements();
      ud.checkConsultationsElements();
      ud.checkRecommendElements();
    });

    it("Check Scroll Find More Button", () => {
      ud.checkScrollBtns(findMore);
    });

    it("Check Scroll Profit Button", () => {
      ud.checkScrollBtns(profitButton);
    });

    it("Check Scroll Balance Button", () => {
      ud.checkScrollBtns(balanceButton);
    });

    it("Check Scroll Fix Button", () => {
      ud.checkScrollBtns(fixButton);
    });

    it("Check Scroll MeetUp Button", () => {
      ud.checkScrollBtns(meetUp);
    });

    it("Check Redirect Link", () => {
      ud.checkRedirectLink();
    });

    it("Fill Form Bad Data", () => {
      ud.fillBadData(badData);
    });

    it("Fill Form Data", () => {
      ud.fillData(data);
    });
  });

  context("Mobile View", () => {
    beforeEach(() => {
      cy.viewport(390, 844);
      cy.visit(urls.IN_LANDING_IFRAME);
    });

    it("Check Page Elements", () => {
      ud.checkMainPageElementsMobile();
      ud.checkFormElements();
      ud.checkGridWapperElements();
      ud.checkVariantsElements();
      ud.checkConsultationsElements();
      ud.checkRecommendElements();
    });

    it("Check Scroll Find More Button", () => {
      ud.checkScrollBtnsMobile(findMore);
    });

    it("Check Scroll Profit Button", () => {
      ud.checkScrollBtnsMobile(profitButton);
    });

    it("Check Scroll Balance Button", () => {
      ud.checkScrollBtnsMobile(balanceButton);
    });

    it("Check Scroll Fix Button", () => {
      ud.checkScrollBtnsMobile(fixButton);
    });

    it("Check Scroll MeetUp Button", () => {
      ud.checkScrollBtnsMobile(meetUp);
    });

    it("Check Redirect Link", () => {
      ud.checkRedirectLink();
    });

    it("Fill Form Bad Data", () => {
      ud.fillBadData(badData);
    });

    it("Fill Form Data", () => {
      ud.fillData(data);
    });
  });
});
