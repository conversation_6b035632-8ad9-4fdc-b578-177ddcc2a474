import { getUdBeApiUrl } from "../helper/helper";
import { waitForPageLoaded } from "../pages/core";
import * as login from "../pages/login";

// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })

Cypress.Commands.add("getByTestId", (testId) => {
  cy.get(`[data-test="${testId}"]`);
});

Cypress.Commands.add("apiLogin", (user) => {
  cy.intercept("GET", "**/user/current").as("current");
  cy.intercept("POST", "**/auth/refresh").as("refresh");
  cy.intercept("GET", "**/user/notification**").as("notif");
  cy.request({
    method: "POST",
    url: `${getUdBeApiUrl()}fe-api/auth/login`,
    body: {
      email: user.login,
      password: user.password,
    },
  }).then((resp) => {
    expect(resp.status).equal(200);
    localStorage.setItem("ACCESS", resp.body.accessToken);
    localStorage.setItem("REFRESH_TOKEN", resp.body.refreshToken);
  });
  cy.visit("/");
  waitForPageLoaded();
  cy.wait(["@refresh", "@notif"]);
  cy.get("@current")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).equal(200);
      expect(resp?.body.email).equal(user.login);
    });
  waitForPageLoaded();
  cy.get('[data-test="navbar.content.userAvatar.mail"]').should("be.visible", {
    setTimeout: 30000,
  });
});

Cypress.Commands.add("login", (user, option?: boolean) => {
  cy.intercept("POST", "**/fe-api/user/email-exists").as("email-exist");
  cy.intercept("POST", "**/fe-api/forgotten-password").as("email-sent");
  cy.intercept("POST", "**/api/v1/user").as("email-reg");
  cy.intercept("GET", `**/${user.login}`).as("login-mail");
  cy.visit("/");

  login.goToLoginModal();
  login.fillLoginMail(user.login);
  login.continueToPassword();
  login.fillPassword(user.password, option);

  cy.intercept("GET", "**/user/current").as("current-login");
  login.checkLogInSucces(user.login);

  cy.wait("@current-login")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).equal(200);
      expect(resp?.body.email).equal(user.login);
    });
});

Cypress.Commands.add("apiRegister", (user) => {
  let accessToken: string;
  let userId: number; // eslint-disable-line

  cy.request({
    method: "POST",
    url: `${getUdBeApiUrl()}fe-api/auth/registration`,
    body: { email: user.login, password: user.password },
  })
    .then((resp) => {
      expect(resp.status).equal(200);
      accessToken = resp.body.accessToken;
    })
    .then(() => {
      cy.request({
        method: "GET",
        url: `${getUdBeApiUrl()}fe-api/user/current`,
        headers: { authorization: `Bearer ${accessToken}` },
      }).then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).has.property("id");
        userId = resp.body.id;
      });
    });
});

Cypress.Commands.add("refreshSeassion", () => {
  cy.clearAllCookies().clearAllLocalStorage().clearAllSessionStorage().reload();
});

Cypress.Commands.add("selectOption", (selectId, optionId, forceBool) => {
  cy.getByTestId(selectId).select(optionId, { force: forceBool });
});
