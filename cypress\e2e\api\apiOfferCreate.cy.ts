import { faker } from "@faker-js/faker";

import { getUdApiUrl, getUdBeApiUrl } from "../../helper/helper";
import {
  colivingFlatFullRoomLandlord,
  colivingFlatFullRoomRealEstate,
  colivingFlatSharedRoomLandlord,
  colivingFlatSharedRoomRealEstate,
  colivingHouseFullRoomLandlord,
  colivingHouseFullRoomRealEstate,
  colivingHouseSharedRoomLandlord,
  colivingHouseSharedRoomRealEstate,
  rentFlatLandlord,
  rentFlatRealEstateBroker,
  rentHouseLandlord,
  rentHouseRealEstateBroker,
  sellFlatLandlord,
  sellFlatRealEstateBroker,
  sellHouseLandlord,
  sellHouseRealEstateBroker,
} from "../../constants/objects";

describe("Create Rent Offers", () => {
  let offerId: number;
  let data: any;

  const basicUser = {
    login: `${faker.word.noun().toLowerCase()}-${Math.round(
      Date.now() / 1000000,
    )}@ulovdomov.cz`,
    password: "testujeme",
  };
  let accessToken: string;

  before(() => {
    cy.apiRegister(basicUser);
  });

  it("Create Rent Flat Offer realEstateBroker", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "realEstateee",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: rentFlatRealEstateBroker,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Rent House Offer realEstateBroker", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: rentHouseRealEstateBroker,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Rent Flat Offer privateLandlord", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: rentFlatLandlord,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Rent House Offer privateLandlord", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: rentHouseLandlord,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Sell Flat Offer realEstateBroker", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "testing",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: sellFlatRealEstateBroker,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Sell House Offer realEstateBroker", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "testing",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: sellHouseRealEstateBroker,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Sell Flat Offer privateLandlord", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: sellFlatLandlord,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Sell House Offer privateLandlord", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: sellHouseLandlord,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Coliving Flat Full Room Offer realEstateBroker", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "testing",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: colivingFlatFullRoomRealEstate,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Coliving Flat Full Room Offer privateLandlord", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: colivingFlatFullRoomLandlord,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Coliving Flat Shared Room Offer realEstateBroker", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "testing",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: colivingFlatSharedRoomRealEstate,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Coliving Flat Shared Room Offer privateLandlord", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: colivingFlatSharedRoomLandlord,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Coliving House Full Room Offer realEstateBroker", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "testing",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: colivingHouseFullRoomRealEstate,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Coliving House Full Room Offer privateLandlord", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: colivingHouseFullRoomLandlord,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Coliving House Shared Room Offer realEstateBroker", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "testing",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: colivingHouseSharedRoomRealEstate,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Create Coliving House Shared Room Offer privateLandlord", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: colivingHouseSharedRoomLandlord,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });
});
