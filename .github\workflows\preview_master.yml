name: K8S Staging Main

on:
  push:
    branches:
      - "main"

concurrency:
  group: preview-main
  cancel-in-progress: true

env:
  PROJECT: ud-fe
  IMAGE_WITH_TAG: ud-fe-stage:master
  DOCKER_REGISTRY: registry.digitalocean.com/ulovdomov-be

jobs:
  build:
    timeout-minutes: 20
    runs-on: [self-hosted, runner-heavy]
    name: <PERSON><PERSON><PERSON><PERSON> prostředí - Master
    steps:
      - uses: actions/checkout@v3

      - name: Build
        run: |
          DOCKER_BUILDKIT=1 docker build . --file Dockerfile.k8s --target web --tag $IMAGE_WITH_TAG
          DOCKER_BUILDKIT=1 docker build . --file Dockerfile.k8s --target docs --tag $IMAGE_WITH_TAG-docs

      - name: Push image to registry
        run: |
          docker tag $IMAGE_WITH_TAG $DOCKER_REGISTRY/$IMAGE_WITH_TAG
          docker push $DOCKER_REGISTRY/$IMAGE_WITH_TAG

          docker tag $IMAGE_WITH_TAG-docs $DOCKER_REGISTRY/$IMAGE_WITH_TAG-docs
          docker push $DOCKER_REGISTRY/$IMAGE_WITH_TAG-docs

      - name: Clean runner
        run: docker system prune -a -f

  deploy:
    runs-on: [self-hosted, k8s-stage-fast]
    name: Testovací prostředí – spuštění (master)
    needs: build
    timeout-minutes: 2
    steps:
      - uses: actions/checkout@v3

      - name: Set up kubectl
        uses: matootie/dokube@v1.4.0
        with:
          personalAccessToken: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          clusterName: ulovdomov-dev
          namespace: ${{ env.PROJECT }}

      - uses: azure/setup-helm@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Install Helm Charts
        run: helm repo add enlabs https://enlabs-org.github.io/charts/

      - name: Update Helm release
        run: |
          helm upgrade ${{ env.PROJECT }}-master -i \
          enlabs/preview-app \
          -f .k8s/values-stage.yml \
          --set image=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_WITH_TAG }} \
          --set host=${{ env.PROJECT }}.k8stage.ulovdomov.cz \
          -n ${{ env.PROJECT }} \
          --create-namespace \
          --version 1.0.0

  cypress:
    needs: deploy
    uses: ./.github/workflows/cypress.yml
    name: QA - Cypress
    with:
      base_url: "https://ud-fe.k8stage.ulovdomov.cz"
