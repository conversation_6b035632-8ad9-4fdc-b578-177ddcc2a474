import { GetServerSideProps, NextPage } from "next";
import { captureException } from "@sentry/nextjs";
import {
  getStoryblokApi,
  StoryblokComponent,
  StoryData,
  useStoryblokState,
} from "@storyblok/react";

import Sales from "scenes/Sales";
import StoryblokTypeUserProvider from "contexts/StoryblokTypeUser";

const Index: NextPage<{
  story: StoryData | null;
  preview: boolean;
}> = ({ story: initialStory, preview }) => {
  const story = useStoryblokState(initialStory || undefined, {}, preview);
  const storyBlok = story ? (
    <StoryblokComponent blok={story.content} preview={preview} />
  ) : null;

  return (
    <StoryblokTypeUserProvider>
      <Sales blogComponents={storyBlok} />
    </StoryblokTypeUserProvider>
  );
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const storyblokApi = getStoryblokApi();
  const apiParams = {
    version: context.preview ? "draft" : undefined,
    token: context.preview
      ? process.env.NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN
      : undefined,
    resolve_relations: ["root_card.root"],
  };

  let resStory;
  try {
    resStory = await storyblokApi.get("cdn/stories/homepage", apiParams);

    return {
      props: {
        story: resStory?.data.story || null,
        preview: context.preview || false,
      },
    };
  } catch (err) {
    captureException(err);

    return {
      notFound: true,
      props: {
        story: null,
        preview: context.preview || false,
      },
    };
  }
};

export default Index;
