name: K8S Staging PR

on:
  pull_request:
    types: [opened, reopened, synchronize]

concurrency:
  group: preview-${{ github.ref }}
  cancel-in-progress: true

env:
  PROJECT: ud-fe
  IMAGE_WITH_TAG: ud-fe-stage:${{ github.event.number }}
  DOCKER_REGISTRY: registry.digitalocean.com/ulovdomov-be

jobs:
  build:
    timeout-minutes: 20
    runs-on: [self-hosted, runner-heavy]
    name: Testovací prostředí - PR
    steps:
      - uses: actions/checkout@v3

      - name: Print Sentry DSN values
        run: |
          echo "SENTRY_DSN: $SENTRY_DSN"
          echo "NEXT_PUBLIC_SENTRY_DSN: $NEXT_PUBLIC_SENTRY_DSN"

      - name: Build
        run: |
          DOCKER_BUILDKIT=1 docker build . --file Dockerfile.k8s --target web --tag $IMAGE_WITH_TAG
          DOCKER_BUILDKIT=1 docker build . --file Dockerfile.k8s --target docs --tag $IMAGE_WITH_TAG-docs

      - name: Push image to registry
        run: |
          docker tag $IMAGE_WITH_TAG $DOCKER_REGISTRY/$IMAGE_WITH_TAG
          docker push $DOCKER_REGISTRY/$IMAGE_WITH_TAG

          docker tag $IMAGE_WITH_TAG-docs $DOCKER_REGISTRY/$IMAGE_WITH_TAG-docs
          docker push $DOCKER_REGISTRY/$IMAGE_WITH_TAG-docs

      - name: Clean runner
        run: docker system prune -a -f

  deploy:
    runs-on: [self-hosted, k8s-stage-fast]
    name: Testovací prostředí – spuštění
    needs: build
    timeout-minutes: 2
    steps:
      - uses: actions/checkout@v3

      - name: Set up kubectl
        uses: matootie/dokube@v1.4.0
        with:
          personalAccessToken: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          clusterName: ulovdomov-dev
          namespace: ${{ env.PROJECT }}

      - uses: azure/setup-helm@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Install Helm Charts
        run: helm repo add enlabs https://enlabs-org.github.io/charts/

      - name: Update Helm release
        run: |
          helm upgrade ${{ env.PROJECT }}-${{ github.event.number }} -i \
          enlabs/preview-app \
          -f .k8s/values-stage.yml \
          --set image=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_WITH_TAG }} \
          --set host=${{ env.PROJECT }}-${{ github.event.number }}.k8stage.ulovdomov.cz \
          -n ${{ env.PROJECT }} \
          --create-namespace \
          --version 1.0.0

  deployment:
    name: PR Deployments
    runs-on: [self-hosted, k8s-stage-fast]
    timeout-minutes: 2
    needs: deploy
    steps:
      - uses: chrnorm/deployment-action@releases/v2
        name: Create GitHub deployment
        if: github.event.action == 'opened' || github.event.action == 'reopened' || github.event.action == 'synchronize'
        id: deployment
        with:
          token: "${{ github.token }}"
          environment-url: "https://${{ env.PROJECT }}-${{ github.event.number }}.k8stage.ulovdomov.cz"
          environment: "${{ env.PROJECT }}-${{ github.event.number }}"
          initial-status: "success"
          ref: "${{ github.event.pull_request.head.ref }}"

      - uses: chrnorm/deployment-action@releases/v2
        name: Create GitHub deployment
        if: github.event.action == 'opened' || github.event.action == 'reopened' || github.event.action == 'synchronize'
        id: deployment-docs
        with:
          token: "${{ github.token }}"
          environment-url: "https://docs-${{ env.PROJECT }}-${{ github.event.number }}.k8stage.ulovdomov.cz"
          environment: "${{ env.PROJECT }}-${{ github.event.number }}-docs"
          initial-status: "success"
          ref: "${{ github.event.pull_request.head.ref }}"
