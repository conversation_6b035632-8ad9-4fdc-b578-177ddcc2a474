import { useEffect, useState } from "react";
import { useRouter } from "next/router";

import InsertOfferWrapper from "components/InsertOfferWrapper";
import ContactForm from "scenes/InsertOffer/form/Contact";
import { useTracking } from "contexts/Tracking";
import { CustomAppPage } from "src/types/types";

const Contact: CustomAppPage = () => {
  const { trackEvent } = useTracking();
  const [tracked, setTracked] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (tracked) {
      return;
    }

    const abVariant = router.query.abVariant as string;

    if (abVariant === "skip_first_step") {
      trackEvent({
        "dl.eCat": "offer_create",
        "dl.eAct": "ab.participated",
        "dl.eLab": "skip_first_step",
      });

      setTracked(true);
    }
  }, [router.query]);

  return (
    <InsertOfferWrapper title="Kontakt u inzerátu">
      <ContactForm buttonNextText="Další" />
    </InsertOfferWrapper>
  );
};

export default Contact;
