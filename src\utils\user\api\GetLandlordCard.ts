import { parsePhoneStringToObject } from "helpers/phoneUtils";
import { BaseRestApiEndpoint, ERestApiMethod } from "utils/api";
import { ILandlord } from "../types";

type TInput = void;
type TOutput = ILandlord;

export default class GetLandlordCardApiEndpoint extends BaseRestApiEndpoint<
  TInput,
  TOutput
> {
  method = ERestApiMethod.GET;

  resolveUri(): string {
    return `/fe-api/user/landlord-card`;
  }

  mapResponseData(_status: number, data: any): TOutput {
    return {
      firstName: data.first_name,
      lastName: data.last_name,
      photoPath: data.photo_url,
      phone: parsePhoneStringToObject(data.contact_phone),
      landlordTypeId: data.user_landlord_type_id
        ? data.user_landlord_type_id
        : 0,
      companyName: data.company_name,
    };
  }
}
