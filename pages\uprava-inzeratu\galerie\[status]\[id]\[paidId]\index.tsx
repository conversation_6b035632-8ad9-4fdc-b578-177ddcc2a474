import { GetServerSideProps } from "next/types";

import InsertOfferWrapper from "components/InsertOfferWrapper";
import GalleryForm from "scenes/InsertOffer/form/Gallery";
import { CustomAppPage, EditOfferPaid } from "src/types/types";

const Gallery: CustomAppPage<EditOfferPaid> = (props) => (
  <InsertOfferWrapper title="Galerie nemovitosti" {...props}>
    <GalleryForm buttonNextText="Uložit a pokračovat" />
  </InsertOfferWrapper>
);

Gallery.hasHiddenFooter = true;

export default Gallery;

export const getServerSideProps: GetServerSideProps = async ({ query }) => ({
  props: {
    id: query.id as string,
    status: query.status as string,
    paidId: query.paidId as string,
  },
});
