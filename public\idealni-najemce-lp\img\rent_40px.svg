<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 40 40">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_31" data-name="Rectangle 31" width="40" height="40"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_313" data-name="Path 313" d="M4-226.316H50.144V-264H4Z" transform="translate(-4 264)" fill="#fff"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <path id="Path_316" data-name="Path 316" d="M124.625-159.4a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-123.782 161.075)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <path id="Path_315" data-name="Path 315" d="M4-226.316H50.144V-264H4Z" transform="translate(-4 264)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <path id="Path_319" data-name="Path 319" d="M145.438-159.4a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-144.595 161.075)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-7">
      <path id="Path_322" data-name="Path 322" d="M166.251-159.4a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-165.408 161.075)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-9">
      <path id="Path_325" data-name="Path 325" d="M187.064-159.4a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-186.221 161.075)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-11">
      <path id="Path_328" data-name="Path 328" d="M207.877-159.4a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-207.034 161.075)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-13">
      <path id="Path_331" data-name="Path 331" d="M124.625-140.891a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-123.782 142.565)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-15">
      <path id="Path_334" data-name="Path 334" d="M145.438-140.891a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-144.595 142.565)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-17">
      <path id="Path_337" data-name="Path 337" d="M166.251-140.891a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-165.408 142.565)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-19">
      <path id="Path_340" data-name="Path 340" d="M187.064-140.891a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-186.221 142.565)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-21">
      <path id="Path_343" data-name="Path 343" d="M207.877-140.891a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-207.034 142.565)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-23">
      <path id="Path_346" data-name="Path 346" d="M124.625-121.293a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837A.84.84,0,0,0,124.625-121.293Z" transform="translate(-123.782 122.967)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-25">
      <path id="Path_349" data-name="Path 349" d="M145.438-121.293a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-144.595 122.967)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-27">
      <path id="Path_352" data-name="Path 352" d="M166.251-121.293a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837A.84.84,0,0,0,166.251-121.293Z" transform="translate(-165.408 122.967)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-29">
      <path id="Path_355" data-name="Path 355" d="M187.064-121.293a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837A.84.84,0,0,0,187.064-121.293Z" transform="translate(-186.221 122.967)" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-31">
      <path id="Path_358" data-name="Path 358" d="M207.877-121.293a.84.84,0,0,0,.842-.837.84.84,0,0,0-.842-.837.84.84,0,0,0-.842.837.84.84,0,0,0,.842.837Z" transform="translate(-207.034 122.967)" fill="#212121"/>
    </clipPath>
  </defs>
  <g id="rent_40px" clip-path="url(#clip-path)">
    <g id="Group_360" data-name="Group 360" transform="translate(-4 266.316)">
      <path id="Path_413" data-name="Path 413" d="M1599.77,15.1V28.56h21.309V15.1Z" transform="translate(-1580 -266.316)" fill="#fff"/>
      <g id="Group_318" data-name="Group 318" transform="translate(4 -264)" clip-path="url(#clip-path-2)">
        <g id="Group_315" data-name="Group 315" transform="translate(7.033 11.477)">
          <path id="Path_310" data-name="Path 310" d="M5.074,7.7H1.1c-.121,0-.187-.047-.23-.284C.634,6.1.379,4.8.14,3.487c-.15-.82-.271-1.167.144-1.58a1.111,1.111,0,0,1,.9-.379c.635-.006,1.271,0,1.906,0,.1,0,.164-.045.235-.176C3.8.459,4.393,0,5.066,0s1.28.453,1.767,1.358c.066.123.133.171.225.17.684,0,1.368-.007,2.052,0a1.164,1.164,0,0,1,.859.734,2.262,2.262,0,0,1,0,1.519c-.236,1.237-.476,2.471-.706,3.712-.036.193-.1.209-.182.208H5.074Z" fill="#fff" stroke="#212121" stroke-width="1"/>
        </g>
        <g id="Group_316" data-name="Group 316" transform="translate(14.212 8.798)">
          <path id="Path_311" data-name="Path 311" d="M20.294,0h2.452V4.019H0V0Z" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_317" data-name="Group 317" transform="translate(14.212 12.817)">
          <path id="Path_312" data-name="Path 312" d="M0,0H22.746V13.4H6.952" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
      </g>
      <g id="Group_320" data-name="Group 320" transform="translate(22.424 -248.169)" clip-path="url(#clip-path-3)">
        <g id="Group_319" data-name="Group 319" transform="translate(-18.424 -15.831)" clip-path="url(#clip-path-4)">
          <path id="Path_314" data-name="Path 314" d="M118.782-166.075H122v3.213h-3.223Z" transform="translate(-101.127 181.137)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_322" data-name="Group 322" transform="translate(25.625 -248.169)" clip-path="url(#clip-path-5)">
        <g id="Group_321" data-name="Group 321" transform="translate(-21.625 -15.831)" clip-path="url(#clip-path-4)">
          <path id="Path_317" data-name="Path 317" d="M139.6-166.075h3.223v3.213H139.6Z" transform="translate(-118.739 181.137)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_324" data-name="Group 324" transform="translate(28.827 -248.169)" clip-path="url(#clip-path-7)">
        <g id="Group_323" data-name="Group 323" transform="translate(-24.827 -15.831)" clip-path="url(#clip-path-4)">
          <path id="Path_320" data-name="Path 320" d="M160.408-166.075h3.223v3.213h-3.223Z" transform="translate(-136.351 181.137)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_326" data-name="Group 326" transform="translate(32.028 -248.169)" clip-path="url(#clip-path-9)">
        <g id="Group_325" data-name="Group 325" transform="translate(-28.028 -15.831)" clip-path="url(#clip-path-4)">
          <path id="Path_323" data-name="Path 323" d="M181.221-166.075h3.223v3.213h-3.223Z" transform="translate(-153.962 181.137)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_328" data-name="Group 328" transform="translate(35.229 -248.169)" clip-path="url(#clip-path-11)">
        <g id="Group_327" data-name="Group 327" transform="translate(-31.229 -15.831)" clip-path="url(#clip-path-4)">
          <path id="Path_326" data-name="Path 326" d="M202.034-166.075h3.223v3.213h-3.223Z" transform="translate(-171.574 181.137)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_330" data-name="Group 330" transform="translate(22.424 -245.322)" clip-path="url(#clip-path-13)">
        <g id="Group_329" data-name="Group 329" transform="translate(-18.424 -18.678)" clip-path="url(#clip-path-4)">
          <path id="Path_329" data-name="Path 329" d="M118.782-147.565H122v3.213h-3.223Z" transform="translate(-101.127 165.475)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_332" data-name="Group 332" transform="translate(25.625 -245.322)" clip-path="url(#clip-path-15)">
        <g id="Group_331" data-name="Group 331" transform="translate(-21.625 -18.678)" clip-path="url(#clip-path-4)">
          <path id="Path_332" data-name="Path 332" d="M139.6-147.565h3.223v3.213H139.6Z" transform="translate(-118.739 165.475)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_334" data-name="Group 334" transform="translate(28.827 -245.322)" clip-path="url(#clip-path-17)">
        <g id="Group_333" data-name="Group 333" transform="translate(-24.827 -18.678)" clip-path="url(#clip-path-4)">
          <path id="Path_335" data-name="Path 335" d="M160.408-147.565h3.223v3.213h-3.223Z" transform="translate(-136.351 165.475)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_336" data-name="Group 336" transform="translate(32.028 -245.322)" clip-path="url(#clip-path-19)">
        <g id="Group_335" data-name="Group 335" transform="translate(-28.028 -18.678)" clip-path="url(#clip-path-4)">
          <path id="Path_338" data-name="Path 338" d="M181.221-147.565h3.223v3.213h-3.223Z" transform="translate(-153.962 165.475)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_338" data-name="Group 338" transform="translate(35.229 -245.322)" clip-path="url(#clip-path-21)">
        <g id="Group_337" data-name="Group 337" transform="translate(-31.229 -18.678)" clip-path="url(#clip-path-4)">
          <path id="Path_341" data-name="Path 341" d="M202.034-147.565h3.223v3.213h-3.223Z" transform="translate(-171.574 165.475)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_340" data-name="Group 340" transform="translate(22.424 -242.307)" clip-path="url(#clip-path-23)">
        <g id="Group_339" data-name="Group 339" transform="translate(-18.424 -21.693)" clip-path="url(#clip-path-4)">
          <path id="Path_344" data-name="Path 344" d="M118.782-127.967H122v3.213h-3.223Z" transform="translate(-101.127 148.891)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_342" data-name="Group 342" transform="translate(25.625 -242.307)" clip-path="url(#clip-path-25)">
        <g id="Group_341" data-name="Group 341" transform="translate(-21.625 -21.693)" clip-path="url(#clip-path-4)">
          <path id="Path_347" data-name="Path 347" d="M139.6-127.967h3.223v3.213H139.6Z" transform="translate(-118.739 148.891)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_344" data-name="Group 344" transform="translate(28.827 -242.307)" clip-path="url(#clip-path-27)">
        <g id="Group_343" data-name="Group 343" transform="translate(-24.827 -21.693)" clip-path="url(#clip-path-4)">
          <path id="Path_350" data-name="Path 350" d="M160.408-127.967h3.223v3.213h-3.223Z" transform="translate(-136.351 148.891)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_346" data-name="Group 346" transform="translate(32.028 -242.307)" clip-path="url(#clip-path-29)">
        <g id="Group_345" data-name="Group 345" transform="translate(-28.028 -21.693)" clip-path="url(#clip-path-4)">
          <path id="Path_353" data-name="Path 353" d="M181.221-127.967h3.223v3.213h-3.223Z" transform="translate(-153.962 148.891)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_348" data-name="Group 348" transform="translate(35.229 -242.307)" clip-path="url(#clip-path-31)">
        <g id="Group_347" data-name="Group 347" transform="translate(-31.229 -21.693)" clip-path="url(#clip-path-4)">
          <path id="Path_356" data-name="Path 356" d="M202.034-127.967h3.223v3.213h-3.223Z" transform="translate(-171.574 148.891)" fill="#212121"/>
        </g>
      </g>
      <g id="Group_359" data-name="Group 359" transform="translate(4 -264)" clip-path="url(#clip-path-2)">
        <g id="Group_349" data-name="Group 349" transform="translate(2.249 19.516)">
          <path id="Path_359" data-name="Path 359" d="M4.306,16.245A3.824,3.824,0,0,1,1.67,15.18,4.853,4.853,0,0,1,.4,13.313a5.869,5.869,0,0,1-.392-2A9.574,9.574,0,0,1,.114,9.622a11.862,11.862,0,0,1,.8-2.807,17.4,17.4,0,0,1,1.355-2.6A21.587,21.587,0,0,1,4.71,1.04c.3-.33.628-.64.948-.958a.3.3,0,0,1,.19-.076q4-.006,8-.006a.37.37,0,0,1,.269.139A22.639,22.639,0,0,1,16,2.2a20.761,20.761,0,0,1,1.886,2.756,15.015,15.015,0,0,1,1.338,3.035,11.322,11.322,0,0,1,.461,2.383,7.019,7.019,0,0,1-.072,1.894A4.922,4.922,0,0,1,17.3,15.734a3.473,3.473,0,0,1-1.834.513H9.855C8,16.242,6.155,16.236,4.306,16.245Z" fill="#fff" stroke="#212121" stroke-width="1"/>
        </g>
        <g id="Group_350" data-name="Group 350" transform="translate(8.346 19.516)">
          <path id="Path_360" data-name="Path 360" d="M1.267,0,0,5.358" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
        </g>
        <g id="Group_351" data-name="Group 351" transform="translate(10.494 19.516)">
          <path id="Path_361" data-name="Path 361" d="M0,0,.9,6.127" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
        </g>
        <g id="Group_352" data-name="Group 352" transform="translate(17.329 7.625)">
          <path id="Path_362" data-name="Path 362" d="M.548,3.182V0" transform="translate(-0.548)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
        </g>
        <g id="Group_353" data-name="Group 353" transform="translate(20.025 7.625)">
          <path id="Path_363" data-name="Path 363" d="M.548,3.182V0" transform="translate(-0.548)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
        </g>
        <g id="Group_354" data-name="Group 354" transform="translate(22.889 7.625)">
          <path id="Path_364" data-name="Path 364" d="M.548,3.182V0" transform="translate(-0.548)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
        </g>
        <g id="Group_355" data-name="Group 355" transform="translate(25.585 7.625)">
          <path id="Path_365" data-name="Path 365" d="M.548,3.182V0" transform="translate(-0.548)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
        </g>
        <g id="Group_356" data-name="Group 356" transform="translate(28.281 7.625)">
          <path id="Path_366" data-name="Path 366" d="M.548,3.182V0" transform="translate(-0.548)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
        </g>
        <g id="Group_357" data-name="Group 357" transform="translate(30.977 7.625)">
          <path id="Path_367" data-name="Path 367" d="M.548,3.182V0" transform="translate(-0.548)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
        </g>
        <g id="Group_358" data-name="Group 358" transform="translate(33.672 7.625)">
          <path id="Path_368" data-name="Path 368" d="M.548,3.182V0" transform="translate(-0.548)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
        </g>
      </g>
    </g>
  </g>
</svg>
