name: UlovDomov CI

on:
  push:
  pull_request:
    branches:
      - "*"
      - "!main"

jobs:
  integration:
    runs-on: "ubuntu-latest"
    name: "Integration"

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 16

      - name: Install
        run: yarn install

      - name: Lint
        run: yarn check:all

      - name: Test build
        run: yarn build:dev:no-static
