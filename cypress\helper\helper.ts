export function getUdBeApiUrl() {
  if (
    Cypress.config("baseUrl")?.includes("localhost") ||
    Cypress.config("baseUrl")?.includes("stage")
  ) {
    return "https://ud-be.k8stage.ulovdomov.cz/";
  } else {
    return "https://www.ulovdomov.cz/";
  }
}

export function getUdApiUrl() {
  if (
    Cypress.config("baseUrl")?.includes("localhost") ||
    Cypress.config("baseUrl")?.includes("stage")
  ) {
    return "https://ud-be-api.k8stage.ulovdomov.cz/v1";
  } else {
    return "https://ud.api.ulovdomov.cz/v1";
  }
}

export function json2array(json: any) {
  const result: any = [];
  const keys = Object.keys(json);
  keys.forEach(function (key) {
    result.push(json[key]);
  });

  return result;
}

export function uploadFile(file: any) {
  cy.fixture(file, "binary")
    .then(Cypress.Blob.binaryStringToBlob)
    .then((fileContent) => {
      cy.get("input#fileInput").attachFile({
        fileContent,
        encoding: "utf8",
        filePath: file,
        mimeType: "application/pdf",
        lastModified: new Date().getTime(),
      });
    });
}

export function exitModal(selector: string) {
  cy.wait(500);
  cy.getByTestId(selector).click({ force: true });
}
