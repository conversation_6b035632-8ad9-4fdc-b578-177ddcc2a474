import { fakerCS_CZ } from "@faker-js/faker";

import { urls } from "../constants/ulrs";
import * as rk from "../pages/rkForm";

const czechData = {
  agencyName: fakerCS_CZ.company.name(),
  software: "Importer",
  name: fakerCS_CZ.person.fullName(),
  email: fakerCS_CZ.internet.email(),
  phone: fakerCS_CZ.phone.number(),
};

const invalidData = {
  agencyName: "guh",
  software: "Importer",
  name: "<PERSON><PERSON>",
  email: "sus",
  phone: "*********",
};

describe("RK Form Test", () => {
  context("Desktop View", () => {
    beforeEach(() => {
      cy.visit(urls.REALITY_OFFICE);
      cy.intercept("POST", "**/fe-api/real-estate/new-contact-form").as(
        "contact-form",
      );
    });

    it("Check Elements", () => {
      rk.checkElements();
    });

    it("Check Links", () => {
      rk.checkLinks();
    });

    it("Fill RK Form Invalid Data", () => {
      rk.fillForm(invalidData);
      rk.submitFormButtonInvalidData();
    });

    it("Fill RK Form Valid Data", () => {
      rk.fillForm(czechData);
      rk.submitFormButton();
    });
  });

  context("Mobile View", () => {
    beforeEach(() => {
      cy.viewport(390, 844);
      cy.visit(urls.REALITY_OFFICE);
      cy.intercept("POST", "**/fe-api/real-estate/new-contact-form").as(
        "contact-form",
      );
    });

    it("Check Elements", () => {
      rk.checkElements();
    });

    it("Check Links", () => {
      rk.checkLinks();
    });

    it("Fill RK Form Invalid Data", () => {
      rk.fillForm(invalidData);
      rk.submitFormButtonInvalidData();
    });

    it("Fill RK Form Valid Data", () => {
      rk.fillForm(czechData);
      rk.submitFormButton();
    });
  });
});
