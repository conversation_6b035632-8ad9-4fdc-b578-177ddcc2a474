import { useState } from "react";

import InsertOfferWrapper from "components/InsertOfferWrapper";
import { Description as DescriptionPage } from "scenes/InsertOffer/form/Description";
import { CustomAppPage } from "src/types/types";
import { useAppSelector } from "hooks/reduxHooks";
import { PROPERTY_TYPE } from "@ud/config/prodeje/config";
import { DescriptionSubStep } from "src/types/insertType";

const Description: CustomAppPage = () => {
  const { typeProperty } = useAppSelector((e) => e.insertForm.data);
  const [subStep, setSubStep] = useState<DescriptionSubStep>("one");

  const getTitle = () => {
    switch (subStep) {
      case "one":
        return typeProperty === PROPERTY_TYPE.HOUSE.apiId
          ? "Základní údaje o domě"
          : "Základní údaje o bytě";
      case "two":
        return "Dodatečné údaje";
      case "three":
        return "Bližší popis nemovitosti";
    }
  };

  return (
    <InsertOfferWrapper title={getTitle()}>
      <DescriptionPage setSubStep={setSubStep} subStep={subStep} />
    </InsertOfferWrapper>
  );
};

Description.hasHiddenFooter = true;

export default Description;
