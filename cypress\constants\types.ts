export type dispositionType =
  | "1+kk"
  | "1+1"
  | "2+kk"
  | "2+1"
  | "3+kk"
  | "3+1"
  | "4+kk"
  | "4+1"
  | "Atypický"
  | "Dům"
  | "Spolubydlení";

export type dogNotification = "setImmediate" | "daily" | "weekly";

export type dogInput = {
  adress: string;
  adress2: string;
  disp: dispositionType;
  min: string;
  max: string;
  name: string;
  name2: string;
  newName: string;
  searchName: string;
  newDispo: dispositionType;
  newMin: string;
  newMax: string;
};

export type animalsType = "DOG" | "CAT" | "RODENT" | "BIRD" | "OTHER" | "NONE";

export const animalSet = {
  DOG: 1,
  CAT: 2,
  RODENT: 3,
  BIRD: 4,
  OTHER: 5,
  NONE: 6,
};
