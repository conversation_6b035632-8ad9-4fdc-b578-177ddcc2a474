import { faker } from "@faker-js/faker";

import { getUdApiUrl, getUdBeApiUrl } from "../../helper/helper";
import {
  landLordCard,
  rentFlatRealEstateBroker,
  rentFlatRealEstateBrokerEdited,
} from "../../constants/objects";

describe("Create Rent Offers", () => {
  let offerId: number;
  let data: any;

  const basicUser = {
    login: `${faker.word.noun().toLowerCase()}-${Math.round(
      Date.now() / 1000000,
    )}@ulovdomov.cz`,
    password: "testujeme",
  };
  let accessToken: string;

  before(() => {
    cy.apiRegister(basicUser);
  });

  it("Create Rent Flat Offer realEstateBroker", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: landLordCard.first_name,
            last_name: landLordCard.last_name,
            contact_phone: landLordCard.contact_phone,
            user_landlord_type_id: landLordCard.user_landlord_type_id,
            company_name: landLordCard.company_name,
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: rentFlatRealEstateBroker,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Get Detail Raw, Landlord Card And OfferDetail", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "GET",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
        })
          .then((resp) => {
            expect(resp.status).equal(200);
            expect(resp.body).has.property("photo_url");
            expect(resp.body).has.property("first_name");
            expect(resp.body).has.property("last_name");
            expect(resp.body).has.property("contact_phone");
            expect(resp.body).has.property("contact_email");
            expect(resp.body).has.property("user_landlord_type_id");
            expect(resp.body).has.property("company_name");
            expect(resp.body).has.property("invoice");
          })
          .then((resp) => {
            expect(resp.body.photo_url).to.include("default_avatar.png");
            expect(resp.body.first_name).to.eq(landLordCard.first_name);
            expect(resp.body.last_name).to.eq(landLordCard.last_name);
            expect(resp.body.contact_phone).to.eq(landLordCard.contact_phone);
            expect(resp.body.contact_email).to.eq(basicUser.login);
            expect(resp.body.user_landlord_type_id).to.eq(
              landLordCard.user_landlord_type_id,
            );
            expect(resp.body.company_name).to.eq(landLordCard.company_name);
          });

        cy.request({
          // raw Detail
          method: "GET",
          url: `${getUdApiUrl()}/offer/detail-raw?offerId=${offerId}`,
          headers: { authorization: `Bearer ${accessToken}` },
        })
          .then((resp) => {
            expect(resp.status).to.eq(200);
            expect(resp.body).has.property("data");
            expect(resp.body.data).has.property("offerId");
            expect(resp.body.data).has.property("emailAddress");
            expect(resp.body.data).has.property("phone");
            expect(resp.body.data).has.property("disposition");
            expect(resp.body.data).has.property("offerType");
            expect(resp.body.data).has.property("propertyType");
            expect(resp.body.data).has.property("colivingType");
            expect(resp.body.data).has.property("price");
            expect(resp.body.data).has.property("depositPrice");
            expect(resp.body.data).has.property("monthlyFeesPrice");
            expect(resp.body.data).has.property("priceUnit");
            expect(resp.body.data).has.property("priceNote");
            expect(resp.body.data).has.property("availability");
            expect(resp.body.data).has.property("firstTourDate");
            expect(resp.body.data).has.property("firstTourDateTo");
            expect(resp.body.data).has.property("furnishing");
            expect(resp.body.data).has.property("usableArea");
            expect(resp.body.data).has.property("estateArea");
            expect(resp.body.data).has.property("floorCount");
            expect(resp.body.data).has.property("roomCount");
            expect(resp.body.data).has.property("floorLevel");
            expect(resp.body.data).has.property("conveniences");
            expect(resp.body.data).has.property("houseConveniences");
            expect(resp.body.data).has.property("ownership");
            expect(resp.body.data).has.property("buildingType");
            expect(resp.body.data).has.property("buildingCondition");
            expect(resp.body.data).has.property("material");
            expect(resp.body.data).has.property("flatType");
            expect(resp.body.data).has.property("objectType");
            expect(resp.body.data).has.property("locationType");
            expect(resp.body.data).has.property("surroundingType");
            expect(resp.body.data).has.property("protectedArea");
            expect(resp.body.data).has.property("energyEfficiencyRating");
            expect(resp.body.data).has.property("typeInsert");
            expect(resp.body.data).has.property("description");
            expect(resp.body.data).has.property("videoTourUrl");
            expect(resp.body.data).has.property("matterportUrl");
            expect(resp.body.data).has.property("typePriceInsert");
            expect(resp.body.data).has.property("priceCommission");
            expect(resp.body.data).has.property("builtUpArea");
            expect(resp.body.data).has.property("specificDate");
            expect(resp.body.data).has.property("village");
            expect(resp.body.data).has.property("street");
            expect(resp.body.data).has.property("streetNumber");
          })
          .then((resp) => {
            expect(resp.body.data.offerId).to.eq(offerId);
            expect(resp.body.data.buildingType).to.eq(
              rentFlatRealEstateBroker.buildingType,
            );
            expect(resp.body.data.availability).to.eq(
              rentFlatRealEstateBroker.availability,
            );
            expect(resp.body.data.buildingCondition).to.eq(
              rentFlatRealEstateBroker.buildingCondition,
            );
            expect(resp.body.data.conveniences).to.eql(
              rentFlatRealEstateBroker.conveniences.reverse(),
            );
            expect(resp.body.data.description).to.eq(
              rentFlatRealEstateBroker.description,
            );
            expect(resp.body.data.disposition).to.eq(
              rentFlatRealEstateBroker.disposition,
            );
            expect(resp.body.data.energyEfficiencyRating).to.eq(
              rentFlatRealEstateBroker.energyEfficiencyRating,
            );
            expect(resp.body.data.floorLevel).to.eq(
              rentFlatRealEstateBroker.floorLevel,
            );
            expect(resp.body.data.furnishing).to.eq(
              rentFlatRealEstateBroker.furnishing,
            );
            expect(resp.body.data.usableArea).to.eq(
              rentFlatRealEstateBroker.usableArea,
            );
            expect(resp.body.data.monthlyFeesPrice).to.eq(
              rentFlatRealEstateBroker.monthlyFeesPrice,
            );
            expect(resp.body.data.price).to.eq(rentFlatRealEstateBroker.price);
            expect(resp.body.data.streetNumber.id).to.eq(
              rentFlatRealEstateBroker.streetNumber,
            );
            expect(resp.body.data.flatType).to.eq(
              rentFlatRealEstateBroker.flatType,
            );
            expect(resp.body.data.houseConveniences).to.eql(
              rentFlatRealEstateBroker.houseConveniences,
            );
            expect(resp.body.data.locationType).to.eq(
              rentFlatRealEstateBroker.locationType,
            );
            expect(resp.body.data.material).to.eq(
              rentFlatRealEstateBroker.material,
            );
            expect(resp.body.data.offerType).to.eq(
              rentFlatRealEstateBroker.offerType,
            );
            expect(resp.body.data.priceCommission).to.eq(
              rentFlatRealEstateBroker.priceCommission,
            );
            expect(resp.body.data.surroundingType).to.eql(
              rentFlatRealEstateBroker.surroundingType,
            );
            expect(resp.body.data.floorCount).to.eq(
              rentFlatRealEstateBroker.floorCount,
            );
            expect(resp.body.data.priceNote).to.eq(
              rentFlatRealEstateBroker.priceNote,
            );
            expect(resp.body.data.propertyType).to.eq(
              rentFlatRealEstateBroker.propertyType,
            );
            expect(resp.body.data.typeInsert).to.eq(
              rentFlatRealEstateBroker.typeInsert,
            );
            expect(resp.body.data.depositPrice).to.eq(
              rentFlatRealEstateBroker.depositPrice,
            );
          })
          .then(() => {
            // edit Offer
            cy.request({
              method: "POST",
              url: `${getUdApiUrl()}/offer/create`,
              headers: { authorization: `Bearer ${accessToken}` },
              body: rentFlatRealEstateBrokerEdited,
            })
              .then((resp) => {
                expect(resp.status).eq(200);
                expect(resp.body.data).has.property("offerId");
                offerId = resp.body.data.offerId;
              })
              .then(() => {
                // get Detail
                cy.request({
                  method: "GET",
                  url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
                  headers: { authorization: `Bearer ${accessToken}` },
                }).then((resp) => {
                  expect(resp.status).eq(200);
                  expect(resp.body.data.id).to.eq(offerId);
                  expect(
                    resp.body.data.parameters.buildingType.options[0].id,
                  ).to.eq(rentFlatRealEstateBrokerEdited.buildingType);
                  expect(
                    resp.body.data.parameters.buildingCondition.options[0].id,
                  ).to.eq(rentFlatRealEstateBrokerEdited.buildingCondition);
                  expect(resp.body.data.description).to.eq(
                    rentFlatRealEstateBrokerEdited.description,
                  );
                  expect(
                    resp.body.data.parameters.disposition.options[0].id,
                  ).to.eq(rentFlatRealEstateBrokerEdited.disposition);
                  expect(
                    resp.body.data.parameters.energyEfficiencyRating.options[0]
                      .id,
                  ).to.eq(
                    rentFlatRealEstateBrokerEdited.energyEfficiencyRating,
                  );
                  expect(
                    resp.body.data.parameters.furnished.options[0].id,
                  ).to.eq(rentFlatRealEstateBrokerEdited.furnishing);
                  expect(resp.body.data.parameters.usableArea.value).to.include(
                    rentFlatRealEstateBrokerEdited.usableArea,
                  );
                  expect(resp.body.data.monthlyFeePrice.value).to.eq(
                    rentFlatRealEstateBrokerEdited.monthlyFeesPrice,
                  );
                  expect(resp.body.data.rentalPrice.value).to.eq(
                    rentFlatRealEstateBrokerEdited.price,
                  );
                  expect(
                    resp.body.data.parameters.flatType.options[0].id,
                  ).to.eq(rentFlatRealEstateBrokerEdited.flatType);
                  expect(
                    resp.body.data.parameters.material.options[0].id,
                  ).to.eq(rentFlatRealEstateBrokerEdited.material);
                  expect(resp.body.data.offerTypeId).to.eq(
                    rentFlatRealEstateBrokerEdited.offerType,
                  );
                  expect(
                    resp.body.data.parameters.surroundingType.options[0].id,
                  ).to.eq(
                    rentFlatRealEstateBrokerEdited.surroundingType.toString(),
                  );
                  expect(resp.body.data.parameters.floors.value).to.eq(
                    rentFlatRealEstateBrokerEdited.floorCount,
                  );
                  expect(resp.body.data.priceNote).to.eq(
                    rentFlatRealEstateBrokerEdited.priceNote,
                  );
                  expect(
                    resp.body.data.parameters.propertyType.options[0].id,
                  ).to.eq(rentFlatRealEstateBrokerEdited.propertyType);
                  expect(resp.body.data.depositPrice.value).to.eq(
                    rentFlatRealEstateBrokerEdited.depositPrice,
                  );
                });
              });
          });
      });
  });
});
