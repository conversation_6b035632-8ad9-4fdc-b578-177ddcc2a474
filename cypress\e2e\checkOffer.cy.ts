import { faker } from "@faker-js/faker";

import * as offer from "../pages/offer";
import { waitForPageLoaded } from "../pages/core";
import { urls } from "../constants/ulrs";
import * as login from "../pages/login";

const password = "ulovdomov";
const discountCode = "theonecode";
const inHousing = "signUpModal.userType.inHousing";

const desktopBasicUser = {
  login: `${faker.word.noun().toLowerCase()}-${Math.round(
    Date.now() / 1000000,
  )}@ulovdomov.cz`,
  password: "testujeme",
};

const mobileBasicUser = {
  login: `${faker.word.noun().toLowerCase()}-${Math.round(
    Date.now() / 1000000,
  )}@ulovdomov.cz`,
  password: "testujeme",
};

const desktopTxt = {
  area: faker.number.int({ min: 1, max: 300 }),
  town: "Praha",
  street: "Italská", // sa spravi vlastny list aby to nebolo vkuse len toto
  price: `${faker.number.int({ min: 8, max: 50 })} ${faker.number.int({
    min: 100,
    max: 999,
  })}`, // cena najmu
  fees: `${faker.number.int({ min: 1, max: 7 })} ${faker.number.int({
    min: 100,
    max: 999,
  })}`, // mesicni poplatky
  deposit: `${faker.number.int({ min: 10, max: 100 })} ${faker.number.int({
    min: 100,
    max: 999,
  })}`, // jistota
  dateDay: faker.number.int({ min: 1, max: 29 }),
  dateYear: new Date().getFullYear() + 1,
  description: faker.lorem.paragraph(),
  fakerFirstName: faker.person.firstName(),
  fakerLastName: faker.person.lastName(),
  fakerEmail: faker.internet.email(),
};

const mobileTxt = {
  area: faker.number.int({ min: 1, max: 300 }),
  town: "Praha",
  street: "Italská", // sa spravi vlastny list aby to nebolo vkuse len toto
  price: `${faker.number.int({ min: 8, max: 50 })} ${faker.number.int({
    min: 100,
    max: 999,
  })}`, // cena najmu
  fees: `${faker.number.int({ min: 1, max: 7 })} ${faker.number.int({
    min: 100,
    max: 999,
  })}`, // mesicni poplatky
  deposit: `${faker.number.int({ min: 10, max: 100 })} ${faker.number.int({
    min: 100,
    max: 999,
  })}`, // jistota
  dateDay: faker.number.int({ min: 1, max: 29 }),
  dateYear: new Date().getFullYear() + 1,
  description: faker.lorem.paragraph(),
  fakerFirstName: faker.person.firstName(),
  fakerLastName: faker.person.lastName(),
  fakerEmail: faker.internet.email(),
};

let userLogin: any;
let txt: any;

describe("Offers Test", () => {
  context("Desktop View", () => {
    beforeEach(() => {
      userLogin = desktopBasicUser;
      txt = desktopTxt;
      waitForPageLoaded();
      cy.intercept("POST", "**/fe-api/user/email-exists").as("email-exist");
      cy.intercept("POST", "**/fe-api/offer").as("offerId");
      cy.intercept("POST", "**/api/v1/user").as("email-reg");
      cy.intercept("GET", "**/fe-api/my-offers").as("myOffers");
      cy.intercept("GET", `**/${txt.fakerEmail}`).as("login-mail");
    });

    it("TC100 | Chech Offer No Data", () => {
      cy.visit(urls.ADD_ADV);
      offer.offerFormDispoElements();
      offer.offerFormAreaElements();
      offer.offerFormFloorElements();
      offer.offerFormRentalElements();
      offer.offerFormFurnishingLabelsElements();
      offer.offerFormFurnishingOptionElements();
      offer.offerFormNameElements();
      offer.clickDateFrom();
      offer.clickSubmitButtonNoData();
    });

    it("TC101 | Check Offer Bad Data Street & Number", () => {
      cy.visit(urls.ADD_ADV);
      offer.fillBadData(txt);
      offer.selectData();
      offer.clickDateFrom();
      offer.clickSubmitButtonBadData();
    });

    it("TC102 | Check Offer Valid Data", () => {
      cy.visit(urls.ADD_ADV);
      offer.fillValidDataCzechLandlordImmediately(txt);
      offer.selectData();
      offer.clickSubmitButton(password, txt);
      offer.clickUlovdomovOffer();
      offer.clickVipListing();
      offer.clickAndFillDiscountCode(discountCode);
      offer.submitPayment();
      offer.checkOffersCzechPrivateLandlordImmedately(txt);
    });

    it("TC103 | Check Offer As Anonym", () => {
      offer.checkNonEditedOffer(txt);
    });

    it("TC104 | Check Offer As Different Logged User", () => {
      cy.visit(urls.HOME); // prihlasenie z addOffer sa chova kompletne inak
      login.goToLoginModal();
      login.fillLoginMail(userLogin.login);
      login.continueToPassword();
      login.fillRegPassword(userLogin.password);
      login.clickRegRadioButton(inHousing);
      login.submitRegistration(userLogin.login);
      offer.checkNonEditedOffer(txt);
    });

    it("TC105 | Edit Offer & Check Edited Offer", () => {
      cy.visit(urls.HOME);
      login.goToLoginModal();
      login.fillLoginMail(txt.fakerEmail);
      login.continueToPassword();
      offer.offerPassword(password);
      login.submitLogin();
      offer.visitOffer();
      offer.editCzechOfferFromDetail(txt);
      offer.checkOffersEditedCzech(txt);
      offer.editCzechOfferFromOffers();
    });

    it("TC106 | Check Edited Offer As Anonym", () => {
      offer.checkOffer(txt);
    });

    it("TC107 | Check Edited Offer As Different Logged User", () => {
      cy.visit(urls.HOME);
      login.goToLoginModal();
      login.fillLoginMail(userLogin.login);
      login.continueToPassword();
      offer.offerPassword(userLogin.password);
      login.submitLogin();
      offer.checkOffer(txt);
    });

    it("TC108 | Deactivate & Check Deactivated Offer", () => {
      cy.visit(urls.HOME);
      login.goToLoginModal();
      login.fillLoginMail(txt.fakerEmail);
      login.continueToPassword();
      offer.offerPassword(password);
      login.submitLogin();
      offer.deActivateOffer();
      offer.checkDeactivatedOffer(txt);
    });

    it("TC109 | Check Deactivated Offer As Anonym", () => {
      offer.checkDeactivatedOfferAsOther(txt);
    });

    it("TC110 | Check Deactivated Offer As Different Logged User", () => {
      cy.visit(urls.HOME);
      login.goToLoginModal();
      login.fillLoginMail(userLogin.login);
      login.continueToPassword();
      offer.offerPassword(userLogin.password);
      login.submitLogin();
      offer.checkDeactivatedOfferAsOther(txt);
    });

    it("TC111 | Delete Offer & Check Deleted Offer", () => {
      cy.visit(urls.HOME);
      login.goToLoginModal();
      login.fillLoginMail(txt.fakerEmail);
      login.continueToPassword();
      offer.offerPassword(password);
      login.submitLogin();
      offer.deleteOffer();
      offer.checkOfferError();
    });

    it("TC112 | Check Deleted Offer As Anonym", () => {
      offer.checkOfferError();
    });

    it("TC113 | Check Deleted Offer As Different Logged User", () => {
      cy.visit(urls.HOME);
      login.goToLoginModal();
      login.fillLoginMail(userLogin.login);
      login.continueToPassword();
      offer.offerPassword(userLogin.password);
      login.submitLogin();
      offer.checkOfferError();
    });
  });

  context("Mobile View", () => {
    beforeEach(() => {
      userLogin = mobileBasicUser;
      txt = mobileTxt;
      cy.viewport(390, 844);
      waitForPageLoaded();
      cy.intercept("POST", "**/fe-api/user/email-exists").as("email-exist");
      cy.intercept("POST", "**/fe-api/offer").as("offerId");
      cy.intercept("POST", "**/api/v1/user").as("email-reg");
      cy.intercept("GET", "**/fe-api/my-offers").as("myOffers");
      cy.intercept("GET", `**/${txt.fakerEmail}`).as("login-mail");
    });

    it("TC100 | Chech Offer No Data", () => {
      cy.visit(urls.ADD_ADV);
      offer.offerFormDispoElements();
      offer.offerFormAreaElements();
      offer.offerFormFloorElements();
      offer.offerFormRentalElements();
      offer.offerFormFurnishingLabelsElements();
      offer.offerFormFurnishingOptionElements();
      offer.offerFormNameElements();
      offer.clickDateFrom();
      offer.clickSubmitButtonNoData();
    });

    it("TC101 | Check Offer Bad Data Street & Number", () => {
      cy.visit(urls.ADD_ADV);
      offer.fillBadData(txt);
      offer.selectData();
      offer.clickDateFrom();
      offer.clickSubmitButtonBadData();
    });

    it("TC102 | Check Offer Valid Data", () => {
      cy.visit(urls.ADD_ADV);
      offer.fillValidDataCzechLandlordImmediately(txt);
      offer.selectData();
      offer.clickSubmitButton(password, txt);
      offer.clickUlovdomovOffer();
      offer.clickVipListing();
      offer.clickAndFillDiscountCode(discountCode);
      offer.submitPayment();
      offer.checkOffersCzechPrivateLandlordImmedately(txt);
    });

    it("TC103 | Check Offer As Anonym", () => {
      offer.checkNonEditedOffer(txt);
    });

    it("TC104 | Check Offer As Different Logged User", () => {
      cy.visit(urls.HOME); // prihlasenie z addOffer sa chova kompletne inak
      login.goToLoginModalMobile();
      login.fillLoginMail(userLogin.login);
      login.continueToPassword();
      login.fillRegPassword(userLogin.password);
      login.clickRegRadioButton(inHousing);
      login.submitRegistrationMobile();
      offer.checkNonEditedOffer(txt);
    });

    it("TC105 | Edit Offer & Check Edited Offer", () => {
      cy.visit(urls.HOME);
      login.goToLoginModalMobile();
      login.fillLoginMail(txt.fakerEmail);
      login.continueToPassword();
      offer.offerPassword(password);
      login.clickRegRadioButton(inHousing);
      login.submitLoginMobile();
      offer.visitOffer();
      offer.editCzechOfferFromDetail(txt);
      offer.checkOffersEditedCzech(txt);
      offer.editCzechOfferFromOffers();
    });

    it("TC106 | Check Edited Offer As Anonym", () => {
      offer.checkOffer(txt);
    });

    it("TC107 | Check Edited Offer As Different Logged User", () => {
      cy.visit(urls.HOME);
      login.goToLoginModalMobile();
      login.fillLoginMail(userLogin.login);
      login.continueToPassword();
      offer.offerPassword(userLogin.password);
      login.submitLoginMobile();
      offer.checkOffer(txt);
    });

    it("TC108 | Deactivate & Check Deactivated Offer", () => {
      cy.visit(urls.HOME);
      login.goToLoginModalMobile();
      login.fillLoginMail(txt.fakerEmail);
      login.continueToPassword();
      offer.offerPassword(password);
      login.submitLoginMobile();
      offer.deActivateOffer();
      offer.checkDeactivatedOffer(txt);
    });

    it("TC109 | Check Deactivated Offer As Anonym", () => {
      offer.checkDeactivatedOfferAsOther(txt);
    });

    it("TC110 | Check Deactivated Offer As Different Logged User", () => {
      cy.visit(urls.HOME);
      login.goToLoginModalMobile();
      login.fillLoginMail(userLogin.login);
      login.continueToPassword();
      offer.offerPassword(userLogin.password);
      login.submitLoginMobile();
      offer.checkDeactivatedOfferAsOther(txt);
    });

    it("TC111 | Delete Offer & Check Deleted Offer", () => {
      cy.visit(urls.HOME);
      login.goToLoginModalMobile();
      login.fillLoginMail(txt.fakerEmail);
      login.continueToPassword();
      offer.offerPassword(password);
      login.submitLoginMobile();
      offer.deleteOffer();
      offer.checkOfferError();
    });

    it("TC112 | Check Deleted Offer As Anonym", () => {
      offer.checkOfferError();
    });

    it("TC113 | Check Deleted Offer As Different Logged User", () => {
      cy.visit(urls.HOME);
      login.goToLoginModalMobile();
      login.fillLoginMail(userLogin.login);
      login.continueToPassword();
      offer.offerPassword(userLogin.password);
      login.submitLoginMobile();
      offer.checkOfferError();
    });
  });
});
