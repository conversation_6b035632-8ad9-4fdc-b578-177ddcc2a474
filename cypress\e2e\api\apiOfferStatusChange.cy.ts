import { faker } from "@faker-js/faker";

import { getUdApiUrl, getUdBeApiUrl } from "../../helper/helper";
import { rentFlatRealEstateBroker } from "../../constants/objects";

describe("Create Rent Offers", () => {
  let offerId: number;
  let data: any;

  const basicUser = {
    login: `${faker.word.noun().toLowerCase()}-${Math.round(
      Date.now() / 1000000,
    )}@ulovdomov.cz`,
    password: "testujeme",
  };
  let accessToken: string;

  before(() => {
    cy.apiRegister(basicUser);
  });

  it("Create Rent Flat Offer realEstateBroker", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdBeApiUrl()}fe-api/user/landlord-card`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            first_name: "Tester",
            last_name: "Testerovic",
            contact_phone: "+420777777777",
            user_landlord_type_id: 1,
            company_name: "realEstateee",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      })
      .then(() => {
        cy.request({
          method: "POST",
          url: `${getUdApiUrl()}/offer/create`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: rentFlatRealEstateBroker,
        })
          .then((resp) => {
            expect(resp.status).eq(200);
            expect(resp.body.data).has.property("offerId");
            offerId = resp.body.data.offerId;
          })
          .then(() => {
            cy.request({
              method: "GET",
              url: `${getUdApiUrl()}/offer/detail?offerId=${offerId}`,
              headers: { authorization: `Bearer ${accessToken}` },
            }).then((resp) => {
              expect(resp.status).eq(200);
              data = resp.body.data;
              expect(data).has.property("id");
              expect(data).has.property("offerTypeId");
              expect(data).has.property("title");
              expect(data).has.property("description");
              expect(data).has.property("street");
              expect(data.street).has.property("id");
              expect(data.street).has.property("name");
            });
          });
      });
  });

  it("Change Status Of Created Offer - Activate", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "PUT",
          url: `${getUdApiUrl()}/offer/status-change`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            offerId,
            status: "activate",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      });
  });

  it("Change Status Of Created Offer - Deactivate", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "PUT",
          url: `${getUdApiUrl()}/offer/status-change`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            offerId,
            status: "deactivate",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      });
  });

  it("Change Status Of Created Offer - Delete", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
      })
      .then(() => {
        cy.request({
          method: "PUT",
          url: `${getUdApiUrl()}/offer/status-change`,
          headers: { authorization: `Bearer ${accessToken}` },
          body: {
            offerId,
            status: "delete",
          },
        }).then((resp) => {
          expect(resp.status).equal(200);
        });
      });
  });
});
