<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 40 40">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_38" data-name="Rectangle 38" width="40" height="40"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_47" data-name="Path 47" d="M0-212.333H40V-245H0Z" transform="translate(0 245)" fill="#fff"/>
    </clipPath>
  </defs>
  <g id="signature_hover_red_40px" clip-path="url(#clip-path)">
    <g id="Group_50" data-name="Group 50" transform="translate(0 252.333)">
      <g id="Group_49" data-name="Group 49" transform="translate(0 -245)" clip-path="url(#clip-path-2)">
        <g id="Group_32" data-name="Group 32" transform="translate(0.533 29.51)">
          <path id="Path_30" data-name="Path 30" d="M0,0H38.933V2.623H0Z" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_33" data-name="Group 33" transform="translate(16.788 29.51)">
          <path id="Path_31" data-name="Path 31" d="M0,0H6.554V.918H0Z" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_34" data-name="Group 34" transform="translate(3.679 8.919)">
          <path id="Path_32" data-name="Path 32" d="M25.824,0h6.817V20.592H0" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_35" data-name="Group 35" transform="translate(36.321 29.51) rotate(180)">
          <path id="Path_33" data-name="Path 33" d="M25.824,20.592h6.817V0H0" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_36" data-name="Group 36" transform="translate(5.384 10.755)">
          <path id="Path_34" data-name="Path 34" d="M24.12,0h5.112V18.755H0" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_37" data-name="Group 37" transform="translate(34.616 29.51) rotate(180)">
          <path id="Path_35" data-name="Path 35" d="M24.12,18.755h5.112V0H0" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_38" data-name="Group 38" transform="translate(29.504 25.444) rotate(180)">
          <path id="Path_36" data-name="Path 36" d="M0,3.541V24.92H18.877V0H2.329" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_39" data-name="Group 39" transform="translate(15.477 20.526)">
          <path id="Path_37" data-name="Path 37" d="M0,1.334l3.6-.022" transform="translate(0 -1.312)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_40" data-name="Group 40" transform="translate(15.43 8.676)">
          <path id="Path_38" data-name="Path 38" d="M0,.492H9.329" transform="translate(0 -0.492)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_41" data-name="Group 41" transform="translate(15.43 10.512)">
          <path id="Path_39" data-name="Path 39" d="M0,.492H9.329" transform="translate(0 -0.492)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_42" data-name="Group 42" transform="translate(15.43 12.217)">
          <path id="Path_40" data-name="Path 40" d="M0,.492H9.329" transform="translate(0 -0.492)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_48" data-name="Group 48" transform="translate(15.43 6.682)">
          <path id="Path_46" data-name="Path 46" d="M0,.492H9.329" transform="translate(0 -0.492)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
      </g>
    </g>
    <g id="Group_50-2" data-name="Group 50" transform="translate(0 252.333)">
      <g id="Group_32-2" data-name="Group 32" transform="translate(0.533 -215.49)">
        <path id="Path_30-2" data-name="Path 30" d="M0,0H38.933V2.623H0Z" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_33-2" data-name="Group 33" transform="translate(16.788 -215.49)">
        <path id="Path_31-2" data-name="Path 31" d="M0,0H6.554V.918H0Z" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_34-2" data-name="Group 34" transform="translate(3.679 -236.081)">
        <path id="Path_32-2" data-name="Path 32" d="M25.824,0h6.817V20.592H0" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_35-2" data-name="Group 35" transform="translate(36.321 -215.49) rotate(180)">
        <path id="Path_33-2" data-name="Path 33" d="M25.824,20.592h6.817V0H0" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_36-2" data-name="Group 36" transform="translate(5.384 -234.245)">
        <path id="Path_34-2" data-name="Path 34" d="M24.12,0h5.112V18.755H0" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_37-2" data-name="Group 37" transform="translate(34.616 -215.49) rotate(180)">
        <path id="Path_35-2" data-name="Path 35" d="M24.12,18.755h5.112V0H0" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_38-2" data-name="Group 38" transform="translate(29.504 -219.556) rotate(180)">
        <path id="Path_36-2" data-name="Path 36" d="M0,3.541V24.92H18.877V0H2.329" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_39-2" data-name="Group 39" transform="translate(15.477 -224.474)">
        <path id="Path_37-2" data-name="Path 37" d="M0,1.334l3.6-.022" transform="translate(0 -1.312)" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_40-2" data-name="Group 40" transform="translate(15.43 -236.324)">
        <path id="Path_38-2" data-name="Path 38" d="M0,.492H9.329" transform="translate(0 -0.492)" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_41-2" data-name="Group 41" transform="translate(15.43 -234.488)">
        <path id="Path_39-2" data-name="Path 39" d="M0,.492H9.329" transform="translate(0 -0.492)" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_42-2" data-name="Group 42" transform="translate(15.43 -232.783)">
        <path id="Path_40-2" data-name="Path 40" d="M0,.492H9.329" transform="translate(0 -0.492)" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_43" data-name="Group 43" transform="matrix(0.643, -0.766, 0.766, 0.643, 20, -223.132)">
        <path id="Path_41" data-name="Path 41" d="M1.442,9.681Q1.443,6.19,0,5.054.843,3.922,3.616,0L7.225,5.052Q5.786,6.491,5.785,9.68" fill="#e74c4c" stroke="#e74c4c" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_44" data-name="Group 44" transform="matrix(0.643, -0.766, 0.766, 0.643, 28.343, -218.015)">
        <path id="Path_42" data-name="Path 42" d="M0,0H4.336V3.9H0Z" fill="#e74c4c" stroke="#e74c4c" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_45" data-name="Group 45" transform="matrix(0.643, -0.766, 0.766, 0.643, 24.937, -222.312)">
        <path id="Path_43" data-name="Path 43" d="M1.011,2.023A1.011,1.011,0,1,0,0,1.012,1.011,1.011,0,0,0,1.011,2.023Z" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_46" data-name="Group 46" transform="translate(22.901 -225.415)">
        <path id="Path_44" data-name="Path 44" d="M2.7,2.267,0,0" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_47" data-name="Group 47" transform="translate(15.477 -227.425)">
        <path id="Path_45" data-name="Path 45" d="M.767,0Q-.4,1.011.213,1.157a1.5,1.5,0,0,0,1.3-.4.907.907,0,0,0,.926.529,5.9,5.9,0,0,0,2-.529" fill="none" stroke="#e74c4c" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_48-2" data-name="Group 48" transform="translate(15.43 -238.318)">
        <path id="Path_46-2" data-name="Path 46" d="M0,.492H9.329" transform="translate(0 -0.492)" fill="none" stroke="#212121" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <path id="Path_47-2" data-name="Path 47" d="M0-212.333H40V-245H0Z" fill="none"/>
    </g>
  </g>
</svg>
