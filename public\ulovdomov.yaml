openapi: 3.0.0
info:
  title: "UD API"
  version: "1.0"
servers:
  - url: "http://localhost:8090"
    description: local
  - url: "http://localhost:8091"
    description: dummy
paths:
  /robots.txt:
    get:
      responses:
        "200":
          description: "Return true, if application is OK"
  /v1/health:
    get:
      responses:
        "200":
          description: "Return true, if application is OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HealthResponse"
  /v1/landlord/update:
    post:
      description: "Update landlord data"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateLandlordRequest"
      responses:
        "200":
          description: "Return updated landlord id"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateLandlordResponse"
  "/v1/make/data-stores/{dataId}":
    get:
      description: "Get data stores by dataId from make.com"
      parameters:
        - name: dataId
          in: path
          schema:
            type: number
            example: 1
      responses:
        "200":
          description: "Return true, if application is OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DataStoresResponse"
  /v1/offer/count:
    post:
      description: "Get offers count by filter"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FindRequest"
      responses:
        "200":
          description: "Count offers by filter"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CountResponse"
  /v1/offer/create:
    post:
      description: "Create or Update offer by data"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateRequest"
      responses:
        "200":
          description: "Create or Update offer by data"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateResponse"
  /v1/offer/data:
    get:
      description: "Get offer data"
      parameters:
        - name: lastOfferId
          in: query
          schema:
            type: number
            example: 4818903
        - name: limit
          in: query
          schema:
            type: number
            maximum: 100
            minimum: 1
            example: 100
      responses:
        "200":
          description: "Offer data"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OfferDataResponse"
  /v1/offer/detail:
    get:
      description: "Get offer detail"
      parameters:
        - name: offerId
          in: query
          schema:
            type: number
            example: 4818903
        - name: counter
          in: query
          schema:
            type: string
            enum:
              - detail
              - detail-page
              - map
            example: detail
            nullable: true
      responses:
        "200":
          description: "Offer detail"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DetailResponse"
  /v1/offer/detail-raw:
    get:
      description: "Get offer raw detail"
      parameters:
        - name: offerId
          in: query
          schema:
            type: number
            example: 4818903
      responses:
        "200":
          description: "Offer raw detail"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RawDetailResponse"
  /v1/offer/find:
    post:
      description: "Find offers by filter"
      parameters:
        - name: page
          in: query
          schema:
            type: number
            example: 1
        - name: perPage
          in: query
          schema:
            type: number
            example: 10
        - name: sorting
          in: query
          schema:
            type: string
            enum:
              - cheapest
              - latest
              - best
            example: cheapest
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FindRequest"
      responses:
        "200":
          description: "Find offers by filter"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FindResponse"
  /v1/offer/latest:
    get:
      description: "Get latest offers by propertyType and offerFunction"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LatestRequest"
      responses:
        "200":
          description: "Get latest offers by propertyType and offerFunction"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LatestResponse"
  /v1/offer/related:
    get:
      description: "Get related offer detail"
      parameters:
        - name: offerId
          in: query
          schema:
            type: number
            example: 4818903
      responses:
        "200":
          description: "OfferRelated related list"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RelatedResponse"
  /v1/offer/status-change:
    put:
      description: "Activate, deactivate or delete offer"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/StatusChangeRequest"
      responses:
        "200":
          description: "Activate, deactivate or delete offer"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusChangeResponse"
  /v1/verify/phone-check:
    get:
      description: "Create or Update offer by data"
      parameters:
        - name: prefix
          in: query
          schema:
            type: string
            example: "420"
        - name: number
          in: query
          schema:
            type: string
            example: "721321123"
        - name: otpCode
          in: query
          required: false
          schema:
            required:
              - sentSmsId
            type: string
            example: "123456"
        - name: sentSmsId
          in: query
          required: false
          schema:
            required:
              - otpCode
            type: string
            example: otp-a12345
      responses:
        "200":
          description: "Check phone number by code verification"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PhoneCheckResponse"
  /v1/verify/phone-resend:
    post:
      description: "Create or Update offer by data"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PhoneResendRequest"
      responses:
        "200":
          description: "Create or Update offer by data"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PhoneResendResponse"
  /v1/verify/phone-send:
    post:
      description: "Send OTP verification message to phone"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PhoneSendRequest"
      responses:
        "200":
          description: "Create or Update offer by data"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PhoneSendResponse"
components:
  schemas:
    ErrorResponse:
      required:
        - error
        - success
      properties:
        error:
          type: string
        success:
          type: boolean
        data:
          type: array
          items:
            type: object
          nullable: true
      type: object
    ValidationErrorItem:
      required:
        - name
        - code
      properties:
        name:
          type: string
        code:
          type: string
        in:
          type: string
          nullable: true
        value:
          nullable: true
        missing:
          type: string
          nullable: true
        expected:
          nullable: true
        used:
          type: string
          nullable: true
      type: object
    ValidationErrorResponse:
      required:
        - error
        - data
        - success
      properties:
        error:
          type: string
        data:
          type: array
          items:
            $ref: "#/components/schemas/ValidationErrorItem"
        success:
          type: boolean
      type: object
    Pagination:
      required:
        - total
        - totalPages
        - perPage
        - currentPage
      properties:
        total:
          type: integer
          minimum: 0
          example: 100
          x-faker:
            datatype.number:
              min: 1
              max: 1000
        totalPages:
          type: integer
          minimum: 0
          example: 10
          x-faker:
            datatype.number:
              min: 1
              max: 1000
        perPage:
          type: integer
          minimum: 0
          example: 10
          x-faker:
            datatype.number:
              min: 5
              max: 20
        currentPage:
          type: integer
          minimum: 0
          example: 2
          x-faker:
            datatype.number:
              min: 1
              max: 20
      type: object
    Phone:
      required:
        - prefix
        - number
      properties:
        prefix:
          type: string
        number:
          type: string
      type: object
    HealthData:
      required:
        - ok
      properties:
        ok:
          type: boolean
      type: object
    HealthResponse:
      required:
        - success
        - data
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/HealthData"
      type: object
    LandlordInvoiceData:
      required:
        - name
      properties:
        name:
          type: string
        identificationNumber:
          type: string
          nullable: true
        vatNumber:
          type: string
          nullable: true
        address:
          type: string
          nullable: true
      type: object
    UpdateLandlordRequest:
      required:
        - contactPhone
        - firstName
        - lastName
      properties:
        companyName:
          type: string
          nullable: true
        contactPhone:
          $ref: "#/components/schemas/Phone"
        firstName:
          type: string
        lastName:
          type: string
        userLandlordTypeId:
          type: integer
          nullable: true
        invoiceData:
          oneOf:
            - $ref: "#/components/schemas/LandlordInvoiceData"
          nullable: true
      type: object
    UpdateLandlordResponse:
      required:
        - success
        - data
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/UpdateLandlordResponseData"
      type: object
    UpdateLandlordResponseData:
      required: []
      properties:
        userId:
          type: integer
          nullable: true
      type: object
    DataStoresData:
      required:
        - url
        - filter
      properties:
        url:
          type: string
        filter:
          $ref: "#/components/schemas/Filter"
      type: object
    DataStoresResponse:
      required:
        - data
      properties:
        data:
          $ref: "#/components/schemas/DataStoresData"
      type: object
    Filter:
      required:
        - conveniences
        - dispositions
        - location
        - offerType
        - price
        - propertyType
        - sorting
      properties:
        conveniences:
          type: array
          items:
            type: string
        dispositions:
          type: array
          items:
            type: string
        location:
          type: string
        offerType:
          type: string
        price:
          $ref: "#/components/schemas/Price"
        propertyType:
          type: string
        sorting:
          type: string
      type: object
    Price:
      required:
        - min
        - max
      properties:
        min:
          type: integer
        max:
          type: integer
      type: object
    CountData:
      required:
        - count
      properties:
        count:
          type: integer
          x-faker:
            datatype.number:
              min: 0
              max: 10000
      type: object
    CountResponse:
      required:
        - success
        - data
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CountData"
      type: object
    CreateData:
      required:
        - offerId
      properties:
        offerId:
          type: integer
          x-faker:
            datatype.number:
              min: 1
              max: 10000
      type: object
    CreateRequest:
      required:
        - offerType
        - propertyType
        - availability
        - furnishing
        - usableArea
        - conveniences
        - houseConveniences
        - surroundingType
        - description
      properties:
        offerId:
          description: "You can edit existing offer if provide offerId."
          type: integer
          nullable: true
        emailAddress:
          type: string
          nullable: true
        phone:
          oneOf:
            - $ref: "#/components/schemas/Phone"
          nullable: true
        disposition:
          type: string
          enum:
            - onePlusKk
            - onePlusOne
            - twoPlusKk
            - twoPlusOne
            - threePlusKk
            - threePlusOne
            - fourPlusKk
            - fourPlusOne
            - fivePlusOne
            - fivePlusKk
            - sixAndMore
            - atypical
            - commercial
            - housing
            - field
            - forests
            - meadows
            - gardens
            - otherLand
            - offices
            - warehouses
            - production
            - commercialSpace
            - accommodation
            - restaurant
            - agricultural
            - otherCommercial
            - cottage
            - monumentOther
            - other
            - familyHouse
            - rentalHouse
            - villa
            - garage
            - turnKey
            - chalet
            - agriculturalFarm
            - ponds
            - room
            - orchardsVineyards
            - virtualOffice
            - wineCellar
            - atticSpace
            - garageParking
            - mobileHome
            - multiGenerationalHouse
            - medicalPractice
            - apartments
          nullable: true
        offerType:
          type: string
          enum:
            - sale
            - rent
            - auctions
            - coliving
        propertyType:
          type: string
          enum:
            - flat
            - house
            - land
            - commercial
            - other
        colivingType:
          type: string
          enum:
            - fullRoom
            - sharedRoom
          nullable: true
        price:
          type: integer
          nullable: true
        depositPrice:
          type: integer
          nullable: true
        monthlyFeesPrice:
          type: integer
          nullable: true
        priceUnit:
          type: string
          enum:
            - perRealEstate
            - perMonth
            - perSqM
            - perSqMPerMonth
            - perSqMPerYear
            - perYear
            - perDay
            - perHour
            - perSqMPerDay
            - perSqMPerHour
          nullable: true
        priceNote:
          type: string
          nullable: true
        availability:
          type: string
          enum:
            - asap
            - date
        firstTourDate:
          type: string
          format: date
          nullable: true
        firstTourDateTo:
          type: string
          format: date
          nullable: true
        furnishing:
          type: string
          enum:
            - "yes"
            - "no"
            - partial
        usableArea:
          type: integer
        estateArea:
          type: integer
          nullable: true
        floorCount:
          type: integer
          nullable: true
        roomCount:
          type: string
          enum:
            - oneRoom
            - twoRooms
            - threeRooms
            - fourRooms
            - fivePlusRooms
            - atypical
          nullable: true
        floorLevel:
          type: integer
          nullable: true
        conveniences:
          type: array
          items:
            type: string
            enum:
              - balcony
              - loggia
              - terrace
              - washingMachine
              - fridge
              - dishwasher
              - cellar
              - garden
              - lift
        houseConveniences:
          type: array
          items:
            type: string
            enum:
              - cellar
              - garage
              - pool
              - parking
              - wheelchairAccessible
        ownership:
          type: string
          enum:
            - personal
            - cooperative
            - municipal
          nullable: true
        buildingType:
          type: string
          enum:
            - groundFloor
            - multiStorey
          nullable: true
        buildingCondition:
          type: string
          enum:
            - veryGood
            - good
            - bad
            - inConstruction
            - project
            - newBuild
            - demolition
            - preReconstruction
            - postReconstruction
            - inReconstruction
          nullable: true
        material:
          type: string
          enum:
            - wood
            - brick
            - stone
            - assembled
            - panel
            - skeleton
            - mixed
          nullable: true
        flatType:
          type: string
          enum:
            - maisonette
            - loft
            - attic
          nullable: true
        objectType:
          type: string
          enum:
            - row
            - corner
            - inBlock
            - detached
          nullable: true
        locationType:
          type: string
          enum:
            - cityCenter
            - quietPart
            - busyPart
            - outskirts
            - housingEstate
            - semiIsolated
            - isolated
          nullable: true
        surroundingType:
          type: array
          items:
            type: string
            enum:
              - residential
              - businessResidential
              - business
              - commercial
              - industrial
              - rural
              - recreational
              - unusedRecreational
        protectedArea:
          type: string
          enum:
            - protectionZone
            - nationalPark
            - protectedLandscapeArea
          nullable: true
        energyEfficiencyRating:
          type: string
          enum:
            - a
            - b
            - c
            - d
            - e
            - f
            - g
          nullable: true
        typeInsert:
          type: string
          enum:
            - privateLandlord
            - realEstateBroker
          nullable: true
        description:
          type: string
        videoTourUrl:
          type: string
          nullable: true
        matterportUrl:
          type: string
          nullable: true
        typePriceInsert:
          type: string
          enum:
            - start
            - standard
            - vip
          nullable: true
        priceCommission:
          type: boolean
          nullable: true
        builtUpArea:
          type: integer
          nullable: true
        specificDate:
          type: string
          format: date
          example: "2024-02-02"
          nullable: true
        village:
          type: integer
          nullable: true
        street:
          type: integer
          nullable: true
        streetNumber:
          type: integer
          nullable: true
      type: object
    CreateResponse:
      required:
        - success
        - data
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateData"
      type: object
    OfferData:
      required:
        - id
        - title
        - description
        - url
        - parameters
      properties:
        id:
          description: "You can edit existing offer if provide offerId."
          type: integer
        title:
          type: string
        description:
          type: string
        url:
          type: string
        parameters:
          type: array
          items:
            $ref: "#/components/schemas/OfferParameterData"
      type: object
    OfferDataResponse:
      required:
        - success
        - data
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: "#/components/schemas/OfferData"
      type: object
    OfferParameterData:
      required:
        - name
      properties:
        name:
          type: string
        value:
          type: mixed
          nullable: true
      type: object
    DetailData:
      required:
        - id
        - offerTypeId
        - title
        - description
        - geoCoordinates
        - seo
        - absoluteUrl
        - adminUrl
        - status
        - owner
        - showScamWarning
        - isLocked
        - isContacted
        - parameters
      properties:
        id:
          type: integer
          x-faker:
            datatype.number:
              min: 1
              max: 10000
        offerTypeId:
          type: string
          enum:
            - sale
            - rent
            - auctions
            - coliving
        title:
          type: string
        description:
          type: string
          example: "Pronájem bytu 4+kk 102 m2 Praha - Ruzyněza 32000 Kč. Hledejte nabídky snadno a rychle na stránce s největší nabídkou pronájmů"
          x-faker: commerce.productDescription
        street:
          oneOf:
            - $ref: "#/components/schemas/LocationDetail"
          nullable: true
        village:
          oneOf:
            - $ref: "#/components/schemas/LocationDetail"
          nullable: true
        villagePart:
          oneOf:
            - $ref: "#/components/schemas/LocationDetail"
          nullable: true
        district:
          oneOf:
            - $ref: "#/components/schemas/LocationDetail"
          nullable: true
        region:
          oneOf:
            - $ref: "#/components/schemas/LocationDetail"
          nullable: true
        geoCoordinates:
          $ref: "#/components/schemas/GeoCoordinates"
        dispositionId:
          type: integer
          nullable: true
          x-faker:
            datatype.number:
              min: 1
              max: 10
        seo:
          type: string
          example: ""
        nearby:
          type: array
          items:
            $ref: "#/components/schemas/Nearby"
          nullable: true
        priceMedian:
          oneOf:
            - $ref: "#/components/schemas/PriceResponse"
          nullable: true
          x:
            faker:
              datatype.number:
                min: 1
                max: 10000
          example: 20
        rentalPrice:
          oneOf:
            - $ref: "#/components/schemas/PriceResponse"
          nullable: true
          x:
            faker:
              datatype.number:
                min: 1
                max: 10000
          example: 20
        monthlyFeePrice:
          oneOf:
            - $ref: "#/components/schemas/PriceResponse"
          nullable: true
          x:
            faker:
              datatype.number:
                min: 1
                max: 10000
          example: 20
        depositPrice:
          oneOf:
            - $ref: "#/components/schemas/PriceResponse"
          nullable: true
          x:
            faker:
              datatype.number:
                min: 1
                max: 10000
          example: 20
        priceUnit:
          type: string
          enum:
            - perRealEstate
            - perMonth
            - perSqM
            - perSqMPerMonth
            - perSqMPerYear
            - perYear
            - perDay
            - perHour
            - perSqMPerDay
            - perSqMPerHour
          nullable: true
        isNoCommission:
          type: boolean
        priceNote:
          type: string
          example: "150 kč měs. / 1os."
          nullable: true
        absoluteUrl:
          type: string
          example: "https://www.ulovdomov.cz/inzerat/pronajem-praha-ruzyne-stocesova-4-kk/5280075"
          x-faker: internet.url
        publishedAt:
          type: string
          example: "2023-14-08 16:29:05"
          nullable: true
          x-faker:
            datatype.date.between:
              from: "2020-01-01T00:00:00.000Z"
              to: "2030-01-01T00:00:00.000Z"
        adminUrl:
          type: string
          example: "https://www.ulovdomov.cz/xadmin/offer/943847"
          x-faker: internet.url
        status:
          type: string
          enum:
            - DRAFT
            - ACTIVE
            - EXPIRED
            - CANCELED
            - IMPORTED
            - IMPORT_ACTIVE
            - IMPORT_EXPIRED
            - IMPORT_IGNORED
            - DELETED
            - IMPORT_DELETED
          example: DRAFT
        owner:
          $ref: "#/components/schemas/Owner"
        showScamWarning:
          type: boolean
        isLocked:
          type: boolean
        isContacted:
          type: boolean
        photos:
          type: array
          items:
            $ref: "#/components/schemas/Photo"
          nullable: true
        matterportUrl:
          oneOf:
            - $ref: "#/components/schemas/MatterportUrl"
          nullable: true
        priceParameters:
          type: array
          items:
            $ref: "#/components/schemas/PriceFormatedResponse"
          nullable: true
        parameters:
          $ref: "#/components/schemas/DetailParameters"
      type: object
    DetailParameters:
      required: []
      properties:
        offerFunction:
          oneOf:
            - $ref: "#/components/schemas/OfferFunctionParameter"
          nullable: true
        propertyType:
          oneOf:
            - $ref: "#/components/schemas/PropertyTypeParameter"
          nullable: true
        roomCount:
          oneOf:
            - $ref: "#/components/schemas/RoomCountParameter"
          nullable: true
        disposition:
          oneOf:
            - $ref: "#/components/schemas/DispositionParameter"
          nullable: true
        houseType:
          oneOf:
            - $ref: "#/components/schemas/HouseTypeParameter"
          nullable: true
        balcony:
          oneOf:
            - $ref: "#/components/schemas/Balcony"
          nullable: true
        pool:
          oneOf:
            - $ref: "#/components/schemas/Pool"
          nullable: true
        buildingCondition:
          oneOf:
            - $ref: "#/components/schemas/BuildingConditionParameter"
          nullable: true
        buildingType:
          oneOf:
            - $ref: "#/components/schemas/BuildingTypeParameter"
          nullable: true
        cellar:
          oneOf:
            - $ref: "#/components/schemas/Cellar"
          nullable: true
        estateArea:
          oneOf:
            - $ref: "#/components/schemas/EstateArea"
          nullable: true
        floorNumber:
          oneOf:
            - $ref: "#/components/schemas/FloorNumber"
          nullable: true
        garage:
          oneOf:
            - $ref: "#/components/schemas/Garage"
          nullable: true
        loggia:
          oneOf:
            - $ref: "#/components/schemas/Loggia"
          nullable: true
        objectType:
          oneOf:
            - $ref: "#/components/schemas/ObjectTypeParameter"
          nullable: true
        ownership:
          oneOf:
            - $ref: "#/components/schemas/OwnershipParameter"
          nullable: true
        parkingLots:
          oneOf:
            - $ref: "#/components/schemas/ParkingLots"
          nullable: true
        terrace:
          oneOf:
            - $ref: "#/components/schemas/Terrace"
          nullable: true
        usableArea:
          oneOf:
            - $ref: "#/components/schemas/UsableArea"
          nullable: true
        acceptanceYear:
          oneOf:
            - $ref: "#/components/schemas/AcceptanceYear"
          nullable: true
        lowEnergy:
          oneOf:
            - $ref: "#/components/schemas/LowEnergy"
          nullable: true
        priceNegotiation:
          oneOf:
            - $ref: "#/components/schemas/PriceNegotiation"
          nullable: true
        energyEfficiencyRating:
          oneOf:
            - $ref: "#/components/schemas/EnergyEfficiencyParameter"
          nullable: true
        finishDate:
          oneOf:
            - $ref: "#/components/schemas/FinishDate"
          nullable: true
        firstTourDate:
          oneOf:
            - $ref: "#/components/schemas/FirstTourDate"
          nullable: true
        firstTourDateTo:
          oneOf:
            - $ref: "#/components/schemas/FirstTourDateTo"
          nullable: true
        flatType:
          oneOf:
            - $ref: "#/components/schemas/FlatTypeParameter"
          nullable: true
        floorArea:
          oneOf:
            - $ref: "#/components/schemas/FloorArea"
          nullable: true
        floors:
          oneOf:
            - $ref: "#/components/schemas/Floors"
          nullable: true
        furnished:
          oneOf:
            - $ref: "#/components/schemas/FurnishedParameter"
          nullable: true
        loft:
          oneOf:
            - $ref: "#/components/schemas/Loft"
          nullable: true
        gas:
          oneOf:
            - $ref: "#/components/schemas/GasParameter"
          nullable: true
        gully:
          oneOf:
            - $ref: "#/components/schemas/GullyParameter"
          nullable: true
        heating:
          oneOf:
            - $ref: "#/components/schemas/HeatingParameter"
          nullable: true
        objectAge:
          oneOf:
            - $ref: "#/components/schemas/ObjectAge"
          nullable: true
        material:
          oneOf:
            - $ref: "#/components/schemas/MaterialParameter"
          nullable: true
        objectLocation:
          oneOf:
            - $ref: "#/components/schemas/ObjectLocationParameter"
          nullable: true
        personal:
          oneOf:
            - $ref: "#/components/schemas/PersonalParameter"
          nullable: true
        protection:
          oneOf:
            - $ref: "#/components/schemas/ProtectionParameter"
          nullable: true
        readyDate:
          oneOf:
            - $ref: "#/components/schemas/ReadyDate"
          nullable: true
        reconstructionYear:
          oneOf:
            - $ref: "#/components/schemas/ReconstructionYear"
          nullable: true
        roadType:
          oneOf:
            - $ref: "#/components/schemas/RoadTypeParameter"
          nullable: true
        surroundingType:
          oneOf:
            - $ref: "#/components/schemas/SurroundingTypeParameter"
          nullable: true
        telecommunication:
          oneOf:
            - $ref: "#/components/schemas/TelecommunicationParameter"
          nullable: true
        transport:
          oneOf:
            - $ref: "#/components/schemas/TransportParameter"
          nullable: true
        water:
          oneOf:
            - $ref: "#/components/schemas/WaterParameter"
          nullable: true
        lift:
          oneOf:
            - $ref: "#/components/schemas/Lift"
          nullable: true
        wheelchairAccessible:
          oneOf:
            - $ref: "#/components/schemas/WheelchairAccessible"
          nullable: true
        gardenArea:
          oneOf:
            - $ref: "#/components/schemas/GardenArea"
          nullable: true
      type: object
    DetailRequest:
      required: []
    DetailResponse:
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: "#/components/schemas/DetailData"
      type: object
    LocationDetail:
      required:
        - id
        - name
      properties:
        id:
          type: integer
          example: 1
          x-faker:
            datatype.number:
              min: 1
              max: 10000
        name:
          type: string
          example: Brno
      type: object
    Nearby:
      required:
        - name
        - type
        - value
      properties:
        name:
          type: string
          example: "Žabka Praha 10, Hyacintová"
        type:
          type: string
          enum:
            - mall
            - travelPoint
          example: mall
        value:
          type: string
          minLength: 1
          example: "3 minut (pěšky)"
      type: object
    Owner:
      required:
        - id
        - firstName
        - surname
        - phone
        - isIn
      properties:
        id:
          type: integer
          example: 3
          x-faker:
            datatype.number:
              min: 1
              max: 10000
        type:
          type: string
          minLength: 1
          example: reality.cz
          nullable: true
          x-faker: internet.url
        firstName:
          type: string
          minLength: 0
          example: Alžběta
          x-faker: name.firstName
        surname:
          type: string
          minLength: 0
          example: Nováková
          x-faker: name.lastName
        photo:
          type: string
          minLength: 1
          example: "https://ud-be.k8stage.ulovdomov.cz/avatar/3q/QUHYZwoy"
          nullable: true
          x-faker: image.animals
        phone:
          type: string
          minLength: 0
          example: "770965345"
        isIn:
          type: boolean
          example: false
      type: object
    AcceptanceYear:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: "Rok kolaudace"
        seo:
          type: string
          example: rok-kolaudace
        value:
          type: string
          example: "2014"
      type: object
    AdvertCode:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: "ID zakázky"
        seo:
          type: string
          example: AC45BR
        value:
          type: string
          example: AC45BR
      type: object
    Balcony:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: Balkon
        seo:
          type: string
          example: balkon
        value:
          type: string
          example: Ano
      type: object
    BuildingConditionOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: Dobrý
        seo:
          type: string
          example: dobry
        id:
          type: string
          enum:
            - veryGood
            - good
            - bad
            - inConstruction
            - project
            - newBuild
            - demolition
            - preReconstruction
            - postReconstruction
            - inReconstruction
      type: object
    BuildingConditionParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: "Stav objektu"
        seo:
          type: string
          example: stav-objektu
        options:
          type: array
          items:
            $ref: "#/components/schemas/BuildingConditionOption"
      type: object
    BuildingTypeOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: Patrová
        seo:
          type: string
          example: patrová
        id:
          type: string
          enum:
            - groundFloor
            - multiStorey
      type: object
    BuildingTypeParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: "Typ budovy"
        seo:
          type: string
          example: typ-budovy
        options:
          type: array
          items:
            $ref: "#/components/schemas/BuildingTypeOption"
      type: object
    Cellar:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: Sklep
        seo:
          type: string
          example: sklep
        value:
          type: string
          example: Ano
      type: object
    DispositionOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: 1kk
        seo:
          type: string
          example: 1kk
        id:
          type: string
          enum:
            - onePlusKk
            - onePlusOne
            - twoPlusKk
            - twoPlusOne
            - threePlusKk
            - threePlusOne
            - fourPlusKk
            - fourPlusOne
            - fivePlusOne
            - fivePlusKk
            - sixAndMore
            - atypical
            - null
      type: object
    DispositionParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: Podkategorie
        seo:
          type: string
          minLength: 1
          example: podkategorie
        options:
          type: array
          items:
            $ref: "#/components/schemas/DispositionOption"
      type: object
    EnergyEfficiencyOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: "A - Nenarocna"
        seo:
          type: string
          example: a
        id:
          type: string
          enum:
            - a
            - b
            - c
            - d
            - e
            - f
            - g
      type: object
    EnergyEfficiencyParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: "Energeticka narocnost"
        seo:
          type: string
          example: energeticka-nartocnost
        options:
          type: array
          items:
            $ref: "#/components/schemas/EnergyEfficiencyOption"
      type: object
    EstateArea:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: "Plocha pozemku"
        seo:
          type: string
          example: plocha-pozemku
        value:
          type: string
          example: "122 m2"
      type: object
    FinishDate:
      required:
        - title
        - value
      properties:
        title:
          type: string
          example: "Datum dokončení"
        value:
          type: string
          example: "2023-14-08 16:29:05"
      type: object
    FirstTourDate:
      required:
        - title
        - value
      properties:
        title:
          type: string
          example: "Datum prohlídky"
        value:
          type: string
          example: "2023-14-08 16:29:05"
      type: object
    FirstTourDateTo:
      required:
        - title
        - value
      properties:
        title:
          type: string
          example: "Datum prohlídky do"
        value:
          type: string
          example: "2023-14-08 16:29:05"
      type: object
    FlatTypeOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: Mezonet
        seo:
          type: string
          example: mezonet
        id:
          type: string
          enum:
            - maisonette
            - loft
            - attic
      type: object
    FlatTypeParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: "Typ bytu"
        seo:
          type: string
          example: typ-bytu
        options:
          type: array
          items:
            $ref: "#/components/schemas/FlatTypeOption"
      type: object
    FloorArea:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: "Podlahová plocha"
        seo:
          type: string
          example: podlahova-plocha
        value:
          type: string
          example: "56 m2"
      type: object
    FloorNumber:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: Patro
        seo:
          type: string
          example: patro
        value:
          type: integer
          example: "7"
          x-faker:
            datatype.number:
              min: 1
              max: 15
      type: object
    Floors:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: "Podlaží počet"
        seo:
          type: string
          example: podlazi-pocet
        value:
          type: integer
          example: 12
          x-faker:
            datatype.number:
              min: 1
              max: 15
      type: object
    FurnishedOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: Ano
        seo:
          type: string
          example: ano
        id:
          type: string
          enum:
            - "yes"
            - "no"
            - partial
      type: object
    FurnishedParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: Vybavení
        seo:
          type: string
          example: vybaveni
        options:
          type: array
          items:
            $ref: "#/components/schemas/FurnishedOption"
      type: object
    Garage:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: Garage
        seo:
          type: string
          example: garaz
        value:
          type: string
          example: Ano
      type: object
    Garden:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: Garden
        seo:
          type: string
          example: garden
        value:
          type: string
          example: Ano
      type: object
    GardenArea:
      required:
        - title
        - value
      properties:
        title:
          type: string
          example: "Plocha zahrady"
        value:
          type: string
          example: "122 m2"
      type: object
    GasOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: Plynovod
        id:
          type: string
          enum:
            - individual
            - gasPipeline
      type: object
    GasParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: Plyn
        options:
          type: array
          items:
            $ref: "#/components/schemas/GasOption"
      type: object
    GullyOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: Jímka
        seo:
          type: string
          example: jimka
        id:
          type: string
          enum:
            - publicSewer
            - wholeBuildingSewer
            - septicTank
            - sump
            - drainageChannel
      type: object
    GullyParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: Odpad
        seo:
          type: string
          example: odpad
        options:
          type: array
          items:
            $ref: "#/components/schemas/GullyOption"
      type: object
    HeatingOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: Podlahové
        id:
          type: string
          enum:
            - localGas
            - localSolidFuels
            - localElectric
            - centralGas
            - centralSolidFuels
            - centralElectric
            - centralDistrict
            - other
            - underfloor
      type: object
    HeatingParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: Vytápění
        options:
          type: array
          items:
            $ref: "#/components/schemas/HeatingOption"
      type: object
    HouseTypeOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: familyHouse
        seo:
          type: string
          example: familyHouse
        id:
          type: string
          enum:
            - cottage
            - monumentOther
            - familyHouse
            - villa
            - turnKey
            - chalet
            - agriculturalFarm
            - multiGeneration
            - null
      type: object
    HouseTypeParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: "Typ domu"
        seo:
          type: string
          minLength: 1
          example: "Typ domu"
        options:
          type: array
          items:
            $ref: "#/components/schemas/HouseTypeOption"
      type: object
    Lift:
      required:
        - title
        - value
      properties:
        title:
          type: string
          example: Výtah
        value:
          type: string
          example: ano
      type: object
    Loft:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: "Půdní vestavba"
        seo:
          type: string
          example: pudni-vestavba
        value:
          type: string
          example: Ano
      type: object
    Loggia:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: Lodžie
        seo:
          type: string
          example: lodzie
        value:
          type: string
          example: Ano
      type: object
    LowEnergy:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: Nízkoenergetický
        seo:
          type: string
          example: nizkoenergeticky
        value:
          type: string
          example: Ano
      type: object
    MaterialOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: Dřevěný
        seo:
          type: string
          example: dreveny
        id:
          type: string
          enum:
            - wood
            - brick
            - stone
            - assembled
            - panel
            - skeleton
            - mixed
      type: object
    MaterialParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: "Poloha domu"
        seo:
          type: string
          example: poloha-domu
        options:
          type: array
          items:
            $ref: "#/components/schemas/MaterialOption"
      type: object
    MatterportUrl:
      required:
        - title
        - value
      properties:
        title:
          type: string
          example: "Odkaz na VR prohlidku na webu Matterport"
        value:
          type: string
          example: www.odkaz.cz
      type: object
    ObjectAge:
      required:
        - title
        - value
      properties:
        title:
          type: string
          example: "Rok výstavby"
        value:
          type: integer
          example: "2023"
      type: object
    ObjectLocationOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: Samota
        id:
          type: string
          enum:
            - cityCenter
            - quietPart
            - busyPart
            - outskirts
            - housingEstate
            - semiIsolated
            - isolated
      type: object
    ObjectLocationParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: "Umísteˇní objektu"
        options:
          type: array
          items:
            $ref: "#/components/schemas/ObjectLocationOption"
      type: object
    ObjectTypeOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: Rohovy
        seo:
          type: string
          example: rohovy
        id:
          type: string
          enum:
            - row
            - corner
            - inBlock
            - detached
      type: object
    ObjectTypeParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: "Typ domu"
        seo:
          type: string
          example: typ-domu
        options:
          type: array
          items:
            $ref: "#/components/schemas/ObjectTypeOption"
      type: object
    OfferFunctionOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: Prodej
        id:
          type: string
          enum:
            - sale
            - rent
            - auctions
            - coliving
      type: object
    OfferFunctionParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: Typ
        options:
          type: array
          items:
            $ref: "#/components/schemas/OfferFunctionOption"
      type: object
    OwnershipOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: Osobní
        seo:
          type: string
          example: osobni
        id:
          type: string
          enum:
            - personal
            - cooperative
            - municipal
      type: object
    OwnershipParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: Vlastnictví
        seo:
          type: string
          example: vlastnictvi
        options:
          type: array
          items:
            $ref: "#/components/schemas/OwnershipOption"
      type: object
    ParkingLots:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: Parkování
        seo:
          type: string
          example: parkovani
        value:
          type: string
          example: ano
      type: object
    PersonalOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: Ano
        id:
          type: string
          enum:
            - "yes"
            - "no"
      type: object
    PersonalParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: "Převod do OV"
        options:
          type: array
          items:
            $ref: "#/components/schemas/PersonalOption"
      type: object
    Pool:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: Bazén
        seo:
          type: string
          example: bazen
        value:
          type: string
          example: Ano
      type: object
    PriceNegotiation:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: "Cena k jednání"
        seo:
          type: string
          example: cena-k-jednani
        value:
          type: string
          example: Ano
      type: object
    PriceTextNote:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: "Poznámka k ceně"
        seo:
          type: string
          example: poznamka-k-cene
        value:
          type: string
          example: "Bez DPH"
      type: object
    PropertyTypeOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: Domy
        id:
          type: string
          enum:
            - flat
            - house
            - land
            - commercial
            - other
      type: object
    PropertyTypeParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: Kategorie
        options:
          type: array
          items:
            $ref: "#/components/schemas/PropertyTypeOption"
      type: object
    ProtectionOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: CHKO
        id:
          type: string
          enum:
            - protectionZone
            - nationalPark
            - protectedLandscapeArea
      type: object
    ProtectionParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: Ochrana
        options:
          type: array
          items:
            $ref: "#/components/schemas/ProtectionOption"
      type: object
    ReadyDate:
      required:
        - title
        - value
      properties:
        title:
          type: string
          example: "Datum nastěhování"
        value:
          type: string
          example: "2023-13-08"
      type: object
    ReconstructionYear:
      required:
        - title
        - value
      properties:
        title:
          type: string
          example: "Rok rekonstrukce"
        value:
          type: integer
          example: "2023"
      type: object
    RoadTypeOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: Asfaltová
        id:
          type: string
          enum:
            - concrete
            - paved
            - asphalt
            - unpaved
            - stabilized
            - gravel
            - dirtRoad
            - noRoad
      type: object
    RoadTypeParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: Komunikace
        options:
          type: array
          items:
            $ref: "#/components/schemas/RoadTypeOption"
      type: object
    RoomCountOption:
      required:
        - title
        - seo
        - id
      properties:
        title:
          type: string
          example: "1 pokoj"
        seo:
          type: string
          example: 1-pokoje
        id:
          type: string
          enum:
            - oneRoom
            - twoRooms
            - threeRooms
            - fourRooms
            - fivePlusRooms
            - atypical
      type: object
    RoomCountParameter:
      required:
        - title
        - seo
        - options
      properties:
        title:
          type: string
          example: Velikost
        seo:
          type: string
          example: velikost
        options:
          type: array
          items:
            $ref: "#/components/schemas/RoomCountOption"
      type: object
    SaleDate:
      required:
        - title
        - value
      properties:
        title:
          type: string
          example: "Datum zahájení prodeje"
        value:
          type: string
          example: "2023-14-08 16:29:05"
      type: object
    SurroundingTypeOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: Obytná
        id:
          type: string
          enum:
            - residential
            - businessResidential
            - business
            - commercial
            - industrial
            - rural
            - recreational
            - unusedRecreational
      type: object
    SurroundingTypeParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: Zástavba
        options:
          type: array
          items:
            $ref: "#/components/schemas/SurroundingTypeOption"
      type: object
    TelecommunicationOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: Internet
        id:
          type: string
          enum:
            - telephone
            - internet
            - satellite
            - cableTV
            - cableDistribution
            - other
      type: object
    TelecommunicationParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: Telekomunikace
        options:
          type: array
          items:
            $ref: "#/components/schemas/TelecommunicationOption"
      type: object
    Terrace:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: Terasa
        seo:
          type: string
          example: terasa
        value:
          type: string
          example: Ano
      type: object
    TransportOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: Vlak
        id:
          type: string
          enum:
            - train
            - highway
            - road
            - publicTransport
            - bus
      type: object
    TransportParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: Doprava
        options:
          type: array
          items:
            $ref: "#/components/schemas/TransportOption"
      type: object
    UsableArea:
      required:
        - title
        - seo
        - value
      properties:
        title:
          type: string
          example: "Užitná plocha"
        seo:
          type: string
          example: uzitna-plocha
        value:
          type: string
          example: "54 m2"
      type: object
    WaterOption:
      required:
        - title
        - id
      properties:
        title:
          type: string
          example: Studna
        id:
          type: string
          enum:
            - localSource
            - districtWaterSupply
            - well
      type: object
    WaterParameter:
      required:
        - title
        - options
      properties:
        title:
          type: string
          example: Voda
        options:
          type: array
          items:
            $ref: "#/components/schemas/WaterOption"
      type: object
    WheelchairAccessible:
      required:
        - title
        - value
      properties:
        title:
          type: string
          example: Bezbarierové
        value:
          type: string
          example: ano
      type: object
    RawDetailData:
      required:
        - "offerId,"
        - "emailAddress,"
        - "phone,"
        - "disposition,"
        - "offerType,"
        - "propertyType,"
        - "colivingType,"
        - "price,"
        - "depositPrice,"
        - "monthlyFeesPrice,"
        - "priceUnit,"
        - "priceNote,"
        - "availability,"
        - "firstTourDate,"
        - "firstTourDateTo,"
        - "furnishing,"
        - "usableArea,"
        - "estateArea,"
        - "floorCount,"
        - "roomCount,"
        - "floorLevel,"
        - "conveniences,"
        - "houseConveniences,"
        - "ownership,"
        - "buildingType,"
        - "buildingCondition,"
        - "material,"
        - "flatType,"
        - "objectType,"
        - "locationType,"
        - "surroundingType,"
        - "protectedArea,"
        - "energyEfficiencyRating,"
        - "typeInsert,"
        - "description,"
        - "videoTourUrl,"
        - "virtualTourUrl,"
        - "typePriceInsert,"
        - "priceCommission,"
        - "builtUpArea,"
        - "specificDate,"
        - "village,"
        - "street,"
        - "streetNumber,"
      properties:
        offerId:
          description: "You can edit existing offer if provide offerId."
          type: integer
        emailAddress:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        disposition:
          type: string
          enum:
            - onePlusKk
            - onePlusOne
            - twoPlusKk
            - twoPlusOne
            - threePlusKk
            - threePlusOne
            - fourPlusKk
            - fourPlusOne
            - fivePlusOne
            - fivePlusKk
            - sixAndMore
            - atypical
            - commercial
            - housing
            - field
            - forests
            - meadows
            - gardens
            - otherLand
            - offices
            - warehouses
            - production
            - commercialSpace
            - accommodation
            - restaurant
            - agricultural
            - otherCommercial
            - cottage
            - monumentOther
            - other
            - familyHouse
            - rentalHouse
            - villa
            - garage
            - turnKey
            - chalet
            - agriculturalFarm
            - ponds
            - room
            - orchardsVineyards
            - virtualOffice
            - wineCellar
            - atticSpace
            - garageParking
            - mobileHome
            - multiGenerationalHouse
            - medicalPractice
            - apartments
          nullable: true
        offerType:
          type: string
          enum:
            - sale
            - rent
            - auctions
            - coliving
        propertyType:
          type: string
          enum:
            - flat
            - house
            - land
            - commercial
            - other
        colivingType:
          type: string
          enum:
            - fullRoom
            - sharedRoom
          nullable: true
        price:
          type: integer
          nullable: true
        depositPrice:
          type: integer
          nullable: true
        monthlyFeesPrice:
          type: integer
          nullable: true
        priceUnit:
          type: string
          enum:
            - perRealEstate
            - perMonth
            - perSqM
            - perSqMPerMonth
            - perSqMPerYear
            - perYear
            - perDay
            - perHour
            - perSqMPerDay
            - perSqMPerHour
          nullable: true
        priceNote:
          type: string
          nullable: true
        availability:
          type: string
          enum:
            - asap
            - date
        firstTourDate:
          type: string
          format: date
          nullable: true
        firstTourDateTo:
          type: string
          format: date
          nullable: true
        furnishing:
          type: string
          enum:
            - "yes"
            - "no"
            - partial
        usableArea:
          type: integer
        estateArea:
          type: integer
          nullable: true
        floorCount:
          type: integer
          nullable: true
        roomCount:
          type: string
          enum:
            - oneRoom
            - twoRooms
            - threeRooms
            - fourRooms
            - fivePlusRooms
            - atypical
          nullable: true
        floorLevel:
          type: integer
          nullable: true
        conveniences:
          type: array
          items:
            type: string
            enum:
              - balcony
              - loggia
              - terrace
              - washingMachine
              - fridge
              - dishwasher
              - cellar
              - garden
              - lift
        houseConveniences:
          type: array
          items:
            type: string
            enum:
              - cellar
              - garage
              - pool
              - parking
              - wheelchairAccessible
        ownership:
          type: string
          enum:
            - personal
            - cooperative
            - municipal
          nullable: true
        buildingType:
          type: string
          enum:
            - groundFloor
            - multiStorey
          nullable: true
        buildingCondition:
          type: string
          enum:
            - veryGood
            - good
            - bad
            - inConstruction
            - project
            - newBuild
            - demolition
            - preReconstruction
            - postReconstruction
            - inReconstruction
          nullable: true
        material:
          type: string
          enum:
            - wood
            - brick
            - stone
            - assembled
            - panel
            - skeleton
            - mixed
          nullable: true
        flatType:
          type: string
          enum:
            - maisonette
            - loft
            - attic
          nullable: true
        objectType:
          type: string
          enum:
            - row
            - corner
            - inBlock
            - detached
          nullable: true
        locationType:
          type: string
          enum:
            - cityCenter
            - quietPart
            - busyPart
            - outskirts
            - housingEstate
            - semiIsolated
            - isolated
          nullable: true
        surroundingType:
          type: array
          items:
            type: string
            enum:
              - residential
              - businessResidential
              - business
              - commercial
              - industrial
              - rural
              - recreational
              - unusedRecreational
        protectedArea:
          type: string
          enum:
            - protectionZone
            - nationalPark
            - protectedLandscapeArea
          nullable: true
        energyEfficiencyRating:
          type: string
          enum:
            - a
            - b
            - c
            - d
            - e
            - f
            - g
          nullable: true
        typeInsert:
          type: string
          enum:
            - privateLandlord
            - realEstateBroker
          nullable: true
        description:
          type: string
        videoTourUrl:
          type: string
          nullable: true
        matterportUrl:
          type: string
          nullable: true
        typePriceInsert:
          type: string
          enum:
            - start
            - standard
            - vip
          nullable: true
        priceCommission:
          type: boolean
          nullable: true
        builtUpArea:
          type: integer
          nullable: true
        specificDate:
          type: string
          format: date
          example: "2024-02-02"
          nullable: true
        village:
          oneOf:
            - $ref: "#/components/schemas/RawDetailLocality"
          nullable: true
        street:
          oneOf:
            - $ref: "#/components/schemas/RawDetailLocality"
          nullable: true
        streetNumber:
          oneOf:
            - $ref: "#/components/schemas/RawDetailLocality"
          nullable: true
      type: object
    RawDetailLocality:
      required:
        - id
        - value
      properties:
        id:
          type: integer
        value:
          type: string
      type: object
    RawDetailResponse:
      required:
        - success
        - data
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/RawDetailData"
      type: object
    Availability:
      required:
        - availability
      properties:
        availability:
          type: string
          format: date
      type: object
    BoundsRequest:
      required:
        - northEast
        - southWest
      properties:
        northEast:
          $ref: "#/components/schemas/GeoCoordinates"
        southWest:
          $ref: "#/components/schemas/GeoCoordinates"
      type: object
    FindData:
      required:
        - offers
        - markers
      properties:
        offers:
          type: array
          items:
            $ref: "#/components/schemas/Offer"
        markers:
          type: array
          items:
            $ref: "#/components/schemas/Mark"
      type: object
    FindRequest:
      required:
        - offerType
        - bounds
      properties:
        propertyType:
          type: string
          enum:
            - flat
            - house
            - land
            - commercial
            - other
        offerType:
          type: string
          enum:
            - sale
            - rent
            - auctions
            - coliving
        colivingType:
          type: array
          items:
            type: string
            enum:
              - fullRoom
              - sharedRoom
        disposition:
          type: array
          items:
            type: string
            enum:
              - onePlusKk
              - onePlusOne
              - twoPlusKk
              - twoPlusOne
              - threePlusKk
              - threePlusOne
              - fourPlusKk
              - fourPlusOne
              - fivePlusOne
              - fivePlusKk
              - sixAndMore
        houseType:
          type: array
          items:
            type: string
            enum:
              - cottage
              - monumentOther
              - familyHouse
              - villa
              - turnKey
              - chalet
              - agriculturalFarm
        bounds:
          $ref: "#/components/schemas/BoundsRequest"
        roomCount:
          type: array
          items:
            type: string
            enum:
              - oneRoom
              - twoRooms
              - threeRooms
              - fourRooms
              - fivePlusRooms
              - atypical
        price:
          $ref: "#/components/schemas/PriceRange"
        floorArea:
          $ref: "#/components/schemas/FloorAreaRange"
        gardenArea:
          $ref: "#/components/schemas/GardenAreaRange"
        furnished:
          type: array
          items:
            type: string
            enum:
              - "yes"
              - "no"
              - partial
        isNoCommission:
          type: boolean
        convenience:
          type: array
          items:
            type: string
            enum:
              - balcony
              - loggia
              - terrace
              - washingMachine
              - fridge
              - dishwasher
              - cellar
              - garden
              - lift
        houseConvenience:
          type: array
          items:
            type: string
            enum:
              - cellar
              - garage
              - pool
              - parking
              - wheelchairAccessible
        buildingType:
          type: array
          items:
            type: string
            enum:
              - groundFloor
              - multiStorey
        objectType:
          type: array
          items:
            type: string
            enum:
              - row
              - corner
              - inBlock
              - detached
        flatType:
          type: array
          items:
            type: string
            enum:
              - maisonette
              - loft
              - attic
        buildingCondition:
          type: array
          items:
            type: string
            enum:
              - veryGood
              - good
              - bad
              - inConstruction
              - project
              - newBuild
              - demolition
              - preReconstruction
              - postReconstruction
              - inReconstruction
        material:
          type: array
          items:
            type: string
            enum:
              - wood
              - brick
              - stone
              - assembled
              - panel
              - skeleton
              - mixed
        floor:
          $ref: "#/components/schemas/FloorRange"
        floorCount:
          $ref: "#/components/schemas/FloorCountRange"
        energyEfficiencyRating:
          type: array
          items:
            type: string
            enum:
              - a
              - b
              - c
              - d
              - e
              - f
              - g
        ownership:
          type: array
          items:
            type: string
            enum:
              - personal
              - cooperative
              - municipal
        locationType:
          type: array
          items:
            type: string
            enum:
              - cityCenter
              - quietPart
              - busyPart
              - outskirts
              - housingEstate
              - semiIsolated
              - isolated
        surroundingType:
          type: array
          items:
            type: string
            enum:
              - residential
              - businessResidential
              - business
              - commercial
              - industrial
              - rural
              - recreational
              - unusedRecreational
        protectedArea:
          type: array
          items:
            type: string
            enum:
              - protectionZone
              - nationalPark
              - protectedLandscapeArea
        availabilityType:
          type: string
          enum:
            - asap
            - date
        availabilityDate:
          type: string
        offerAge:
          type: string
          enum:
            - day
            - week
            - month
      type: object
    FindResponse:
      required:
        - success
        - extraData
        - data
      properties:
        success:
          type: boolean
        extraData:
          $ref: "#/components/schemas/Pagination"
        data:
          $ref: "#/components/schemas/FindData"
      type: object
    FloorAreaRange:
      required: []
      properties:
        min:
          type: integer
          maximum: 250
          minimum: 0
          example: 10
        max:
          type: integer
          maximum: 250
          minimum: 0
          example: 25
      type: object
    FloorCountRange:
      required: []
      properties:
        min:
          type: integer
          maximum: 100
          minimum: 0
          example: 0
        max:
          type: integer
          maximum: 100
          minimum: 0
          example: 2
      type: object
    FloorRange:
      required: []
      properties:
        min:
          type: integer
          maximum: 100
          minimum: 0
          example: 0
        max:
          type: integer
          maximum: 100
          minimum: 0
          example: 2
      type: object
    GardenAreaRange:
      required: []
      properties:
        min:
          type: integer
          maximum: 5000
          minimum: 0
          example: 50
        max:
          type: integer
          maximum: 5000
          minimum: 0
          example: 200
      type: object
    Location:
      required:
        - id
        - title
      properties:
        id:
          type: number
          minimum: 0
          example: 2
        title:
          type: string
          example: Brno
          x-faker: address.city
      type: object
    Mark:
      required:
        - geoCoordinates
        - offerIds
      properties:
        geoCoordinates:
          $ref: "#/components/schemas/GeoCoordinates"
        offerIds:
          type: array
          items:
            type: number
            minimum: 0
            example: 2
            x-faker:
              datatype.number:
                min: 1
                max: 1000
      type: object
    Offer:
      required:
        - id
        - title
        - area
        - description
        - geoCoordinates
        - photos
        - isNoCommission
        - published
        - seo
        - village
        - convenience
        - houseConvenience
        - offerType
        - propertyType
        - showScamWarn
        - isContacted
        - isTop
        - absoluteUrl
      properties:
        id:
          type: integer
          x-faker:
            datatype.number:
              min: 1
              max: 1000
        title:
          type: string
        area:
          type: integer
          x-faker:
            datatype.number:
              min: 1
              max: 70
        description:
          type: string
          x-faker: commerce.productDescription
        disposition:
          type: string
          enum:
            - onePlusKk
            - onePlusOne
            - twoPlusKk
            - twoPlusOne
            - threePlusKk
            - threePlusOne
            - fourPlusKk
            - fourPlusOne
            - fivePlusOne
            - fivePlusKk
            - sixAndMore
            - atypical
            - null
          nullable: true
        houseType:
          type: string
          enum:
            - cottage
            - monumentOther
            - familyHouse
            - villa
            - turnKey
            - chalet
            - agriculturalFarm
            - multiGeneration
            - null
          nullable: true
        geoCoordinates:
          $ref: "#/components/schemas/GeoCoordinates"
        photos:
          type: array
          items:
            $ref: "#/components/schemas/Photo"
        rentalPrice:
          oneOf:
            - $ref: "#/components/schemas/PriceResponse"
          nullable: true
          x:
            faker:
              datatype.number:
                min: 1
                max: 10000
        isNoCommission:
          type: boolean
        depositPrice:
          oneOf:
            - $ref: "#/components/schemas/PriceResponse"
          nullable: true
          x:
            faker:
              datatype.number:
                min: 1
                max: 10000
        monthlyFeesPrice:
          oneOf:
            - $ref: "#/components/schemas/PriceResponse"
          nullable: true
          x:
            faker:
              datatype.number:
                min: 1
                max: 10000
        priceUnit:
          type: string
          enum:
            - perRealEstate
            - perMonth
            - perSqM
            - perSqMPerMonth
            - perSqMPerYear
            - perYear
            - perDay
            - perHour
            - perSqMPerDay
            - perSqMPerHour
          nullable: true
        published:
          type: string
          format: date-time
        seo:
          type: string
          x-faker: internet.domainWord
        street:
          oneOf:
            - $ref: "#/components/schemas/Location"
          nullable: true
        village:
          $ref: "#/components/schemas/Location"
        villagePart:
          oneOf:
            - $ref: "#/components/schemas/Location"
          nullable: true
        convenience:
          type: array
          items:
            type: string
            enum:
              - balcony
              - loggia
              - terrace
              - washingMachine
              - fridge
              - dishwasher
              - cellar
              - garden
              - lift
        houseConvenience:
          type: array
          items:
            type: string
            enum:
              - cellar
              - garage
              - pool
              - parking
              - wheelchairAccessible
        floorLevel:
          type: integer
          minimum: -5
          example: 1
          nullable: true
          x-faker:
            datatype.number:
              min: -5
              max: 10
        availableFrom:
          type: string
          format: date
          nullable: true
        priceNote:
          type: string
          nullable: true
          x-faker: commerce.productDescription
        offerType:
          type: string
          enum:
            - sale
            - rent
            - auctions
            - coliving
        propertyType:
          type: string
          enum:
            - flat
            - house
            - land
            - commercial
            - other
        adminUrl:
          type: string
          nullable: true
          x-faker: internet.url
        showScamWarn:
          type: boolean
          example: true
        isContacted:
          type: boolean
          example: true
        isTop:
          type: boolean
          example: true
        absoluteUrl:
          type: string
          x-faker: internet.url
      type: object
    ParameterSorting:
      required:
        - sorting
      properties:
        sorting:
          type: App\Presentation\Api\V1\Offer\Enum\Sorting
      type: object
    PriceRange:
      required: []
      properties:
        min:
          type: integer
          minimum: 0
          x-faker:
            datatype.number:
              min: 1
              max: 1000
        max:
          type: integer
          minimum: 0
          x-faker:
            datatype.number:
              min: 1
              max: 1000
      type: object
    LatestRequest:
      required:
        - propertyType
        - offerType
      properties:
        propertyType:
          type: string
          enum:
            - flat
            - house
            - land
            - commercial
            - other
        offerType:
          type: string
          enum:
            - sale
            - rent
            - auctions
            - coliving
      type: object
    LatestResponse:
      required:
        - offers
      properties:
        offers:
          type: array
          items:
            $ref: "#/components/schemas/Offer"
      type: object
    OfferRelated:
      required:
        - id
        - title
        - area
        - description
        - photos
        - published
        - seo
        - village
        - convenience
        - houseConvenience
        - offerType
        - propertyType
      properties:
        id:
          type: integer
          x-faker:
            datatype.number:
              min: 1
              max: 1000
        title:
          type: string
        area:
          type: integer
          x-faker:
            datatype.number:
              min: 1
              max: 70
        description:
          type: string
          x-faker: commerce.productDescription
        disposition:
          type: string
          enum:
            - onePlusKk
            - onePlusOne
            - twoPlusKk
            - twoPlusOne
            - threePlusKk
            - threePlusOne
            - fourPlusKk
            - fourPlusOne
            - fivePlusOne
            - fivePlusKk
            - sixAndMore
            - atypical
            - null
          nullable: true
        houseType:
          type: string
          enum:
            - cottage
            - monumentOther
            - familyHouse
            - villa
            - turnKey
            - chalet
            - agriculturalFarm
            - multiGeneration
            - null
          nullable: true
        photos:
          type: array
          items:
            $ref: "#/components/schemas/Photo"
        rentalPrice:
          oneOf:
            - $ref: "#/components/schemas/PriceResponse"
          nullable: true
          x:
            faker:
              datatype.number:
                min: 1
                max: 10000
        commissionPrice:
          type: boolean
          nullable: true
          x-faker:
            datatype.number:
              min: 1
              max: 10000
        depositPrice:
          oneOf:
            - $ref: "#/components/schemas/PriceResponse"
          nullable: true
          x:
            faker:
              datatype.number:
                min: 1
                max: 10000
        monthlyFeesPrice:
          oneOf:
            - $ref: "#/components/schemas/PriceResponse"
          nullable: true
          x:
            faker:
              datatype.number:
                min: 1
                max: 10000
        published:
          type: string
          format: date-time
        seo:
          type: string
          x-faker: internet.domainWord
        street:
          oneOf:
            - $ref: "#/components/schemas/Location"
          nullable: true
        village:
          $ref: "#/components/schemas/Location"
        villagePart:
          oneOf:
            - $ref: "#/components/schemas/Location"
          nullable: true
        convenience:
          type: array
          items:
            type: string
            enum:
              - balcony
              - loggia
              - terrace
              - washingMachine
              - fridge
              - dishwasher
              - cellar
              - garden
              - lift
        houseConvenience:
          type: array
          items:
            type: string
            enum:
              - cellar
              - garage
              - pool
              - parking
              - wheelchairAccessible
        floorLevel:
          type: integer
          minimum: -5
          example: 1
          nullable: true
          x-faker:
            datatype.number:
              min: -5
              max: 10
        availableFrom:
          type: string
          format: date
          nullable: true
        priceNote:
          type: string
          nullable: true
          x-faker: commerce.productDescription
        offerType:
          type: string
          enum:
            - sale
            - rent
            - auctions
            - coliving
        propertyType:
          type: string
          enum:
            - flat
            - house
            - land
            - commercial
            - other
      type: object
    RelatedData:
      required:
        - offers
      properties:
        offers:
          type: array
          items:
            $ref: "#/components/schemas/OfferRelated"
      type: object
    RelatedResponse:
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: "#/components/schemas/RelatedData"
      type: object
    GeoCoordinates:
      required:
        - lat
        - lng
      properties:
        lat:
          type: number
          x-faker: address.latitude
        lng:
          type: number
          x-faker: address.longitude
      type: object
    Photo:
      required:
        - id
        - path
        - alt
      properties:
        id:
          type: integer
          x-faker:
            datatype.number:
              min: 1
              max: 1000
        path:
          type: string
          x-faker: image.animals
        alt:
          type: string
      type: object
    PriceFormatedResponse:
      required:
        - title
        - value
      properties:
        title:
          type: string
          x-faker:
            datatype.number:
              min: 1
              max: 1000
        value:
          type: string
          x-faker: faker.finance.currencyName
      type: object
    PriceResponse:
      required:
        - value
        - currency
      properties:
        value:
          type: integer
          x-faker:
            datatype.number:
              min: 1
              max: 1000
        currency:
          type: string
          x-faker: faker.finance.currencyName
      type: object
    StatusChangeRequest:
      required:
        - offerId
        - status
      properties:
        offerId:
          type: integer
        status:
          type: string
          enum:
            - activate
            - deactivate
            - delete
      type: object
    StatusChangeResponse:
      required:
        - success
      properties:
        success:
          type: boolean
      type: object
    PhoneCheckData:
      required:
        - verified
      properties:
        verified:
          type: boolean
      type: object
    PhoneCheckResponse:
      required:
        - success
        - data
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/PhoneCheckData"
      type: object
    PhoneResendData:
      required:
        - id
      properties:
        id:
          type: string
        nextSmsAllowedTime:
          type: integer
      type: object
    PhoneResendRequest:
      required:
        - sentSmsId
      properties:
        phone:
          $ref: "#/components/schemas/Phone"
        sentSmsId:
          type: string
      type: object
    PhoneResendResponse:
      required:
        - success
        - data
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/PhoneResendData"
      type: object
    PhoneSendData:
      required:
        - id
      properties:
        id:
          type: string
        nextSmsAllowedTime:
          type: integer
      type: object
    PhoneSendRequest:
      required:
        - phone
      properties:
        phone:
          $ref: "#/components/schemas/Phone"
      type: object
    PhoneSendResponse:
      required:
        - success
        - data
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/PhoneSendData"
      type: object
