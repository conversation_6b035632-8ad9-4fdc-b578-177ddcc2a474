// yarn cypress run --headless --spec "cypress/e2e/checkFeElements.cy.ts"

import { faker } from "@faker-js/faker";

import { urls } from "../constants/ulrs";
import * as homepage from "../pages/homepage";
import * as core from "../pages/core";
import * as search from "../pages/search";
import { json2array } from "../helper/helper";

describe("Check visibility of key elements", () => {
  it(`TC001 | Check ${urls.HOME} as visitor`, () => {
    cy.visit(urls.HOME);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    homepage.searchBanner();
    cy.scrollTo("bottom");
    homepage.newOffers();
    homepage.favouriteCities();
    homepage.noProvision();
    homepage.ourServices();
    homepage.blogBanner();
    core.checkFooter();
  });

  it(`TC002 | Check ${urls.SEARCH} as visitor`, () => {
    cy.visit(urls.SEARCH);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    search.generalElementsCheck();
  });

  it(`TC003 | Check ${urls.ADD_ADV} as visitor`, () => {
    cy.visit(urls.ADD_ADV);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC004 | Check ${urls.TIPS.MAIN} as visitor`, () => {
    cy.visit(urls.TIPS.MAIN);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC005 | Check ${urls.TIPS.OWNER} as visitor`, () => {
    cy.visit(urls.TIPS.OWNER);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC006 | Check ${urls.TIPS.TENANT} as visitor`, () => {
    cy.visit(urls.TIPS.MAIN);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC007 | Check ${urls.BENEFITS.PRIVATE_ADV} as visitor`, () => {
    cy.visit(urls.BENEFITS.PRIVATE_ADV);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC008 | Check ${urls.BENEFITS.PREMIUM_ADV} as visitor`, () => {
    cy.visit(urls.BENEFITS.PREMIUM_ADV);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC009 | Check ${urls.BENEFITS.ADV_RISK_PERSONS} as visitor`, () => {
    cy.visit(urls.BENEFITS.ADV_RISK_PERSONS);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC010 | Check ${urls.CONTACTS} as visitor`, () => {
    cy.visit(urls.CONTACTS);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC011 | Check ${urls.PRICELIST} as visitor`, () => {
    cy.visit(urls.PRICELIST);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC012 | Check ${urls.RISK} as visitor`, () => {
    cy.visit(urls.RISK);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC013 | Check ${urls.DOG} as visitor`, () => {
    cy.visit(urls.DOG);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC014 | Check ${urls.SELF_EVALUE} as visitor`, () => {
    cy.visit(urls.SELF_EVALUE);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC015 | Check ${urls.GUIDE} as visitor`, () => {
    cy.visit(urls.GUIDE);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC016 | Check ${urls.PACKAGE} as visitor`, () => {
    cy.visit(urls.PACKAGE);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC017 | Check ${urls.TERMS} as visitor`, () => {
    cy.visit(urls.TERMS);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC018 | Check ${urls.PERSONAL_INFO} as visitor`, () => {
    cy.visit(urls.PERSONAL_INFO);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC019 | Check ${urls.TERMS_RO} as visitor`, () => {
    cy.visit(urls.TERMS_RO);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC020 | Check ${urls.PREMIUM.URL} as visitor`, () => {
    cy.visit(urls.PREMIUM.URL);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC021 | Check ${urls.PREMIUM.PAYMENT} as visitor`, () => {
    cy.visit(urls.PREMIUM.PAYMENT);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC022 | Check ${urls.DASHBOARD} as visitor`, () => {
    cy.visit(urls.DASHBOARD);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC023 | Check ${urls.TERMS_CC} as visitor`, () => {
    cy.visit(urls.TERMS_CC);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC024 | Check ${urls.CONTRACT} as visitor`, () => {
    cy.visit(urls.CONTRACT);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC025 | Check ${urls.PRICE_RO} as visitor`, () => {
    cy.visit(urls.PRICE_RO);
    core.waitForPageLoaded();
    core.checkNavBarVisitor();
    // here add another functions for page
    core.checkFooter();
  });
});

describe("Elements check as LOGED user", () => {
  const basicUser = {
    login: `${faker.word.noun().toLowerCase()}-${Math.round(
      Date.now() / 1000000,
    )}@ulovdomov.cz`,
    password: "testujeme",
  };

  before(() => {
    cy.apiRegister(basicUser);
  });

  beforeEach(() => {
    cy.session("Login as basic user", () => {
      cy.apiLogin(basicUser);
      cy.visit("/");
    });
  });

  it(`TC026 | Check ${urls.HOME} as user`, () => {
    cy.visit(urls.HOME);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    homepage.searchBanner();
    homepage.newOffers();
    homepage.favouriteCities();
    homepage.noProvision();
    homepage.ourServices();
    homepage.blogBanner();
    core.checkFooter();
  });

  it(`TC027 | Check ${urls.SEARCH} as user`, () => {
    cy.visit(urls.SEARCH);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    search.generalElementsCheck();
  });

  it(`TC028 | Check ${urls.ADD_ADV} as user`, () => {
    cy.visit(urls.ADD_ADV);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC029 | Check ${urls.TIPS.MAIN} as user`, () => {
    cy.visit(urls.TIPS.MAIN);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC030 | Check ${urls.TIPS.OWNER} as user`, () => {
    cy.visit(urls.TIPS.OWNER);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC031 | Check ${urls.TIPS.TENANT} as user`, () => {
    cy.visit(urls.TIPS.MAIN);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC032 | Check ${urls.BENEFITS.PRIVATE_ADV} as user`, () => {
    cy.visit(urls.BENEFITS.PRIVATE_ADV);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC033 | Check ${urls.BENEFITS.PREMIUM_ADV} as user`, () => {
    cy.visit(urls.BENEFITS.PREMIUM_ADV);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC034 | Check ${urls.BENEFITS.ADV_RISK_PERSONS} as user`, () => {
    cy.visit(urls.BENEFITS.ADV_RISK_PERSONS);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC035 | Check ${urls.CONTACTS} as user`, () => {
    cy.visit(urls.CONTACTS);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC036 | Check ${urls.PRICELIST} as user`, () => {
    cy.visit(urls.PRICELIST);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC037 | Check ${urls.RISK} as user`, () => {
    cy.visit(urls.RISK);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC038 | Check ${urls.DOG} as user`, () => {
    cy.visit(urls.DOG);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC039 | Check ${urls.SELF_EVALUE} as user`, () => {
    cy.visit(urls.SELF_EVALUE);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC040 | Check ${urls.GUIDE} as user`, () => {
    cy.visit(urls.GUIDE);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC041 | Check ${urls.PACKAGE} as user`, () => {
    cy.visit(urls.PACKAGE);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC042 | Check ${urls.TERMS} as user`, () => {
    cy.visit(urls.TERMS);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC043 | Check ${urls.PERSONAL_INFO} as user`, () => {
    cy.visit(urls.PERSONAL_INFO);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC044 | Check ${urls.TERMS_RO} as user`, () => {
    cy.visit(urls.TERMS_RO);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC045 | Check ${urls.PREMIUM.URL} as user`, () => {
    cy.visit(urls.PREMIUM.URL);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC046 | Check ${urls.PREMIUM.PAYMENT} as user`, () => {
    cy.visit(urls.PREMIUM.PAYMENT);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC047 | Check ${urls.DASHBOARD} as user`, () => {
    cy.visit(urls.DASHBOARD);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC048 | Check ${urls.TERMS_CC} as user`, () => {
    cy.visit(urls.TERMS_CC);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC049 | Check ${urls.CONTRACT} as user`, () => {
    cy.visit(urls.CONTRACT);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC050 | Check ${urls.PRICE_RO} as user`, () => {
    cy.visit(urls.PRICE_RO);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });

  it(`TC051 | Check ${urls.MY_OFFERS.URL} as user`, () => {
    cy.visit(urls.MY_OFFERS.URL);
    core.waitForPageLoaded();
    core.checkNavBarUser();
    // here add another functions for page
    core.checkFooter();
  });
});

describe(`Check most visited pages on DESKTOP`, () => {
  json2array(urls.COMMON).forEach((url: string) => {
    it(`Basic check ${url}`, () => {
      cy.visit(url);
      core.waitForPageLoaded();
      core.checkNavBarVisitor();
      search.generalElementsCheck();
    });
  });
});

describe(`Check most visited pages on MOBILE`, () => {
  json2array(urls.COMMON).forEach((url: string) => {
    it(`Basic check ${url}`, () => {
      cy.viewport(390, 844);
      cy.visit(url);
      core.waitForPageLoaded();
      core.checkNavBarVisitorMobile();
      search.generalElementsCheckMobile();
    });
  });
});
