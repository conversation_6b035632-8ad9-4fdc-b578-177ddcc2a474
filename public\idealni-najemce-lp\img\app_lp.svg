<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 40 40">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_2" data-name="Rectangle 2" width="40" height="40"/>
    </clipPath>
  </defs>
  <g id="app_40px" clip-path="url(#clip-path)">
    <g id="Group_111" data-name="Group 111" transform="translate(61.665 243.619)">
      <g id="Group_96" data-name="Group 96" transform="translate(-44.424 -227.848)">
        <path id="Path_92" data-name="Path 92" d="M0,12.533q.217,4.749,6.988,7.4,3.076-2.831,6.078-5.512A5.118,5.118,0,0,1,11.3,9.248q.713-2.8-1.911-5.722L6.309,0V7.224C4.456,5.876,2.759,2.206.852,3.882S2.475,8.228,3.23,9.291c-.116,1.82.4,3.241.155,3.241Z" fill="#fff" stroke="#212121" stroke-width="1"/>
      </g>
      <g id="Group_97" data-name="Group 97" transform="translate(-55.008 -242.973)">
        <path id="Path_93" data-name="Path 93" d="M.676,0H16.217a.676.676,0,0,1,.676.676V22.349c-2.284-1.93-3.936-4.967-5.58-3.216S13.875,24.5,13.875,24.5a6.476,6.476,0,0,0,.356,3.164H.676A.676.676,0,0,1,0,26.983V.676A.676.676,0,0,1,.676,0Z" fill="#fff" stroke="#212121" stroke-width="1"/>
      </g>
      <g id="Group_98" data-name="Group 98" transform="translate(-55.008 -242.973)">
        <path id="Path_94" data-name="Path 94" d="M.676,0H16.217a.676.676,0,0,1,.676.676V2.782a.676.676,0,0,1-.676.676H.676A.676.676,0,0,1,0,2.782V.676A.676.676,0,0,1,.676,0Z" fill="#fff" stroke="#212121" stroke-width="1"/>
      </g>
      <g id="Group_99" data-name="Group 99" transform="translate(-55.008 -219.853)">
        <path id="Path_95" data-name="Path 95" d="M.676,0h12.01a12.307,12.307,0,0,1,1.189,1.374,6.282,6.282,0,0,0,.109,2.341.676.676,0,0,1-.659.823H.676A.676.676,0,0,1,0,3.862V.676A.676.676,0,0,1,.676,0Z" fill="#fff" stroke="#212121" stroke-width="1"/>
      </g>
      <g id="Group_100" data-name="Group 100" transform="translate(-44.829 -236.491)">
        <path id="Path_96" data-name="Path 96" d="M0,0H3.682V3.889H0Z" fill="#fff" stroke="#212121" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_101" data-name="Group 101" transform="translate(-38.548 -214.234)">
        <path id="Path_97" data-name="Path 97" d="M0,7.333,8.07,0,10.4,2.606,2.325,9.94Z" fill="#fff" stroke="#212121" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <g id="Group_102" data-name="Group 102" transform="translate(-44.179 -224.39)">
        <path id="Path_98" data-name="Path 98" d="M6.422,12.317q-3.875-2.3-3.377-6.4.007-.049-2.4-2.9Q-.628,1.535.607.425T3.362,1.1L8.013,5.912" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
      </g>
      <g id="Group_103" data-name="Group 103" transform="translate(-52.301 -236.491)">
        <path id="Path_99" data-name="Path 99" d="M0,.64H4.981" transform="translate(0 -0.64)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
      </g>
      <g id="Group_104" data-name="Group 104" transform="translate(-52.301 -234.546)">
        <path id="Path_100" data-name="Path 100" d="M0,.64H4.981" transform="translate(0 -0.64)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
      </g>
      <g id="Group_105" data-name="Group 105" transform="translate(-52.301 -232.601)">
        <path id="Path_101" data-name="Path 101" d="M0,.64H4.981" transform="translate(0 -0.64)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
      </g>
      <g id="Group_106" data-name="Group 106" transform="translate(-52.301 -229.792)">
        <path id="Path_102" data-name="Path 102" d="M0,.64H11.262" transform="translate(0 -0.64)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
      </g>
      <g id="Group_107" data-name="Group 107" transform="translate(-52.301 -227.848)">
        <path id="Path_103" data-name="Path 103" d="M0,.64H11.262" transform="translate(0 -0.64)" fill="#fff" stroke="#212121" stroke-linecap="round" stroke-width="1"/>
      </g>
      <path id="Path_107" data-name="Path 107" d="M49.854-86.491a.973.973,0,0,0,.689-.284.973.973,0,0,0,.286-.688.973.973,0,0,0-.286-.688.973.973,0,0,0-.689-.284.973.973,0,0,0-.689.284.973.973,0,0,0-.286.688.973.973,0,0,0,.286.688.973.973,0,0,0,.689.284Z" transform="translate(-96.307 -130.12)" fill="#212121" fill-rule="evenodd"/>
    </g>
  </g>
</svg>
