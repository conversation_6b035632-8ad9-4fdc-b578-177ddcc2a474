import { GetServerSideProps, NextPage } from "next";
import { useEffect } from "react";

import { QueryKeys, QueryValues } from "@ud/config/queries";
import useRouter from "hooks/useRouter";
import { normalizeLocation } from "utils/normalizeLocation";

const Vyhledavani: NextPage = () => {
  const { replace } = useRouter();

  useEffect(() => {
    replace("/pronajem-bytu/custom");
  }, []);

  return null;
};

export default Vyhledavani;

const getOfferType = ({
  queryObject,
  type,
}: {
  queryObject: Array<string>;
  type: string;
}): string => {
  const offerType = type === "pronajem" ? "pronajem-bytu" : "spolubydleni";
  const sortingString = queryObject.find((q) => q.includes("radit_dle"));
  if (!sortingString) {
    return offerType;
  }

  const sorting = sortingString.replace("radit_dle=", "");
  if (sorting === "nejlevnejsi") {
    return offerType + "-levne";
  }

  return offerType + `-${sorting}`;
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  const { search: searchKeys } = QueryKeys;
  const { params, ...otherQueries } = query;
  const [type, location, ...restParams] = params as Array<string>;

  const queryObject = Object.entries(otherQueries).map(
    ([key, value]) => `${key}=${value}`,
  );

  const queryParams = queryObject
    .filter((s) => !s.includes("radit_dle"))
    .join("&");
  const newQuery = new URLSearchParams(queryParams);

  const editedLocation = normalizeLocation(location);

  const finalUrl = [getOfferType({ type, queryObject }), editedLocation];

  const dispositions = restParams.find((param) => param.includes("dispozice"));
  if (dispositions) {
    const dispos = dispositions.replace("dispozice-", "").split(".");
    finalUrl.push(`dispozice-${dispos.shift()}`);
    if (dispos.length > 1) {
      newQuery.append("dispozice", dispos.join(","));
    }
  }

  // furnishing
  if (newQuery.has(searchKeys["search.furnishing"])) {
    let furnishingStr = newQuery.get(searchKeys["search.furnishing"]) || "";
    furnishingStr = furnishingStr.replace("vubec", "bez-vybaveni");
    const furnishingItems = furnishingStr.split(";");

    if (furnishingItems.length === 1) {
      newQuery.delete(searchKeys["search.furnishing"]);
      if (furnishingItems[0] === "bez-vybaveni") {
        finalUrl.push(searchKeys[`search.furnishing.none`]);
      }
      if (furnishingItems[0] === "plne") {
        finalUrl.push(searchKeys[`search.furnishing.full`]);
      }
    }

    if (furnishingItems.length > 1) {
      finalUrl.push(
        `${searchKeys["search.furnishing.full"]}&${searchKeys["search.furnishing.none"]}`,
      );
      newQuery.set(searchKeys["search.furnishing"], furnishingItems.join(";"));
    }
  }

  const noCommission = restParams.find((param) =>
    param.includes("bez-provize"),
  );
  if (noCommission) {
    finalUrl.push("bez-provize");
  }

  // conveniences
  if (newQuery.has(searchKeys["search.conveniences"])) {
    let convenineces =
      newQuery.get(searchKeys["search.conveniences"])?.split(";") || [];

    const isBalcony = convenineces.find((item) => item === "balkon");

    if (isBalcony) {
      convenineces = convenineces.filter((item) => item !== "balkon");
      finalUrl.push(QueryValues.conveniences["conveniences.balcony"]);
    }

    if (convenineces.length) {
      newQuery.set(searchKeys["search.conveniences"], convenineces.join(";"));
    }
  }

  const queryString = newQuery.toString();

  return {
    redirect: {
      destination: `/${finalUrl.join("/")}${
        queryString
          ? `?${queryString}&location=${location}`
          : `?location=${location}`
      }`,
      permanent: true,
    },
  };
};
