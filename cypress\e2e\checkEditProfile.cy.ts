// yarn cypress run --headless --spec "cypress/e2e/checkEditProfile.cy.ts"

import { faker } from "@faker-js/faker";

import { urls } from "../constants/ulrs";
import { animals, prefix } from "../constants/data";
import * as settings from "../pages/settings";
import * as core from "../pages/core";
import * as api from "../pages/apiHelp";
import { exitModal } from "../helper/helper";

const basicUser = {
  login: `${faker.word.noun().toLowerCase()}-${Math.round(
    Date.now() / 1000000,
  )}@ulovdomov.cz`,
  login3: "<EMAIL>",
  password: "testujeme",
  name: faker.person.firstName(),
  surname: faker.person.lastName(),
  ocup: faker.company.buzzVerb(),
  info: faker.lorem.sentences(5),
  duration: faker.number.int({ max: 4 }),
  popleCount: faker.number.int({ max: 10 }),
  animal: animals[faker.number.int({ max: animals.length - 1 })],
  number: faker.phone.number("*********"),
  prefix: prefix[faker.number.int({ max: prefix.length - 1 })],
};

const picture = "profilePicture.png";
const button = "global.exitConsentModalButton";

describe("Edit My profile", () => {
  before(() => {
    cy.apiRegister(basicUser);
  });

  beforeEach(() => {
    cy.intercept("GET", "**/user/landlord-card").as("lanlordCard");
    cy.session("apiLogin", () => {
      cy.apiLogin(basicUser);
    });
    cy.visit("/");
  });

  it("Upload picture", () => {
    cy.visit(urls.SETTINGS);
    settings.goToMyLanlordProfilSettings();
    settings.uploadPicture(picture);
    exitModal(button); // natvrdo vyvolat consent a zavriet lebo sa trigruje nahodne
    settings.saveMyProfile();
  });

  it("Successful Edit name", () => {
    cy.visit(urls.SETTINGS);
    settings.goToMyLanlordProfilSettings();
    settings.fillMyProfile(basicUser.name, basicUser.surname);
    settings.saveMyProfile();
    cy.reload();
    core.waitForPageLoaded();
    // core.checkVisibleNameInHeader(`${basicUser.name} ${basicUser.surname}`);
  });

  it("Select animals, duration and people count", () => {
    cy.visit(urls.SETTINGS);
    settings.goToMyLanlordProfilSettings();
    settings.fillMyProfile(
      undefined,
      undefined,
      undefined,
      undefined,
      "2025",
      // basicUser.duration,
      basicUser.animal,
      basicUser.popleCount,
      basicUser.info,
    );
    settings.saveMyProfile();
    core.waitForPageLoaded();
  });

  it("Edit phone number", () => {
    cy.visit(urls.SETTINGS);
    settings.goToMyLanlordProfilSettings();
    settings.editPhoneNumber(basicUser.number, basicUser.prefix);
    settings.saveMyProfile();
    core.waitForPageLoaded();
  });

  it("Change password", () => {
    const newUserData = {
      login: basicUser.login,
      password: faker.internet.password(),
    };

    cy.visit(urls.SETTINGS);
    settings.goToMyLanlordProfilSettings();
    settings.changePassword(basicUser.password, newUserData.password);
    cy.clearAllLocalStorage().clearAllSessionStorage();
    cy.visit("/");
    cy.login(newUserData, true);
    api.changePassword(basicUser.password, newUserData.password);
  });

  it("Past Year to Start NOK", () => {
    cy.visit(urls.SETTINGS);
    settings.goToMyLanlordProfilSettings();
    settings.fillMyProfile(
      undefined,
      undefined,
      undefined,
      undefined,
      // "2021",
      undefined,
      undefined,
      undefined,
      undefined,
    );
    settings.checkWarningText();
  });
});
