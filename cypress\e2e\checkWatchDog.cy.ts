// yarn cypress run --headless --spec "cypress/e2e/checkWatchDog.cy.ts"

import { faker } from "@faker-js/faker";

import { urls } from "../constants/ulrs";
import * as dog from "../pages/dog";
import { dogInput } from "../constants/types";
import * as hp from "../pages/homepage";
import * as offer from "../pages/offer";
import * as search from "../pages/search";
import * as login from "../pages/login";
import { adressList, dispo } from "../constants/data";
import { waitForPageLoaded } from "../pages/core";

const basicUser = {
  login: `${faker.word.noun().toLowerCase()}-${Math.round(
    Date.now() / 1000000,
  )}@ulovdomov.cz`,
  // login: "<EMAIL>",
  password: "testujeme",
};

const adressDog: dogInput = {
  adress: adressList[Math.floor(Math.random() * adressList.length)],
  adress2: adressList[Math.floor(Math.random() * adressList.length)],
  disp: dispo[Math.floor(Math.random() * dispo.length)],
  min: "5000",
  max: "25000",
  name: faker.word.noun(),
  name2: faker.word.noun(),
  newName: faker.word.noun(),
  searchName: faker.word.noun(),
  newDispo: "4+1",
  newMin: faker.number.int({ min: 2500, max: 10000 }).toString(),
  newMax: faker.number.int({ min: 10001, max: 25000 }).toString(),
};

describe("Watch dog as registered user", () => {
  before(() => {
    cy.apiRegister(basicUser);
  });

  beforeEach(() => {
    cy.session("apiLogin", () => {
      cy.apiLogin(basicUser);
    });
    cy.visit("/");
  });

  it("Check Basic Dog without any data", () => {
    cy.visit(urls.DOG);
    dog.emptyDogElementsCheck();
  });

  it("Add watch dog", () => {
    cy.visit(urls.DOG);
    dog.typeAdressIntoSearch(adressDog.adress);
    dog.selectSpecificsOfDog(adressDog.disp, adressDog.min, adressDog.max);
    dog.nameMyDog(adressDog.name);
    dog.selectDogNotification("daily");
    dog.saveDog();
    dog.confirmMyDogList(adressDog.name);
  });

  it("Edit watchdog", () => {
    cy.visit(urls.DOG);
    dog.editdogWatch(
      adressDog.name,
      adressDog.newName,
      adressDog.disp,
      adressDog.newDispo,
      adressDog.newMin,
      adressDog.newMax,
    );
    dog.confirmMyDogList(adressDog.newName);
  });

  it("Show offers", () => {
    cy.visit(urls.DOG);
    dog.visitDogOffers(adressDog.newName);
    dog.resultsDogOffers(adressDog.newName);
  });

  it("Delete watchdog", () => {
    cy.visit(urls.DOG);
    dog.deleteDogWatch(adressDog.newName);
  });

  it("Create watchdog from offer", () => {
    cy.visit("/");
    hp.goToRandomPragueOffer();
    offer.createWatchDogFromOffer();
    dog.createWatchDogByModal();
    dog.confirmDogCreatedByModal();
  });

  it.skip("Create watchdog from search promo", () => {
    cy.intercept("POST", "**/offerCount").as("offerCount0");
    cy.visit(
      `${urls.SEARCH_SPEC}${adressDog.adress
        .replace(/\s+/g, "-")
        .replace(/[\u0300-\u036f]/g, "")
        .toLowerCase()}?location=${adressDog.adress
        .replace(/\s+/g, "+")
        .replace(/[\u0300-\u036f]/g, "")}`,
    );
    waitForPageLoaded();
    search.makeSpecificSearchOpenDogModal("promo");
    dog.searchDogModal(adressDog.searchName);
    cy.visit(urls.DOG);
    dog.confirmMyDogList(adressDog.searchName);
  });

  it.skip("Create watchdog from search with data", () => {
    cy.intercept("POST", "**/offerCount").as("offerCount0");
    cy.visit(
      `${urls.SEARCH_SPEC}${adressDog.adress
        .replace(/\s+/g, "-")
        .replace(/[\u0300-\u036f]/g, "")
        .toLowerCase()}?location=${adressDog.adress
        .replace(/\s+/g, "+")
        .replace(/[\u0300-\u036f]/g, "")}`,
    );
    waitForPageLoaded();
    search.makeSpecificSearchOpenDogModal("search");
    dog.searchDogModal(adressDog.searchName);
  });
});

describe("Watchdog when not loged in", () => {
  it("Create Watch dog when Not", () => {
    const inHousing = "signUpModal.userType.inHousing";
    const randomMail = faker.internet.email();

    cy.intercept("POST", "**/fe-api/user/email-exists").as("email-exist");
    cy.intercept("POST", "**/api/v1/user").as("email-reg");

    cy.visit(urls.DOG);
    dog.typeAdressIntoSearch("Zlín");
    dog.selectSpecificsOfDog(adressDog.disp, adressDog.min, adressDog.max);
    dog.nameMyDog(adressDog.name2);
    dog.saveDog(false);
    login.fillLoginMail(randomMail);
    login.continueToPassword();
    login.fillRegPassword();
    login.clickRegRadioButton(inHousing);
    login.submitRegistration(randomMail);
    dog.saveDog();
    dog.confirmMyDogList(adressDog.name2);
  });

  it("Try create watchdog from offer", () => {
    cy.visit("/");
    hp.goToRandomPragueOffer();
    offer.createWatchDogFromOffer();
    dog.checkLoginModal();
  });
});
