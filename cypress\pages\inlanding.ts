import { json2array } from "../helper/helper";

const inUrl = "https://idealninajemce.cz/doporucit-majitele";
const inRentUrl = "https://idealninajemce.cz/naceneni-najmu";

const _cities: any = {
  brno: "347",
  praha: "6253",
  olomouc: "3447",
  ostrava: "3517",
  plzen: "3754",
  pardubice: "3612",
  hradec: "1335",
  cBudejovice: "646",
};

const randomCity =
  _cities[
    Object.keys(_cities)[
      Math.floor(Math.random() * Object.keys(_cities).length)
    ]
  ];

const el = {
  mainPage: {
    image: "in.landing.image",
    image2: "in.landing.image2",
    header: "in.landing.header",
    headerUD: "in.landing.header.ud",
    text: "in.landing.text",
    strongText: "in.landing.strongText",
    scrollBtn: "in.landing.scrollButton",
    stats: "in.landing.stats",
    infoCards: "in.landing.infoCards",
  },
  form: {
    form: "in.landing.form",
    name: "in.landing.form.name",
    city: "in.landing.form.city",
    phone: "in.landing.form.phone",
    email: "in.landing.form.email",
    checkBox: "in.landing.form.checkBox",
    disclaimer: "in.landing.form.disclaimer",
    submitBtn: "in.landing.form.submitBtn",
  },
  succesForm: {
    form: "in.landing.succesForm",
    succesIcon: "in.landing.successIcon",
    sentHeader: "in.landing.sentHeader",
    sentTxt: "in.landing.sentTxt",
    redirectBtn: "in.landing.redirectBtn",
  },
  errorForm: {
    form: "in.landing.errorForm",
    errorIcon: "in.landing.errorIcon",
    errorHeading: "in.landing.errorHeading",
    errorTxt: "in.landing.errorTxt",
    supportBtn: "in.landing.supportBtn",
  },
  succesLoader: {
    loader: "in.landing.successLoader",
    load: "in.landing.formLoad",
  },
  gridWrapper: {
    header: "in.landing.headerAZ",
    wrapper: "in.landing.gridWrapper",
  },
  variants: {
    header: "in.landing.headerVariants",
    wrapper: "in.landing.pricingWrapper",
    profitBtn: "in.landing.profitBtn",
    balancBtn: "in.landing.balancBtn",
    fixBtn: "in.landing.fixBtn",
  },
  consultation: {
    div: "in.landing.consultation",
    // icon: "in.landing.csnIcon", cypress effective width wtf
    header: "in.landing.csnHeader",
    txt: "in.landing.csnTxt",
    meetUp: "in.landing.meetUp",
  },
  reccomend: {
    header: "in.landing.recommendHeader",
    txt: "in.landing.recommendTxt",
    strongText: "in.landing.recommendStrong",
    strongText2: "in.landing.recommendStrong2",
    btn: "in.landing.recommendBtn",
  },
};

export function checkMainPageElements() {
  json2array(el.mainPage).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function checkMainPageElementsMobile() {
  const elements = [
    el.mainPage.header,
    el.mainPage.headerUD,
    el.mainPage.infoCards,
    el.mainPage.scrollBtn,
    el.mainPage.stats,
    el.mainPage.strongText,
    el.mainPage.text,
  ];

  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

export function checkFormElements() {
  json2array(el.form).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function checkSuccesFormElements() {
  json2array(el.succesForm).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

function checkErrorFormElements() {
  json2array(el.errorForm).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function checkSuccessLoaderElements() {
  json2array(el.succesLoader).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function checkGridWapperElements() {
  json2array(el.gridWrapper).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function checkVariantsElements() {
  json2array(el.variants).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function checkConsultationsElements() {
  json2array(el.consultation).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function checkRecommendElements() {
  json2array(el.reccomend).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function checkScrollBtns(button: string) {
  cy.getByTestId(button).click({ force: true });
  cy.scrollTo("bottom");
}

export function checkScrollBtnsMobile(button: string) {
  cy.getByTestId(button).click({ force: true });
  cy.scrollTo("bottom");
}

export function checkRedirectLink() {
  cy.getByTestId(el.reccomend.btn)
    .invoke("attr", "href")
    .then(() => {
      cy.getByTestId(el.reccomend.btn)
        .should("have.attr", "href")
        .and("include", inUrl);
    });
}

export function fillBadData(data: any) {
  cy.getByTestId(el.form.name).clear().type(data.name);
  // cy.selectOption(el.form.city, randomCity, true);
  cy.getByTestId(el.form.phone).clear().type(data.phone);
  cy.getByTestId(el.form.email).clear().type(data.email);
  cy.getByTestId(el.form.submitBtn).click();
  checkErrorFormElements();
}

export function fillData(data: any) {
  cy.getByTestId(el.form.name).clear().type(data.name);
  cy.selectOption(el.form.city, randomCity, true);
  cy.getByTestId(el.form.phone).clear().type(data.phone);
  cy.getByTestId(el.form.email).clear().type(data.email);
  cy.getByTestId(el.form.submitBtn).click();
  // checkSuccessLoaderElements(); neviem kedy sa trigruje ani co to je
  checkSuccesFormElements();
  cy.getByTestId(el.reccomend.btn)
    .invoke("attr", "href")
    .then(() => {
      cy.getByTestId(el.succesForm.redirectBtn)
        .should("have.attr", "href")
        .and("include", inRentUrl);
    });
}
