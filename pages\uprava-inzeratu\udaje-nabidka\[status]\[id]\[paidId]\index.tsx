import { GetServerSideProps } from "next/types";

import InsertOfferWrapper from "components/InsertOfferWrapper";
import InfoOfferForm from "scenes/InsertOffer/form/InfoOffer";
import { CustomAppPage, EditOfferPaid } from "src/types/types";

const InfoOffer: CustomAppPage<EditOfferPaid> = (props) => (
  <InsertOfferWrapper title="Údaje o nabídce" {...props}>
    <InfoOfferForm />
  </InsertOfferWrapper>
);

InfoOffer.hasHiddenFooter = true;

export default InfoOffer;

export const getServerSideProps: GetServerSideProps = async ({ query }) => ({
  props: {
    id: query.id as string,
    status: query.status as string,
    paidId: query.paidId as string,
  },
});
