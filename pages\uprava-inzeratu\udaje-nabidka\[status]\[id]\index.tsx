import { GetServerSideProps } from "next/types";

import InsertOfferWrapper from "components/InsertOfferWrapper";
import InfoOfferForm from "scenes/InsertOffer/form/InfoOffer";
import { CustomAppPage, EditOfferUnpaid } from "src/types/types";

const InfoOffer: CustomAppPage<EditOfferUnpaid> = (props) => (
  <InsertOfferWrapper title="Údaje o nabídce" {...props}>
    <InfoOfferForm />
  </InsertOfferWrapper>
);

InfoOffer.hasHiddenFooter = true;

export default InfoOffer;

export const getServerSideProps: GetServerSideProps = async ({ query }) => ({
  props: {
    id: query.id as string,
    status: query.status as string,
  },
});
