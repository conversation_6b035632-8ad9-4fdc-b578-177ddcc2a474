import { GetServerSideProps, NextPage } from "next";
import {
  getStoryblok<PERSON><PERSON>,
  StoryblokComponent,
  StoryData,
} from "@storyblok/react";
import { captureException } from "@sentry/nextjs";
import axios from "axios";

import OfferDetail from "scenes/OfferDetail";
import useRouter from "hooks/useRouter";
import { DetailResponse } from "@ud/api/newPhpApi/__generated__/newPhpApi.generated";

const Index: NextPage<{
  offer: DetailResponse;
  story: StoryData | null;
}> = ({ offer, story }) => {
  const { isFallback } = useRouter();

  const storyBlok = story ? <StoryblokComponent blok={story.content} /> : null;

  return (
    <OfferDetail
      offer={offer.data}
      isFallback={isFallback}
      blogComponents={storyBlok}
    />
  );
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const storyblokApi = getStoryblokApi();
  const apiParams = {
    version: context.preview ? "draft" : undefined,
    token: context.preview
      ? process.env.NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN
      : undefined,
    resolve_relations: ["root_card.root"],
  };

  let resStory;
  try {
    resStory = await storyblokApi.get("cdn/stories/offer-detail", apiParams);
  } catch (err) {
    captureException(err);
  }

  const { params } = context;
  const idAsString = params?.id;

  if (!parseInt(idAsString?.toString() || "NaN")) {
    return { notFound: true };
  }

  const id = parseInt(idAsString?.toString() ?? "0");

  try {
    const res = await axios.get(
      `${process.env.NEXT_PUBLIC_NEW_PHP_API_URL}/v1/offer/detail?offerId=${id}`,
    );
    const offer = res.data;

    if (offer) {
      return {
        props: {
          offer,
          story: resStory?.data.story || null,
        },
      };
    }

    return { notFound: true };
  } catch (error) {
    return {
      redirect: {
        destination: `/muj-inzerat-rip/${id}`,
        permanent: false,
      },
    };
  }
};

export default Index;
