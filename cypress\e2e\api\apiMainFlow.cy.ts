import { faker } from "@faker-js/faker";

import { getUdBeApiUrl } from "../../helper/helper";

describe("Main FLOW Login Check", () => {
  const basicUser = {
    login: `${faker.word.noun().toLowerCase()}-${Math.round(
      Date.now() / 1000000,
    )}@ulovdomov.cz`,
    password: "testujeme",
  };
  let accessToken: string;

  before(() => {
    cy.apiRegister(basicUser);
  });

  it("Current Check", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
        // refreshToken = resp.body.refreshToken;
      })
      .then(() => {
        cy.request({
          method: "GET",
          url: `${getUdBeApiUrl()}fe-api/user/current`,
          headers: { authorization: `Bearer ${accessToken}` },
        }).then((resp) => {
          expect(resp.body).to.have.property("additionalSearchSortBy");
          expect(resp.body).to.have.property("additionalSearchSortBy");
          expect(resp.body).to.have.property("disableFavorite");
          expect(resp.body).to.have.property("email");
          expect(resp.body).to.have.property("favorite");
          expect(resp.body).to.have.property("firstName");
          expect(resp.body).to.have.property("id");
          expect(resp.body).to.have.property("isSelfCheck");
          expect(resp.body).to.have.property("lastName");
          expect(resp.body).to.have.property("menuItemsToDisplay");
          expect(resp.body.menuItemsToDisplay).to.have.property(
            "isSocialLogin",
          );
          expect(resp.body.menuItemsToDisplay).to.have.property(
            "menuCcReports",
          );
          expect(resp.body.menuItemsToDisplay).to.have.property("myInbox");
          expect(resp.body.menuItemsToDisplay).to.have.property(
            "settingsPasswordOld",
          );
          expect(resp.body.menuItemsToDisplay).to.have.property(
            "settingsRenterCard",
          );
          expect(resp.body.menuItemsToDisplay).to.have.property("topBoard");
          expect(resp.body.menuItemsToDisplay).to.have.property("topBoardBrno");
          expect(resp.body).to.have.property("notifications");
          expect(resp.body.notifications).to.have.property("myOffers");
          expect(resp.body.notifications).to.have.property("myRents");
          expect(resp.body.notifications).to.have.property("watchdog");
          expect(resp.body).to.have.property("owns");
          expect(resp.body).to.have.property("phoneNumberDrawerTill");
          expect(resp.body).to.have.property("photoPath");
          expect(resp.body).to.have.property("premiumAccTill");
          expect(resp.body).to.have.property("profileScore");
          expect(resp.body).to.have.property("selfCheckTill");
          expect(resp.body).to.have.property("walletCreditCheckCredits");

          expect(resp.body.email).to.equal(basicUser.login);
        });
      });
  });

  it("Rented Card", () => {
    cy.request({
      method: "POST",
      url: `${getUdBeApiUrl()}fe-api/auth/login`,
      body: {
        email: basicUser.login,
        password: basicUser.password,
      },
    })
      .then((resp) => {
        expect(resp.status).equal(200);
        expect(resp.body).to.have.property("accessToken");
        expect(resp.body).to.have.property("refreshToken");
        expect(resp.body.accessToken).to.have.length.above(260);
        expect(resp.body.refreshToken).to.have.length.above(450);
        accessToken = resp.body.accessToken;
        // refreshToken = resp.body.refreshToken;
      })
      .then(() => {
        cy.request({
          method: "GET",
          url: `${getUdBeApiUrl()}fe-api/user/renter-card`,
          headers: { authorization: `Bearer ${accessToken}` },
        }).then((resp) => {
          expect(resp.body).to.have.property(
            "msg_after_contact_unfilled_close",
          );
          expect(resp.body).to.have.property("photo_url");
        });
      });
  });
});
