import { json2array } from "../helper/helper";
import { waitForPageLoaded } from "./core";

const el = {
  search: {
    searcBck: "search.background.image",
    content: "search.content",
    title: "search.content.title",
    subtitle: "search.content.subtitle",
    cities: "search.content.cities",
    praha: "search.content.cities.Praha",
    brno: "search.content.cities.Brno",
    ostrava: "search.content.cities.Ostrava",
    olomouc: "search.content.cities.Olomouc",
    plzen: "search.content.cities.Plzen",
    liberec: "search.content.cities.Liberec",
    pardubice: "search.content.cities.Pardubice",
    zlin: "search.content.cities.Zlin",
    searchBox: "search.content.searchBox",
  },
  newOffers: {
    prague: "newOffersPrague",
    pragueTitle: "newOffersPrague.title",
    pragueResults: "newOffersPrague.results",
    pragueOffer: "newOffersPrague.results.offerCard",
    brno: "newOffersBrno",
    brnoTitle: "newOffersBrno.title",
    brnoResults: "newOffersBrno.results",
    brnoOffer: "newOffersBrno.results.offerCard",
  },
  favouriteCities: {
    praha: "favouriteCities.Praha",
    brno: "favouriteCities.Brno",
    ostrava: "favouriteCities.Ostrava",
    olomouc: "favouriteCities.Olomouc",
    plzen: "favouriteCities.Plzeň",
    zlin: "favouriteCities.Zlín",
    liberec: "favouriteCities.Liberec",
  },
  noProvision: {
    nooffers: "newOffersNoProvision",
    title: "newOffersNoProvision.title",
    results: "newOffersNoProvision.results",
    offerCard: "newOffersNoProvision.results.offerCard",
    image: "newOffersNoProvision.results.offerCard.image",
    locality: "newOffersNoProvision.results.offerCard.locality",
    disposition: "newOffersNoProvision.results.offerCard.disposition",
    price: "newOffersNoProvision.results.offerCard.price",
  },
  ourServices: {
    bg: "ourServices.background",
    content: "ourServices.content",
    title: "ourServices.content.title",
    cards: "ourServices.content.cards",
    watchdog: "ourServices.content.cards.watchdog",
    premium: "ourServices.content.cards.premium",
    cc: "ourServices.content.cards.cc",
  },
  blogBanner: {
    blog: "blog-wrapper",
    // allArticlesButton: "blog-allArticles-button", --> we need dev help where to put this selector
    title: "blog-h2",
    posts: "blog-posts",
    postSpecific: "blog-post-specific",
    img: "blog-post-img",
    articleTitle: "blog-article-title",
  },
};

export function searchBanner() {
  json2array(el.search).forEach((element: any) => {
    cy.getByTestId(`homepage.${element}`).should("be.visible");
  });
}

export function newOffers() {
  json2array(el.newOffers).forEach((element: any) => {
    cy.getByTestId(`homepage.${element}`).should("be.visible");
  });
}
export function favouriteCities() {
  json2array(el.favouriteCities).forEach((element: any) => {
    cy.getByTestId(`homepage.${element}`).should("be.visible");
  });
}
export function noProvision() {
  json2array(el.noProvision).forEach((element: any) => {
    cy.getByTestId(`homepage.${element}`).should("be.visible");
  });
}
export function ourServices() {
  json2array(el.ourServices).forEach((element: any) => {
    cy.getByTestId(`homepage.${element}`).should("be.visible");
  });
}
export function blogBanner() {
  cy.get("footer").scrollIntoView(); // blogger wrapper sa nerendruje kym sa nan nescrollne
  json2array(el.blogBanner).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function goToRandomPragueOffer() {
  let numberOfOffers: number;
  cy.intercept("GET", "**/related").as("related");
  cy.getByTestId(`homepage.${el.newOffers.pragueResults}`)
    .within(() => {
      cy.getByTestId(`homepage.${el.newOffers.pragueOffer}`).then((offers) => {
        numberOfOffers = offers.length - 1;
      });
    })
    .then(() => {
      cy.getByTestId(`homepage.${el.newOffers.pragueOffer}`)
        .eq(numberOfOffers)
        .click();
    });
  waitForPageLoaded();
  cy.wait("@related")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.equal(200);
    });
}
