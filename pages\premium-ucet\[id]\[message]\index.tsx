import React from "react";
import { NextPage } from "next";

import Profile from "scenes/Profile/Profile";
import useRouter from "hooks/useRouter";

const Index: NextPage = () => {
  const router = useRouter();

  let paymentStatus: "success" | "error" | undefined;
  if (router.query?.message === "dekujeme-za-platbu-za-premium-ucet") {
    paymentStatus = "success";
  } else if (router.query?.message === "platba-se-nezdarila") {
    paymentStatus = "error";
  }

  return <Profile paymentStatus={paymentStatus} />;
};

export default Index;
