// @ts-nocheck
import pagesWithDataTests from "../fixtures/pages.json";

Cypress.on("uncaught:exception", (err) => {
  // ignor konkrétních errorů podle hl<PERSON>
  if (err.message.includes("")) {
    return false;
  }
  if (err.message.includes("Script error.")) {
    return false;
  }

  // return false; na konci bude ignorovat všechny uncaught exceptions errory
});

const getByDataTest = (dataTest) => cy.get(`[data-test="${dataTest}"]`);

const executeString = (codeToExecute) => {
  Function(`"use strict";return (${codeToExecute})`)();
};

// Toto je první test. Vytváří si testy nad každou stránkou, kde postupně kontroluje,
// zda-li všechny data-testy stále existují.
describe("Basic test to see if data-tests are still present", () => {
  pagesWithDataTests.pages.forEach((page) => {
    it(`Now testing ${page.name}`, () => {
      cy.visit(page.url);
      page.dataTestsWithTriggers.forEach((dataTestsWithTrigger) => {
        // někdy je na zobrazení data-testu potřeba udělat nějakou akci
        // tyto akce jsou uložené spolu s data-testy v .jsonu
        const codeToExecute = dataTestsWithTrigger.trigger;
        if (codeToExecute !== "") {
          executeString(codeToExecute);
        }
        dataTestsWithTrigger.dataTests.forEach((dataTest) => {
          getByDataTest(page.name + "." + dataTest);
        });
      });
    });
  });
});
