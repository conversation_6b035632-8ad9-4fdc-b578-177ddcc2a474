import { faker } from "@faker-js/faker";

const el = {
  loginButton: "navbar.content.loginButton",
  passwordInput: "loginModal.signIn.form.passwordInput",
  signInButton: "loginModal.signIn.form.button",
  closeModalButton: "loginModal.closeButton",
  loginModal: {
    modal: "loginModal",
    closeButton: "loginModal.closeButton",
    identifi: "loginModal.identification",
    heading: "loginModal.identification.heading",
    text: "loginModal.identification.text",
    loginForm: "loginModal.identification.form",
    mailInput: "global.form.input", // shodne s signInModal.passInput
    continueButton: "loginModal.identification.form.button",
    agreements: "loginModal.identification.form.agreements",
  },
  signInModal: {
    inModal: "loginModal.signIn",
    heading: "loginModal.signIn.heading",
    text: "loginModal.signIn.text",
    signForm: "loginModal.signIn.form",
    passInput: "global.form.input", // nova promenna aby sa nemylilo
    passTextInput: "loginModal.signIn.form.passwordInput",
    loginButton: "loginModal.signIn.form.button",
    forgotButton: "loginModal.signIn.form.forgottenPasswordButton",
  },
  signUpModal: {
    upModal: "loginModal.signUp",
    heading: "loginModal.signUp.heading",
    text: "loginModal.signUp.text",
    form: "loginModal.signUp.form",
    newPassword: "global.form.input",
    pass: "loginModal.signUp.form.input",
    consent: "loginModal.signUp.form.consent",
    consentText: "loginModal.signUp.form.consent.text",
    gdpr: "loginModal.signUp.form.consent.text.gdpr",
    button: "loginModal.signUp.form.button",
    privateLanlord: "signUpModal.userType.privateLanlord",
    realEstate: "signUpModal.userType.realEstate",
    inHousing: "signUpModal.userType.inHousing",
  },
  forgotPassModal: {
    forgotPass: "loginModal.forgottenPassword",
    heading: "loginModal.forgottenPassword.heading",
    text: "loginModal.forgottenPassword.text",
    mail: "loginModal.forgottenPassword.mail",
    button: "loginModal.forgottenPassword.button",
    mailSent: "loginModal.forgottenPassword.mailSent",
    mailNotSent: "loginModal.forgottenPassword.mailNotSent",
  },
  navbarProfile: {
    avatar: "navbar.content.userAvatar",
    avatarImage: "navbar.content.userAvatar.image",
    avatarMail: "navbar.content.userAvatar.mail",
  },
  navbar: {
    button: "navbar.hamburgerButton",
    logOutButton: "navbar.sidebar.logoutButton",
    logOutIcon: "navbar.sidebar.logoutButton.icon",
    logOutText: "navbar.sidebar.logoutButton.text",
    logInButton: "navbar.sidebar.loginButton",
    myProfile: "navbar.sidebar.myProfile.text",
  },
  navbarLoginButton: "navbar.sidebar.loginButton",
  navbarMyProfileText: "navbar.sidebar.myProfile.text",
};

const isVisible = (elem: any) =>
  !!(elem.offsetWidth || elem.offsetHeight || elem.get(0).getClientRects());

function checkLoginModalElementsFirstStep() {
  const elements = [
    el.loginModal.modal,
    el.loginModal.closeButton,
    el.loginModal.identifi,
    el.loginModal.heading,
    el.loginModal.text,
    el.loginModal.loginForm,
    el.loginModal.mailInput,
    el.loginModal.continueButton,
    el.loginModal.agreements,
  ];
  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

function checkLoginModalElementsSecondStep() {
  const elements = [
    el.loginModal.modal,
    el.loginModal.closeButton,
    el.signInModal.inModal,
    el.signInModal.heading,
    el.signInModal.text,
    el.signInModal.signForm,
    el.signInModal.passInput,
    el.signInModal.passTextInput,
    el.signInModal.loginButton,
    el.signInModal.forgotButton,
  ];
  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

export function checkSignUpModalElements() {
  const elements = [
    el.signUpModal.button,
    el.signUpModal.consent,
    el.signUpModal.consentText,
    el.signUpModal.form,
    el.signUpModal.gdpr,
    el.signUpModal.heading,
    el.signUpModal.newPassword,
    el.signUpModal.pass,
    el.signUpModal.text,
    el.signUpModal.upModal,
  ];
  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

function checkNavbarProfileElements() {
  const elements = [
    el.navbarProfile.avatar,
    el.navbarProfile.avatarImage,
    el.navbarProfile.avatarMail,
  ];
  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

function checkNavbarElementsMobile() {
  const elements = [
    el.navbar.myProfile,
    el.navbar.logOutIcon,
    el.navbar.logOutButton,
    el.navbar.logOutText,
  ];
  elements.forEach((element) => {
    cy.getByTestId(element).should("be.visible");
  });
}

function checkHamburgerButton() {
  return cy.getByTestId(el.navbar.button).then((elem) => {
    expect(isVisible(elem)).to.be.true;
  });
}

function checkSidebarLogOutElements() {
  const elements = [
    el.navbar.logOutButton,
    el.navbar.logOutIcon,
    el.navbar.logOutText,
  ];
  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

export function checkForgottenPassElementsFirstStep() {
  const elements = [
    el.loginModal.modal,
    el.loginModal.closeButton,
    el.forgotPassModal.button,
    el.forgotPassModal.forgotPass,
    el.forgotPassModal.heading,
    el.forgotPassModal.mail,
    el.forgotPassModal.text,
  ];
  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

function checkForgottenPassElementsSecondStep() {
  const elements = [
    el.loginModal.modal,
    el.loginModal.closeButton,
    el.forgotPassModal.forgotPass,
    el.forgotPassModal.heading,
    el.forgotPassModal.text,
    el.forgotPassModal.mailSent,
  ];
  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

function checkForgottenPassElementsSecondStepNotSend() {
  const elements = [
    el.loginModal.modal,
    el.loginModal.closeButton,
    el.forgotPassModal.forgotPass,
    el.forgotPassModal.heading,
    el.forgotPassModal.text,
    el.forgotPassModal.mailNotSent,
  ];
  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

export function goToLoginModal() {
  cy.getByTestId(el.loginButton).click();
  checkLoginModalElementsFirstStep();
}

export function goToLoginModalMobile() {
  cy.getByTestId(el.navbar.button).click({ force: true });
  cy.getByTestId(el.navbar.logInButton).click();
  checkLoginModalElementsFirstStep();
}

export function fillLoginMail(value?: string) {
  const email: any = value !== undefined ? value : faker.word.noun();
  cy.get("#email").clear().type(email);
}

export function fillPassword(value?: string, optional?: boolean) {
  const pass: any = value !== undefined ? value : faker.internet.password();
  cy.getByTestId(el.passwordInput).clear().type(pass, { delay: 50 });
  cy.wait("@login-mail")
    .its("response")
    .then((resp) => {
      if (resp?.statusCode === 404) {
        cy.wait(500);
        cy.getByTestId(el.signUpModal.inHousing).click({ force: true });
        cy.getByTestId(el.signInButton).click({ force: true });
      } else if (optional) {
        cy.getByTestId(el.signInButton).click({ force: true });
      }
    });
}

export function enterBadDataToLogin(value?: string) {
  cy.getByTestId(el.loginModal.continueButton).should("be.visible").click();
  cy.getByTestId(el.loginModal.loginForm)
    .contains("Povinné pole")
    .should("be.visible");
  fillLoginMail(value);
  cy.getByTestId(el.loginModal.continueButton)
    .click()
    .get("input:invalid")
    .should("have.length", 1);
}

export function closeLoginModal() {
  cy.getByTestId(el.closeModalButton).click();
  cy.getByTestId(el.loginModal.modal).should("not.exist");
}

export function continueToPassword() {
  cy.getByTestId(el.loginModal.continueButton).click();
  cy.wait("@email-exist")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.be.oneOf([200, 404]); // 200 => pokracovanie na heslo, 404 => reg
      if (resp?.statusCode === 200) {
        checkLoginModalElementsSecondStep();
        cy.getByTestId(el.signInModal.inModal).should("be.visible");
      } else if (resp?.statusCode === 404) {
        checkSignUpModalElements();
        cy.getByTestId(el.signUpModal.upModal).should("be.visible");
      }
    });
}

export function enterBadDataToPassword(value?: string) {
  cy.getByTestId(el.signInButton).should("be.visible").click();
  cy.getByTestId(el.signInModal.signForm)
    .contains("Povinné pole")
    .should("be.visible");
  fillPassword(value);
  cy.getByTestId(el.signInButton).click();
  cy.getByTestId(el.signInModal.passInput)
    .contains("Zadané heslo není správné")
    .should("be.visible");
}

export function submitLogin() {
  cy.getByTestId(el.signInModal.loginButton).click();
  checkNavbarProfileElements();
}

export function submitLoginMobile() {
  cy.getByTestId(el.signInModal.loginButton).click();
  cy.getByTestId(el.navbar.button).click({ force: true });
  checkNavbarElementsMobile();
}

export function checkLogInSucces(email: string) {
  cy.getByTestId(el.navbarProfile.avatarMail)
    .contains(email)
    .should("be.visible");
  cy.getByTestId(el.loginButton).should("not.exist");
}

export function checkLoginSuccessMobile() {
  cy.getByTestId(el.navbar.button).click({ force: true });
  cy.getByTestId(el.navbar.myProfile).should("be.visible");
}

export function checkLogOutSucces() {
  checkHamburgerButton().click({ force: true });
  checkSidebarLogOutElements();
  cy.getByTestId(el.navbar.logOutButton).should("be.visible").click();
  cy.getByTestId(el.navbarProfile.avatarMail).should("not.exist");
  cy.getByTestId(el.loginButton).should("be.visible");
}

export function checkLogOutSuccesMobile() {
  checkHamburgerButton().click({ force: true });
  checkSidebarLogOutElements();
  cy.getByTestId(el.navbar.logOutButton).should("be.visible").click();
  cy.getByTestId(el.navbar.button)
    .click()
    .then(() => {
      cy.getByTestId(el.navbar.logInButton).should("be.visible");
    });
}

export function clickForgottenPass() {
  cy.getByTestId(el.signInModal.forgotButton).click();
  checkForgottenPassElementsFirstStep();
}

export function clickResetPass() {
  cy.getByTestId(el.forgotPassModal.button).click();
  cy.wait("@email-sent")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.be.oneOf([200, 409]);
      if (resp?.statusCode === 200) {
        checkForgottenPassElementsSecondStep();
        cy.getByTestId(el.forgotPassModal.mailSent).should("be.visible");
      } else if (resp?.statusCode === 409) {
        checkForgottenPassElementsSecondStepNotSend();
        cy.getByTestId(el.forgotPassModal.mailNotSent).should("be.visible");
      }
    });
}

export function fillRegPassword(value?: string) {
  const password: any = value !== undefined ? value : faker.internet.password();
  cy.getByTestId(el.signUpModal.pass).clear().type(password);
}

export function checkGreySubmitButton() {
  cy.getByTestId(el.signUpModal.button).should("be.disabled");
}

export function clickRegRadioButton(value: string) {
  cy.getByTestId(value).click();
}

export function submitRegistration(mail: string) {
  cy.getByTestId(el.signUpModal.button).click();
  checkNavbarProfileElements();
  cy.wait("@email-reg");
  cy.getByTestId(el.navbarProfile.avatarMail).should("contain", mail);
}

export function submitRegistrationMobile() {
  cy.getByTestId(el.signUpModal.button).click();
  cy.getByTestId(el.navbar.button).click({ force: true });
  checkNavbarElementsMobile();
  cy.wait("@email-reg");
}
