import { getUdApiUrl } from "../../helper/helper";

describe("UD Offer Find Rent", () => {
  it("Check OfferFind All CZ", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 52.95525697845468,
            lng: 19.193115234375004,
          },
          southWest: {
            lat: 46.4605655457854,
            lng: 11.755371093750002,
          },
        },
        offerType: "rent",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Praha", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 50.177403,
            lng: 14.7067945,
          },
          southWest: {
            lat: 49.9419363,
            lng: 14.2244533,
          },
        },
        offerType: "rent",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Brno", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.294485,
            lng: 16.7278532,
          },
          southWest: {
            lat: 49.1096552,
            lng: 16.4280678,
          },
        },
        offerType: "rent",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Ostrava", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.9137292,
            lng: 18.3763648,
          },
          southWest: {
            lat: 49.7257537,
            lng: 18.0984242,
          },
        },
        offerType: "rent",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Plzen", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.805786,
            lng: 13.4758465,
          },
          southWest: {
            lat: 49.6776084,
            lng: 13.2680001,
          },
        },
        offerType: "rent",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Liberec", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 50.8244004,
            lng: 15.1469891,
          },
          southWest: {
            lat: 50.7078286,
            lng: 14.9523506,
          },
        },
        offerType: "rent",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Olomouc", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.6573504,
            lng: 17.396027,
          },
          southWest: {
            lat: 49.5329452,
            lng: 17.1630437,
          },
        },
        offerType: "rent",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Pardubice", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 50.0773774,
            lng: 15.8912722,
          },
          southWest: {
            lat: 49.9920055,
            lng: 15.6250687,
          },
        },
        offerType: "rent",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Zlin", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.2990687,
            lng: 17.7911742,
          },
          southWest: {
            lat: 49.1630163,
            lng: 17.5618395,
          },
        },
        offerType: "rent",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Ceske Budejovice", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.019637,
            lng: 14.5947066,
          },
          southWest: {
            lat: 48.933068,
            lng: 14.3863878,
          },
        },
        offerType: "rent",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });
});

describe("UD Offer Find Sale", () => {
  it("Check OfferFind All CZ", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 52.95525697845468,
            lng: 19.193115234375004,
          },
          southWest: {
            lat: 46.4605655457854,
            lng: 11.755371093750002,
          },
        },
        offerType: "sale",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Praha", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 50.177403,
            lng: 14.7067945,
          },
          southWest: {
            lat: 49.9419363,
            lng: 14.2244533,
          },
        },
        offerType: "sale",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Brno", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.294485,
            lng: 16.7278532,
          },
          southWest: {
            lat: 49.1096552,
            lng: 16.4280678,
          },
        },
        offerType: "sale",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Ostrava", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.9137292,
            lng: 18.3763648,
          },
          southWest: {
            lat: 49.7257537,
            lng: 18.0984242,
          },
        },
        offerType: "sale",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Plzen", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.805786,
            lng: 13.4758465,
          },
          southWest: {
            lat: 49.6776084,
            lng: 13.2680001,
          },
        },
        offerType: "sale",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Liberec", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 50.8244004,
            lng: 15.1469891,
          },
          southWest: {
            lat: 50.7078286,
            lng: 14.9523506,
          },
        },
        offerType: "sale",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Olomouc", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.6573504,
            lng: 17.396027,
          },
          southWest: {
            lat: 49.5329452,
            lng: 17.1630437,
          },
        },
        offerType: "sale",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Pardubice", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 50.0773774,
            lng: 15.8912722,
          },
          southWest: {
            lat: 49.9920055,
            lng: 15.6250687,
          },
        },
        offerType: "sale",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Zlin", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.2990687,
            lng: 17.7911742,
          },
          southWest: {
            lat: 49.1630163,
            lng: 17.5618395,
          },
        },
        offerType: "sale",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });

  it("Check OfferFind Ceske Budejovice", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.019637,
            lng: 14.5947066,
          },
          southWest: {
            lat: 48.933068,
            lng: 14.3863878,
          },
        },
        offerType: "sale",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        expect(resp.body.data.offers.length).not.to.eq(0);
      });
  });
});

describe("UD Offer Find Coliving", () => {
  it("Check OfferFind All CZ", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 52.95525697845468,
            lng: 19.193115234375004,
          },
          southWest: {
            lat: 46.4605655457854,
            lng: 11.755371093750002,
          },
        },
        offerType: "coliving",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        const sum = resp.body.data.offers.length;
        if (sum === 0) {
          cy.log("Count is 0 which is ok");
        }
        if (sum > 0) {
          cy.log("Count is: " + sum);
        }
      });
  });

  it("Check OfferFind Praha", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 50.177403,
            lng: 14.7067945,
          },
          southWest: {
            lat: 49.9419363,
            lng: 14.2244533,
          },
        },
        offerType: "coliving",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        const sum = resp.body.data.offers.length;
        if (sum === 0) {
          cy.log("Count is 0 which is ok");
        }
        if (sum > 0) {
          cy.log("Count is: " + sum);
        }
      });
  });

  it("Check OfferFind Brno", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.294485,
            lng: 16.7278532,
          },
          southWest: {
            lat: 49.1096552,
            lng: 16.4280678,
          },
        },
        offerType: "coliving",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        const sum = resp.body.data.offers.length;
        if (sum === 0) {
          cy.log("Count is 0 which is ok");
        }
        if (sum > 0) {
          cy.log("Count is: " + sum);
        }
      });
  });

  it("Check OfferFind Ostrava", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.9137292,
            lng: 18.3763648,
          },
          southWest: {
            lat: 49.7257537,
            lng: 18.0984242,
          },
        },
        offerType: "coliving",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        const sum = resp.body.data.offers.length;
        if (sum === 0) {
          cy.log("Count is 0 which is ok");
        }
        if (sum > 0) {
          cy.log("Count is: " + sum);
        }
      });
  });

  it("Check OfferFind Plzen", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.805786,
            lng: 13.4758465,
          },
          southWest: {
            lat: 49.6776084,
            lng: 13.2680001,
          },
        },
        offerType: "coliving",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        const sum = resp.body.data.offers.length;
        if (sum === 0) {
          cy.log("Count is 0 which is ok");
        }
        if (sum > 0) {
          cy.log("Count is: " + sum);
        }
      });
  });

  it("Check OfferFind Liberec", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 50.8244004,
            lng: 15.1469891,
          },
          southWest: {
            lat: 50.7078286,
            lng: 14.9523506,
          },
        },
        offerType: "coliving",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        const sum = resp.body.data.offers.length;
        if (sum === 0) {
          cy.log("Count is 0 which is ok");
        }
        if (sum > 0) {
          cy.log("Count is: " + sum);
        }
      });
  });

  it("Check OfferFind Olomouc", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.6573504,
            lng: 17.396027,
          },
          southWest: {
            lat: 49.5329452,
            lng: 17.1630437,
          },
        },
        offerType: "coliving",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        const sum = resp.body.data.offers.length;
        if (sum === 0) {
          cy.log("Count is 0 which is ok");
        }
        if (sum > 0) {
          cy.log("Count is: " + sum);
        }
      });
  });

  it("Check OfferFind Pardubice", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 50.0773774,
            lng: 15.8912722,
          },
          southWest: {
            lat: 49.9920055,
            lng: 15.6250687,
          },
        },
        offerType: "coliving",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        const sum = resp.body.data.offers.length;
        if (sum === 0) {
          cy.log("Count is 0 which is ok");
        }
        if (sum > 0) {
          cy.log("Count is: " + sum);
        }
      });
  });

  it("Check OfferFind Zlin", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.2990687,
            lng: 17.7911742,
          },
          southWest: {
            lat: 49.1630163,
            lng: 17.5618395,
          },
        },
        offerType: "coliving",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        const sum = resp.body.data.offers.length;
        if (sum === 0) {
          cy.log("Count is 0 which is ok");
        }
        if (sum > 0) {
          cy.log("Count is: " + sum);
        }
      });
  });

  it("Check OfferFind Ceske Budejovice", () => {
    cy.request({
      method: "POST",
      url: `${getUdApiUrl()}/offer/find?page=1&perPage=20&sorting=latest`,
      body: {
        bounds: {
          northEast: {
            lat: 49.019637,
            lng: 14.5947066,
          },
          southWest: {
            lat: 48.933068,
            lng: 14.3863878,
          },
        },
        offerType: "coliving",
      },
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body).to.have.property("extraData");
        expect(resp.body).to.have.property("data");
        expect(resp.body.data).to.have.property("offers");
      })
      .then((resp) => {
        expect(typeof resp.body.data).to.eq("object");
        expect(typeof resp.body.data.offers).to.eq("object");
        const sum = resp.body.data.offers.length;
        if (sum === 0) {
          cy.log("Count is 0 which is ok");
        }
        if (sum > 0) {
          cy.log("Count is: " + sum);
        }
      });
  });
});
