import { captureException } from "@sentry/nextjs";
import <PERSON><PERSON> from "joi";
import { useEffect, useMemo, useState } from "react";
import { Box, Heading, Stack, Text } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";

import { useTracking } from "contexts/Tracking";
import CreateWatchdogApiEndpoint from "utils/watchdog/api/CreateWatchdog";
import { EApiWatchdogFrequencyId } from "utils/watchdog";
import useForm from "hooks/useForm";
import useApiEndpoint from "hooks/useApiEndpoint";
import useGlobalState from "hooks/useGlobalState";
import useGlobalActions from "hooks/useGlobalActions";
import { resolveDogFreq } from "helpers/watchdog";
import { ISearchFilterState } from "utils/search";
import { useWatchdogCreatedModal } from "../useWatchdogCreatedModal";
import { InitialUseCreateModalType, useCreateModal } from "../ModalsProvider";
import AnimatedInput from "components/Chakra/AnimatedInput";
import CustomLink from "components/Chakra/CustomLink";
import Button from "components/Chakra/Button";
import FormRadio from "components/Chakra/FormRadio";
import useRouter from "hooks/useRouter";
import { isSearchPath } from "helpers/search";
import { TFilter } from "scenes/Search/types";
import {
  disposition as dispositionConfig,
  convenience as convenienceConfig,
} from "@ud/config/prodeje/config";
import { DISPOSITIONS } from "@ud/config/dispositions";
import NumericInterval from "utils/NumericInterval";
import { FURNISHING } from "@ud/config/furnishing";
import { EApiFurnishingId } from "@ud/config/prodeje/types";
import { CONVENIENCES } from "@ud/config/conveniences";
import { OFFER_AGE } from "@ud/config/offerAge";
import { OFFER_SORTING } from "@ud/config/offerSorting";

export function WatchdogModal({
  location,
  filter,
}: {
  location: string;
  filter: TFilter;
}) {
  const [showWatchdog, setShowWatchdog] = useState(false);
  const [isLoginLoading, setIsLoginLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [frequencyId, setFrequencyId] = useState<string>(
    EApiWatchdogFrequencyId.ONCE_PER_DAY,
  );

  const { callEndpoint: createWatchdogApi } = useApiEndpoint(
    CreateWatchdogApiEndpoint,
  );

  const { openModal: openWatchdogCreatedModal, closeAllModals } =
    useWatchdogCreatedModal();
  const { trackEvent } = useTracking();
  const { user } = useGlobalState();
  const { login } = useGlobalActions();
  const { t } = useTranslation();
  const { asPath } = useRouter();
  const formSchema = useMemo(
    () =>
      Joi.object({
        name: Joi.string().required(),
        email: Joi.string().email({ tlds: false }).required().trim(),
        password: Joi.string().required(),
      }),
    [],
  );
  const priceLabel = useMemo(() => {
    if (!filter.price) {
      return null;
    }
    if (filter.price.min && filter.price.max) {
      return `cena od ${filter.price.min} do ${filter.price.max} Kč`;
    } else if (filter.price.min) {
      return `cena od ${filter.price.min} Kč`;
    } else if (filter.price.max) {
      return `cena do ${filter.price.max} Kč`;
    }

    return null;
  }, []);
  const areaLabel = useMemo(() => {
    if (!filter.floorArea) {
      return null;
    }

    const area = new NumericInterval(
      filter.floorArea?.min,
      filter.floorArea?.max,
    );

    if (!area.isBothEndsUnbounded()) {
      if (area.isBothEndsBounded()) {
        return `velikost ${area.min} - ${area.max} m2`;
      } else if (area.isLeftBounded() && !area.isRightBounded()) {
        return `velikost od ${area.min} m2`;
      }

      return `velikost do ${area.max} m2`;
    }

    return null;
  }, [filter.floorArea]);
  const defaultWdName = useMemo(
    () =>
      [
        location === "custom" ? "" : location,
        filter.disposition
          ?.map((dispo) => dispositionConfig[dispo].translationId)
          .join(", "),
        priceLabel,
        filter.isNoCommission ? "bez provize" : "",
        areaLabel,
      ]
        .filter(Boolean)
        .join(", "),
    [filter],
  );
  const {
    handleSubmit,
    registerWithError,
    watch,
    formState: { isSubmitting },
    getFieldState,
    clearErrors,
  } = useForm<{
    email: string;
    password: string;
    name: string;
  }>(formSchema, {
    defaultValues: {
      email: user?.email || "",
      password: "",
      name: defaultWdName,
    },
  });
  const frequencyOptions = useMemo(
    () =>
      [
        {
          value: EApiWatchdogFrequencyId.IMMEDIATE,
          label: t("watchdogFrequency.immediate"),
          badge: "Nejoblíbenější",
        },
        {
          value: EApiWatchdogFrequencyId.ONCE_PER_DAY,
          label: t("watchdogFrequency.oncePerDay"),
        },
        {
          value: EApiWatchdogFrequencyId.ONCE_PER_WEEK,
          label: t("watchdogFrequency.oncePerWeek"),
        },
      ] || [],
    [],
  );

  const buildOldFilterFromNew = (newFilter: TFilter) => {
    const getDispos = (): ISearchFilterState["dispositions"] => {
      if (!newFilter.disposition?.length) {
        return [];
      }

      const result: ISearchFilterState["dispositions"] = [];

      Object.values(dispositionConfig).map(({ translationId, id }) => {
        if (
          newFilter.disposition?.includes(id as keyof typeof dispositionConfig)
        ) {
          const found = DISPOSITIONS.find(
            (disp) => disp.translationId === translationId,
          );
          if (found) {
            result.push(found);
          }
        }
      });

      return result;
    };

    const getConveniences = (): ISearchFilterState["conveniences"] => {
      if (!newFilter.convenience?.length) {
        return [];
      }

      const result: ISearchFilterState["conveniences"] = [];

      Object.values(convenienceConfig).map(({ translationId, id }) => {
        if (
          newFilter.convenience?.includes(id as keyof typeof convenienceConfig)
        ) {
          const found = CONVENIENCES.find(
            (conv) => conv.translationId === translationId,
          );
          if (found) {
            result.push(found);
          }
        }
      });

      return result;
    };

    const getFurnishing = (): ISearchFilterState["furnishing"] => {
      if (!newFilter.furnished || !newFilter.furnished.length) {
        return [];
      }

      const result: ISearchFilterState["furnishing"] = [];

      if (newFilter.furnished.includes("no")) {
        const found = FURNISHING.find(
          ({ apiId }) => apiId === EApiFurnishingId.NONE,
        );
        if (found) {
          result.push(found);
        }
      }
      if (newFilter.furnished.includes("yes")) {
        const found = FURNISHING.find(
          ({ apiId }) => apiId === EApiFurnishingId.FULL,
        );
        if (found) {
          result.push(found);
        }
      }
      if (newFilter.furnished.includes("partial")) {
        const found = FURNISHING.find(
          ({ apiId }) => apiId === EApiFurnishingId.MEDIUM,
        );
        if (found) {
          result.push(found);
        }
      }

      return result;
    };

    const oldFilter: ISearchFilterState = {
      offerTypeId: newFilter.offerType === "rent" ? 1 : 2,
      bounds: newFilter.bounds,
      noCommitionFee: newFilter.isNoCommission || null,
      dispositions: getDispos(),
      area: new NumericInterval(
        newFilter.floorArea?.min,
        newFilter.floorArea?.max,
      ),
      price: new NumericInterval(newFilter.price?.min, newFilter.price?.max),
      furnishing: getFurnishing(),
      conveniences: getConveniences(),
      isBalcony: null,
      pageIndex: 0,
      offerAge: OFFER_AGE[0],
      specialFlag: null,
      sorting: OFFER_SORTING[0],
    };

    return oldFilter;
  };

  const handleWatchdog = async () => {
    const email = watch("email");
    const name = watch("name");
    const password = watch("password");
    if (!email?.length || !name?.length) {
      return;
    }

    try {
      const { data } = await createWatchdogApi({
        email,
        filter: buildOldFilterFromNew(filter),
        frequencyId,
        name,
        offerTypeId: 1,
        location,
      });
      if (data?.status === "ok" && !password) {
        trackEvent({
          "dl.eCat": isSearchPath(asPath) ? "search" : "",
          "dl.eAct": "registration",
          "dl.eLab": "Hlídací pes",
        });
      }
      clearErrors();
      if (data?.status === "error" && data?.error_id === "ERROR_EMAIL_EXISTS") {
        setErrorMessage(
          "Zadanou emailovou adresu již známe. Zadejte heslo prosím.",
        );
        setShowWatchdog(true);

        return;
      }
      // const dispositions = filter.dispositions.map(
      //   (item) => item.translationId,
      // );
      // const furnishing = filter.furnishing.map((item) => item.translationId);
      // const parseData: TEventDataWatchdogCreated = {
      //   city: location,
      //   dispositions: dispositions || [],
      //   frequency: frequencyId,
      //   furnishing: furnishing || [],
      //   noCommission: filter.noCommitionFee || false,
      // };
      // createEventWatchdogCreated({
      //   userEmail: email,
      //   eventData: parseData,
      // });
      closeAllModals();
      trackEvent({
        "dl.eCat": "watchdog",
        "dl.eAct": "vytvoreni_vyhledavani_top",
        "dl.eLab": resolveDogFreq(frequencyId),
      });
      openWatchdogCreatedModal();
    } catch (err) {
      captureException(err);
    }
  };

  const handleData = async ({
    email,
    password,
  }: {
    email: string;
    password: string;
  }) => {
    if (!email || !password) {
      return handleWatchdog();
    }

    setIsLoginLoading(true);
    const wasSuccessful = await login({
      email,
      password,
    });

    if (wasSuccessful) {
      return handleWatchdog();
    }

    setErrorMessage("Přihlášení se nezdařilo");
  };

  const handleFrequency = (value: string) => {
    setFrequencyId(value);
  };
  const handleSubmitClick = () => {
    trackEvent({
      "dl.eCat": "search",
      "dl.eAct": "watchdog.clicked",
      "dl.eLab": "Watchdog - Uložit psa",
    });
    if (
      (!getFieldState("email")?.error && getFieldState("email")?.isDirty) ||
      user
    ) {
      handleWatchdog();
    }
  };

  useEffect(() => {
    trackEvent({
      "dl.eCat": "search",
      "dl.eAct": "dialog.shown",
      "dl.eLab": "Watchdog - založení",
    });
  }, []);

  return (
    <Box textAlign="center" data-test="watchDotSearchModalContent">
      <Heading data-test="dogModalHeading" as="h3">
        Hlídací pes hlídá inzeráty za vás
      </Heading>
      <Text mt="10px" mb="20px">
        Každý nový inzerát vyčmuchá <br />a pošle rovnou na e-mail.
      </Text>
      <form onSubmit={handleSubmit(handleData)}>
        <Stack mb="15px">
          <AnimatedInput
            {...registerWithError("name")}
            name="name"
            label="Jméno psa"
            required
            data-test="dogNameInput"
          />
        </Stack>

        <Stack my="15px" data-test="watchDogNotification">
          <Text
            align="left"
            textTransform="uppercase"
            color="ud-grey-600"
            fontWeight="500"
            variant="form-label"
          >
            Interval upozornění
          </Text>
          <Box
            p="16px"
            borderRadius="6px"
            border="1px solid"
            borderColor="ud-grey-300"
          >
            <FormRadio
              items={frequencyOptions}
              value={frequencyId}
              onChange={handleFrequency}
            />
          </Box>
        </Stack>

        <Stack mt="15px">
          {(!user || isLoginLoading) && (
            <Box mb="25px">
              <div>
                <AnimatedInput
                  {...registerWithError("email")}
                  name="email"
                  label="E-mail"
                  autoComplete="email"
                  required
                />
              </div>
              {showWatchdog && (
                <Stack mt="15px">
                  <AnimatedInput
                    {...registerWithError("password")}
                    name="password"
                    type="password"
                    label="Heslo"
                    placeholder="Vaše heslo"
                    autoComplete={
                      showWatchdog ? "password" : "current-password"
                    }
                    required
                  />
                </Stack>
              )}
            </Box>
          )}
        </Stack>

        {errorMessage && (
          <Box>
            <Text textColor="ud-primary" my="15px">
              {errorMessage}
            </Text>
          </Box>
        )}

        <Text mb="5px" variant="small_2">
          Kliknutím souhlasíte s našimi{" "}
          <CustomLink showUnderline to="/zpracovani-osobnich-udaju">
            zásadami ochrany osobních údajů.
          </CustomLink>
        </Text>
        <Stack mt={4} direction="row" justify="center">
          <Button
            data-test="saveDogButton"
            type="submit"
            onClick={handleSubmitClick}
            isLoading={isSubmitting}
            isDisabled={isSubmitting}
          >
            Uložit psa
          </Button>
        </Stack>
      </form>
    </Box>
  );
}

export const useWatchdogModal: InitialUseCreateModalType<{
  location: string;
  filter: TFilter;
}> = () =>
  useCreateModal("watchdogModal", WatchdogModal as React.FC, null, null, {
    size: "lg",
    variant: "primary",
  });
