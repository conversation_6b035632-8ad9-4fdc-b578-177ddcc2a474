import { TFilter } from "scenes/Search/types";
import { EApiDispositionId } from "./types";

export type Config = Record<string, ConfigProps>;

export type ConfigProps = {
  translationId: string;
  queryValue?: string;
  description?: string;
  apiId?: string;
};
export const offerSorting = {
  best: {
    translationId: "Nejlep<PERSON><PERSON>",
    queryValue: "nejlepsi",
    id: "best" as const,
  },
  latest: {
    translationId: "Nejnovější",
    queryValue: "nejnovejsi",
    id: "latest" as const,
  },
  cheapest: {
    translationId: "Nejlevnější",
    queryValue: "nejlevnejsi",
    id: "cheapest" as const,
  },
};

export const floorCountRangeMin = {
  "1": {
    translationId: "1. podlaž<PERSON>",
    queryValue: "1",
    comparisonIndex: 1,
  },
  "2": {
    translationId: "2. podla<PERSON><PERSON>",
    queryValue: "2",
    comparisonIndex: 2,
  },
  "3": {
    translationId: "3. podla<PERSON><PERSON>",
    queryValue: "3",
    comparisonIndex: 3,
  },
  "4": {
    translationId: "4. podlaž<PERSON>",
    queryValue: "4",
    comparisonIndex: 4,
  },
  "5": {
    translationId: "5. podlaží",
    queryValue: "5",
    comparisonIndex: 5,
  },
  "6": {
    translationId: "6. podlaží",
    queryValue: "6",
    comparisonIndex: 6,
  },
  "7": {
    translationId: "7. podlaží",
    queryValue: "7",
    comparisonIndex: 7,
  },
};

export const floorCountRangeMax = {
  "3": {
    translationId: "3. podlaží",
    queryValue: "3",
    comparisonIndex: 3,
  },
  "4": {
    translationId: "4. podlaží",
    queryValue: "4",
    comparisonIndex: 4,
  },
  "5": {
    translationId: "5. podlaží",
    queryValue: "5",
    comparisonIndex: 5,
  },
  "6": {
    translationId: "6. podlaží",
    queryValue: "6",
    comparisonIndex: 6,
  },
  "7": {
    translationId: "7. podlaží",
    queryValue: "7",
    comparisonIndex: 7,
  },
};

export const floorRangeMin = {
  "-1": {
    translationId: "suterén",
    queryValue: "-1",
    comparisonIndex: 0.4,
  },
  "0": {
    translationId: "přízemí",
    queryValue: "0",
    comparisonIndex: 0.5,
  },
  "1": {
    translationId: "1. podlaží",
    queryValue: "1",
    comparisonIndex: 1,
  },
  "2": {
    translationId: "2. podlaží",
    queryValue: "2",
    comparisonIndex: 2,
  },
  "3": {
    translationId: "3. podlaží",
    queryValue: "3",
    comparisonIndex: 3,
  },
};

export const floorRangeMax = {
  "1": {
    translationId: "1. podlaží",
    queryValue: "1",
    comparisonIndex: 1,
  },
  "2": {
    translationId: "2. podlaží",
    queryValue: "2",
    comparisonIndex: 2,
  },
  "3": {
    translationId: "3. podlaží",
    queryValue: "3",
    comparisonIndex: 3,
  },
  "4": {
    translationId: "4. podlaží",
    queryValue: "4",
    comparisonIndex: 4,
  },
  "5": {
    translationId: "5. podlaží",
    queryValue: "5",
    comparisonIndex: 5,
  },
};

export const propertyType = {
  flat: {
    translationId: "Byt",
    queryValue: "bytu",
    id: "flat" as const,
  },
  house: {
    translationId: "Dům",
    queryValue: "domu",
    id: "house" as const,
  },
};

export const availabilityType = {
  asap: {
    translationId: "Ihned",
    queryValue: "asap",
    id: "asap" as const,
  },
  date: {
    translationId: "Konkrétní datum",
    queryValue: "datum",
    id: "date" as const,
  },
};

export const colivingType = {
  fullRoom: {
    translationId: "Celý pokoj",
    queryValue: "cely",
    id: "fullRoom",
  },
  sharedRoom: {
    translationId: "Sdílený pokoj",
    queryValue: "zdileny",
    id: "sharedRoom",
  },
};

export const offerType = {
  rent: {
    translationId: "Pronájem",
    queryValue: "pronajem",
    id: "rent" as const,
  },
  sale: {
    translationId: "Prodej",
    queryValue: "prodej",
    id: "sale" as const,
  },
  coliving: {
    translationId: "Spolubydlení",
    queryValue: "spolubydleni",
    id: "coliving" as const,
  },
};

export const convenience = {
  cellar: {
    translationId: "Sklep",
    queryValue: "sklep",
    id: "cellar" as const,
  },
  dishwasher: {
    translationId: "Myčka",
    queryValue: "mycka",
    id: "dishwasher" as const,
  },
  washingMachine: {
    translationId: "Pračka",
    queryValue: "pracka",
    id: "washingMachine" as const,
  },
  fridge: {
    translationId: "Lednička",
    queryValue: "lednicka",
    id: "fridge" as const,
  },
  balcony: {
    translationId: "Balkón",
    queryValue: "s-balkonem",
    id: "balcony" as const,
  },
  loggia: {
    translationId: "Loggia",
    queryValue: "loggia",
    id: "loggia" as const,
  },
  terrace: {
    translationId: "Terasa",
    queryValue: "terasa",
    id: "terrace" as const,
  },
  lift: {
    translationId: "Výtah",
    queryValue: "vytah",
    id: "lift" as const,
  },
  garden: {
    translationId: "Zahrada",
    queryValue: "zahrada",
    id: "garden" as const,
  },
};

export const energyEfficiencyRating = {
  a: {
    translationId: "A",
    translationLong: "A - Mimořádně úsporná",
    queryValue: "a",
    id: "a",
  },
  b: {
    translationId: "B",
    translationLong: "B - Velmi úsporná",
    queryValue: "b",
    id: "b",
  },
  c: {
    translationId: "C",
    translationLong: "C - Úsporná",
    queryValue: "c",
    id: "c",
  },
  d: {
    translationId: "D",
    translationLong: "D - Méně úsporná",
    queryValue: "d",
    id: "d",
  },
  e: {
    translationId: "E",
    translationLong: "E - Nehospodárná",
    queryValue: "e",
    id: "e",
  },
  f: {
    translationId: "F",
    translationLong: "F - Velmi nehospodárná",
    queryValue: "f",
    id: "f",
  },
  g: {
    translationId: "G",
    translationLong: "G -  Mimořádně nehospodárná",
    queryValue: "g",
    id: "g",
  },
};

export const offerAge = {
  day: {
    translationId: "Den",
    queryValue: "den",
  },
  week: {
    translationId: "Týden",
    queryValue: "tyden",
  },
  month: {
    translationId: "Měsíc",
    queryValue: "mesic",
  },
};

export const locationType = {
  cityCenter: {
    queryValue: "centrum",
    translationId: "Centrum obce",
    id: "cityCenter",
  },
  quietPart: {
    queryValue: "klidna",
    translationId: "Klidná část obce",
    id: "quietPart",
  },
  busyPart: {
    queryValue: "rusna",
    translationId: "Rušná část obce",
    id: "busyPart",
  },
  outskirts: {
    queryValue: "okraj",
    translationId: "Okraj obce",
    id: "outskirts",
  },
  housingEstate: {
    queryValue: "sidliste",
    translationId: "Sídlište",
    id: "housingEstate",
  },
  semiIsolated: {
    queryValue: "polosamota",
    translationId: "Polosamota",
    id: "semiIsolated",
  },
  isolated: {
    queryValue: "samota",
    translationId: "Samota",
    id: "isolated",
  },
};

export const surroundingType = {
  residential: {
    queryValue: "obytna",
    translationId: "Obytná",
    id: "residential",
  },
  businessResidential: {
    queryValue: "obchodni-obytna",
    translationId: "Obchodní a obytná",
    id: "businessResidential",
  },
  business: {
    queryValue: "obchodni",
    translationId: "Obchodní",
    id: "business",
  },
  commercial: {
    queryValue: "komercni",
    translationId: "Komerční",
    id: "commercial",
  },
  industrial: {
    queryValue: "prumyslova",
    translationId: "Průmyslová",
    id: "industrial",
  },
  rural: {
    queryValue: "venkovska",
    translationId: "Venkovská",
    id: "rural",
  },
  recreational: {
    queryValue: "rekreacni",
    translationId: "Rekreační",
    id: "recreational",
  },
  unusedRecreational: {
    queryValue: "nevyuzita",
    translationId: "Rekreační nevyužitá",
    id: "unusedRecreational",
  },
};

export const furnished = {
  no: {
    translationId: "Nezařízené",
    queryValue: "ne",
    id: "no",
  },
  partial: {
    translationId: "Částečně",
    queryValue: "castecne",
    id: "partial",
  },
  yes: {
    translationId: "Zařízené",
    queryValue: "ano",
    id: "yes",
  },
};

export const flatType = {
  maisonette: {
    translationId: "Mezonet",
    queryValue: "mezonet",
    id: "maisonette",
  },
  loft: {
    translationId: "Loft",
    queryValue: "loft",
    id: "loft",
  },
  attic: {
    translationId: "Podkrovní",
    queryValue: "podkrovi",
    id: "attic",
  },
};

export const ownership = {
  personal: {
    translationId: "Osobní",
    queryValue: "osobni",
    id: "personal",
  },
  cooperative: {
    translationId: "Družstevní",
    queryValue: "druzstevni",
    id: "cooperative",
  },
  municipal: {
    translationId: "Obecní",
    queryValue: "obecni",
    id: "municipal",
  },
};

export const buildingCondition = {
  veryGood: {
    translationId: "Velmi dobrý",
    queryValue: "velmi-dobry",
    id: "veryGood",
  },
  good: {
    translationId: "Dobrý",
    queryValue: "dobry",
    id: "good",
  },
  bad: {
    translationId: "Špatný",
    queryValue: "spatny",
    id: "bad",
  },
  newBuild: {
    translationId: "Novostavba",
    queryValue: "novostavba",
    id: "newBuild",
  },
  project: {
    translationId: "Projekt",
    queryValue: "projekt",
    id: "project",
  },
  inConstruction: {
    translationId: "Ve výstavbě",
    queryValue: "ve-vystavbe",
    id: "inConstruction",
  },
  preReconstruction: {
    translationId: "Před rekonstrukcí",
    queryValue: "pred-rekon",
    id: "preReconstruction",
  },
  postReconstruction: {
    translationId: "Po rekonstrukci",
    queryValue: "po-rekon",
    id: "postReconstruction",
  },
  inReconstruction: {
    translationId: "V rekonstrukci",
    queryValue: "v-rekon",
    id: "inReconstruction",
  },
  demolition: {
    translationId: "K demolici",
    queryValue: "demolice",
    id: "demolition",
  },
};

export const material = {
  brick: {
    translationId: "Cihlová",
    queryValue: "cihlova",
    id: "brick",
  },
  panel: {
    translationId: "Panelová",
    queryValue: "panel",
    id: "panel",
  },
  assembled: {
    translationId: "Montovaná",
    queryValue: "montovana",
    id: "assembled",
  },
  skeleton: {
    translationId: "Skeletová",
    queryValue: "skeletova",
    id: "skeleton",
  },
  stone: {
    translationId: "Kamenná",
    queryValue: "kamenna",
    id: "stone",
  },
  wood: {
    translationId: "Dřevěná",
    queryValue: "drevena",
    id: "wood",
  },
  mixed: {
    translationId: "Smíšená",
    queryValue: "smisena",
    id: "mixed",
  },
};

export const houseConvenience = {
  pool: {
    translationId: "Bazén",
    queryValue: "bazen",
  },
  cellar: {
    translationId: "Sklep",
    queryValue: "sklep",
  },
  garage: {
    translationId: "Garáž",
    queryValue: "garaz",
  },
  parking: {
    translationId: "Parkování",
    queryValue: "parkovani",
  },
  wheelchairAccessible: {
    translationId: "Bezbariérové",
    queryValue: "bezbarierove",
  },
};

export const roomCount = {
  oneRoom: {
    translationId: "1 pokoj",
    queryValue: "1",
    id: "oneRoom",
  },
  twoRooms: {
    translationId: "2 pokoje",
    queryValue: "2",
    id: "twoRooms",
  },
  threeRooms: {
    translationId: "3 pokoje",
    queryValue: "3",
    id: "threeRooms",
  },
  fourRooms: {
    translationId: "4 pokoje",
    queryValue: "4",
    id: "fourRooms",
  },
  fivePlusRooms: {
    translationId: "5 a více",
    queryValue: "5",
    id: "fivePlusRooms",
  },
  atypical: {
    translationId: "Atypický",
    queryValue: "atypicky",
    id: "atypical",
  },
};

export const buildingType = {
  groundFloor: {
    translationId: "Přízemní",
    queryValue: "prizemni",
    id: "groundFloor",
  },
  multiStorey: {
    translationId: "Patrová",
    queryValue: "patrova",
    id: "multiStorey",
  },
};

export const objectType = {
  detached: {
    translationId: "Samostatný",
    queryValue: "samostatny",
    id: "detached",
  },
  row: {
    translationId: "Řadový",
    queryValue: "radovy",
    id: "row",
  },
  inBlock: {
    translationId: "V bloku",
    queryValue: "v-bloku",
    id: "inBlock",
  },
  corner: {
    translationId: "Rohový",
    queryValue: "rohovy",
    id: "corner",
  },
};

export const protectedArea = {
  protectionZone: {
    translationId: "Ochranné pásmo",
    queryValue: "ochranne",
    id: "protectionZone",
  },
  protectedLandscapeArea: {
    translationId: "CHKO",
    queryValue: "chko",
    id: "protectedLandscapeArea",
  },
  nationalPark: {
    translationId: "Národní park",
    queryValue: "narodni",
    id: "nationalPark",
  },
};

export const houseType = {
  familyHouse: {
    translationId: "Rodinný",
    queryValue: "rodinny",
    id: "familyHouse",
  },
  multiGeneration: {
    translationId: "Vícegenerační",
    queryValue: "vicegeneracni",
    id: "multiGeneration",
  },
  villa: {
    translationId: "Vila",
    queryValue: "vila",
    id: "villa",
  },
  chalet: {
    translationId: "Chata",
    queryValue: "chata",
    id: "chalet",
  },
  cottage: {
    translationId: "Chalupa",
    queryValue: "chalupa",
    id: "cottage",
  },
  agriculturalFarm: {
    translationId: "Zemědělská usedlost",
    queryValue: "osedlost",
    id: "agriculturalFarm",
  },
  turnKey: {
    translationId: "Na klíč",
    queryValue: "klic",
    id: "turnKey",
  },
  monumentOther: {
    translationId: "Památka/jiné",
    queryValue: "jine",
    id: "monumentOther",
  },
};

export const disposition = {
  onePlusKk: {
    translationId: "1+kk",
    queryValue: "1-kk",
    id: "onePlusKk" as const,
  },
  onePlusOne: {
    translationId: "1+1",
    queryValue: "1-1",
    id: "onePlusOne" as const,
  },
  twoPlusKk: {
    translationId: "2+kk",
    queryValue: "2-kk",
    id: "twoPlusKk" as const,
  },
  twoPlusOne: {
    translationId: "2+1",
    queryValue: "2-1",
    id: "twoPlusOne" as const,
  },
  threePlusKk: {
    translationId: "3+kk",
    queryValue: "3-kk",
    id: "threePlusKk" as const,
  },
  threePlusOne: {
    translationId: "3+1",
    queryValue: "3-1",
    id: "threePlusOne" as const,
  },
  fourPlusKk: {
    translationId: "4+kk",
    queryValue: "4-kk",
    id: "fourPlusKk",
  },
  fourPlusOne: {
    translationId: "4+1",
    queryValue: "4-1",
    id: "fourPlusOne" as const,
  },
  fivePlusKk: {
    translationId: "5+kk",
    queryValue: "5-kk",
    id: "fivePlusKk" as const,
  },
  fivePlusOne: {
    translationId: "5+1",
    queryValue: "5-1",
    id: "fivePlusOne" as const,
  },
  sixAndMore: {
    translationId: "6+kk",
    queryValue: "6-kk",
    id: "sixAndMore" as const,
  },
};

export const advertiserType = {
  realEstateBroker: {
    translationId: "Realitní makléř",
    queryValue: "kancelar",
    id: "realEstateBroker",
  },
  privateLandlord: {
    translationId: "Soukromý majitel",
    queryValue: "soukromnik",
    id: "privateLandlord",
  },
};

export const priceUnitType = {
  perRealEstate: {
    translationId: "Za nemovitost",
    queryValue: "nemovitost",
    id: "perRealEstate",
  },
  perSqM: {
    translationId: "Za m2",
    queryValue: "m2",
    id: "perSqM",
  },
};

export type QueryParamsProps = {
  value: Record<string, { queryValue: string; translationId: string }>;
  id: keyof TFilter;
  seoTitle: string;
  isNotArray?: boolean;
};

export const queryParams: Array<QueryParamsProps> = [
  { value: offerSorting, seoTitle: "seradit", id: "sorting", isNotArray: true },
  { value: disposition, seoTitle: "dispozice", id: "disposition" },
  { value: houseType, seoTitle: "typ-domu", id: "houseType" },
  { value: flatType, seoTitle: "typ-bytu", id: "flatType" },
  { value: colivingType, seoTitle: "typ-spolubydleni", id: "colivingType" },
  { value: protectedArea, seoTitle: "ochrana", id: "protectedArea" },
  { value: objectType, seoTitle: "typ-objektu", id: "objectType" },
  { value: buildingType, seoTitle: "typ-budovy", id: "buildingType" },
  { value: convenience, seoTitle: "vybava", id: "convenience" },
  { value: furnished, seoTitle: "zarizeny", id: "furnished" },
  { value: houseConvenience, seoTitle: "vybava-domu", id: "houseConvenience" },
  { value: material, seoTitle: "material", id: "material" },
  { value: buildingCondition, seoTitle: "stav", id: "buildingCondition" },
  {
    value: availabilityType,
    seoTitle: "dostupnost",
    id: "availabilityType",
    isNotArray: true,
  },
  { value: offerAge, seoTitle: "stari", id: "offerAge", isNotArray: true },
  { value: ownership, seoTitle: "vlastnictvi", id: "ownership" },
  {
    value: energyEfficiencyRating,
    seoTitle: "energie",
    id: "energyEfficiencyRating",
  },
  { value: locationType, seoTitle: "typ-lokace", id: "locationType" },
  { value: surroundingType, seoTitle: "okoli", id: "surroundingType" },

  { value: roomCount, seoTitle: "pokoju", id: "roomCount" },
];

export const floorParams: Array<{
  valueMin: Record<string, { queryValue: string; translationId: string }>;
  valueMax: Record<string, { queryValue: string; translationId: string }>;
  id: keyof Pick<TFilter, "floor" | "floorCount">;
  seoMin: string;
  seoMax: string;
}> = [
  {
    valueMin: floorRangeMin,
    valueMax: floorRangeMax,
    seoMin: "patro-od",
    seoMax: "patro-do",
    id: "floor",
  },
  {
    valueMin: floorCountRangeMin,
    valueMax: floorCountRangeMax,
    seoMin: "pater-od",
    seoMax: "pater-do",
    id: "floorCount",
  },
];

export const rangedParams: Array<{
  id: keyof Pick<TFilter, "floorArea" | "price">;
  seoTitleMin: string;
  seoTitleMax: string;
  unit: string;
  unitTranslated: string;
}> = [
  {
    id: "floorArea",
    seoTitleMin: "od",
    seoTitleMax: "do",
    unit: "m2",
    unitTranslated: "\u33A1",
  },
  {
    id: "price",
    seoTitleMin: "cena-od",
    seoTitleMax: "cena-do",
    unit: "kc",
    unitTranslated: "kč",
  },
];

export const boundsQueryValue = "bounds";
export const pageQueryValue = "strana";
export const locationQueryValue = "lokace";
export const noPropertyQueryValue = "nemovitosti";
export const isNoCommission = {
  queryValue: "bez-provize",
  translationId: "Bez provize",
};

// REBASE >>>>>>>>>>>

export const URL_TITLES_ARRAY = {
  disposition: "dispozice",
  buildingTypes: "typ-budovy",
  cohabitingType: "typ-spolubydleni",
  condition: "stav",
  convenience: "vybava",
  energyEfficiency: "narocnost",
  flatType: "typ-bytu",
  furnished: "zarizenost",
  houseConvenience: "vybava-domu",
  houseType: "typ-domu",
  locationType: "typ-lokality",
  material: "material",
  objectType: "typ-objektu",
  ownership: "vlastnictvi",
  protectedArea: "ochrana-uzemi",
  surrounding: "okoli",
  totalRooms: "pocet-pokoju",
};

export const URL_TITLES = {
  availabilityDate: "dostupny-od",
  availabilityType: "typ-dostupnosti",
  bounds: "bounds",
  priceMin: "cena-od",
  priceMax: "cena-do",
  areaMin: "od",
  areaMax: "do",
  floorMin: "patro-od",
  floorMax: "patro-do",
  totalFloorMin: "pocet-pater-od",
  totalFloorMax: "pocet-pater-do",
  location: "lokace",
  sorting: "seradit",
  offerAge: "stari-inzeratu",
  isNoCommission: "bez-provize",
};

export const CONFIG = {
  FLOOR_TOTAL_MAX: {
    "3F": {
      translationId: "3. podlaží",
      queryValue: "3p",
      comparisonIndex: 3,
    },
    "4F": {
      translationId: "4. podlaží",
      queryValue: "4p",
      comparisonIndex: 4,
    },
    "5F": {
      translationId: "5. podlaží",
      queryValue: "5p",
      comparisonIndex: 5,
    },
    "6F": {
      translationId: "6. podlaží",
      queryValue: "6p",
      comparisonIndex: 6,
    },
    "7F": {
      translationId: "7. podlaží",
      queryValue: "7p",
      comparisonIndex: 7,
    },
  },

  FLOOR_MIN: {
    BASEMENT: {
      translationId: "suterén",
      queryValue: "suteren",
      comparisonIndex: 0.4,
    },
    GROUND: {
      translationId: "přízemí",
      queryValue: "prizemi",
      comparisonIndex: 0.5,
    },
    "1F": {
      translationId: "1. podlaží",
      queryValue: "1p",
      comparisonIndex: 1,
    },
    "2F": {
      translationId: "2. podlaží",
      queryValue: "2p",
      comparisonIndex: 2,
    },
    "3F": {
      translationId: "3. podlaží",
      queryValue: "3p",
      comparisonIndex: 3,
    },
  },

  FLOOR_MAX: {
    "1F": {
      translationId: "1. podlaží",
      queryValue: "1p",
      comparisonIndex: 1,
    },
    "2F": {
      translationId: "2. podlaží",
      queryValue: "2p",
      comparisonIndex: 2,
    },
    "3F": {
      translationId: "3. podlaží",
      queryValue: "3p",
      comparisonIndex: 3,
    },
    "4F": {
      translationId: "4. podlaží",
      queryValue: "4p",
      comparisonIndex: 4,
    },
    "5F": {
      translationId: "5. podlaží",
      queryValue: "5p",
      comparisonIndex: 5,
    },
  },
  FLOOR_TOTAL_MIN: {
    "1F": {
      translationId: "1. podlaží",
      queryValue: "1p",
      comparisonIndex: 1,
    },
    "2F": {
      translationId: "2. podlaží",
      queryValue: "2p",
      comparisonIndex: 2,
    },
    "3F": {
      translationId: "3. podlaží",
      queryValue: "3p",
      comparisonIndex: 3,
    },
    "4F": {
      translationId: "4. podlaží",
      queryValue: "4p",
      comparisonIndex: 4,
    },
    "5F": {
      translationId: "5. podlaží",
      queryValue: "5p",
      comparisonIndex: 5,
    },
    "6F": {
      translationId: "6. podlaží",
      queryValue: "6p",
      comparisonIndex: 6,
    },
    "7F": {
      translationId: "7. podlaží",
      queryValue: "7p",
      comparisonIndex: 7,
    },
  },
  OFFER_AGE: {
    DAY: {
      translationId: "Den",
      queryValue: "den",
    },
    WEEK: {
      translationId: "Týden",
      queryValue: "tyden",
    },
    MONTH: {
      translationId: "Měsíc",
      queryValue: "mesic",
    },
  },
  TOTAL_ROOMS: {
    "1": {
      apiId: "1",
      translationId: "1 pokoj",
      queryValue: "1-pokoj",
    },
    "2": {
      apiId: "2",
      translationId: "2 pokoje",
      queryValue: "2-pokoje",
    },
    "3": {
      apiId: "3",
      translationId: "3 pokoje",
      queryValue: "3-pokoje",
    },
    "4": {
      apiId: "4",
      translationId: "4 pokoje",
      queryValue: "4-pokoje",
    },
    "5": {
      apiId: "5",
      translationId: "5 a více",
      queryValue: "5-vice",
    },
    ATYPICAL: {
      apiId: "ATYPICAL",
      translationId: "Atypický",
      queryValue: "atypicky-pokoje",
    },
  },
  FURNISHING: {
    NONE: {
      apiId: "NONE",
      translationId: "Bez vybavení",
      queryValue: "bez-vybaveni",
      description: "Základní zařízení",
    },
    MEDIUM: {
      apiId: "MEDIUM",
      translationId: "Částečně zařízené",
      queryValue: "castecne",
      description: "Včetně drobného nábytku",
    },
    FULL: {
      apiId: "FULL",
      translationId: "Plně zařízené",
      queryValue: "zarizeny",
      description: "Kompletně vybaveno",
    },
  },
  AVAILABILITY_TYPE: {
    ASAP: {
      translationId: "Ihned",
      queryValue: "ihned",
    },
    DATE: {
      translationId: "Konkrétní datum",
      queryValue: "datum",
    },
  },
  COHABITING_TYPE: {
    WHOLE_ROOM: {
      apiId: "WHOLE_ROOM",
      translationId: "Celý pokoj",
      queryValue: "cely-pokoj",
    },
    SHARED_ROOM: {
      apiId: "SHARED_ROOM",
      translationId: "Sdílený pokoj",
      queryValue: "sdileny-pokoj",
    },
  },
  FLAT_TYPE: {
    MESONET: {
      apiId: "MESONET",
      translationId: "Mezonet",
      queryValue: "mezonet",
    },
    LOFT: {
      apiId: "LOFT",
      translationId: "Loft",
      queryValue: "loft",
    },
    ATTIC: {
      apiId: "ATTIC",
      translationId: "Podkroví",
      queryValue: "podkrovi",
    },
  },
  SURROUNDINGS: {
    RESIDENTIAL: {
      queryValue: "obytna",
      translationId: "Obytná",
    },
    BUSINESS_RESIDENTIAL: {
      queryValue: "obchodni-obytna",
      translationId: "Obchodní a obytná",
    },
    BUSINESS: {
      queryValue: "obchodni",
      translationId: "Obchodní",
    },
    COMMERCIAL: {
      queryValue: "komercni",
      translationId: "Komerční",
    },
    INDUSTRIAL: {
      queryValue: "prumyslova",
      translationId: "Průmyslová",
    },
    RURAL: {
      queryValue: "venkovska",
      translationId: "Venkovská",
    },
    RECREATIONAL: {
      queryValue: "rekreacni",
      translationId: "Rekreační",
    },
    RECREATIONAL_UNUSED: {
      queryValue: "rekreacni-nevyuzita",
      translationId: "Rekreační nevyužitá",
    },
  },
  LOCATION_TYPE: {
    TOWN_CENTER: {
      queryValue: "centrum",
      translationId: "Centrum obce",
    },
    QUIET_PART: {
      queryValue: "klidna-cast",
      translationId: "Klidná část obce",
    },
    BUSY_PART: {
      queryValue: "rusna-cast",
      translationId: "Rušná část obce",
    },
    OUTSKIRTS: {
      queryValue: "okraj",
      translationId: "Okraj obce",
    },
    HOUSING_ESTATE: {
      queryValue: "sidliste",
      translationId: "Sídlište",
    },
    SEMI_SOLITUDE: {
      queryValue: "polosamota",
      translationId: "Polosamota",
    },
    SOLITUDE: {
      queryValue: "samota",
      translationId: "Samota",
    },
  },
  PROTECTED_AREA: {
    PROTECTION_ZONE: {
      apiId: "PROTECTION_ZONE",
      translationId: "Ochranné pásmo",
      queryValue: "pasmo",
    },
    CHKO: {
      apiId: "CHKO",
      translationId: "CHKO",
      queryValue: "chko",
    },
    NATIONAL_PARK: {
      apiId: "NATIONAL_PARK",
      translationId: "Národní park",
      queryValue: "nar-park",
    },
  },
  OBJECT_TYPE: {
    SEPARATE: {
      apiId: "SEPARATE",
      translationId: "Samostatný",
      queryValue: "samostatny",
    },
    ROW: {
      apiId: "ROW",
      translationId: "Řadový",
      queryValue: "radovy",
    },
    BLOCK: {
      apiId: "BLOCK",
      translationId: "V bloku",
      queryValue: "v-bloku",
    },
    CORNER: {
      apiId: "CORNER",
      translationId: "Rohový",
      queryValue: "rohovy",
    },
  },
  BUILDING_TYPE: {
    GROUND: {
      apiId: "GROUND",
      translationId: "Přízemní",
      queryValue: "prizemni",
    },
    STOREY: {
      apiId: "STOREY",
      translationId: "Patrová",
      queryValue: "patrova",
    },
  },
  HOUSE_TYPE: {
    FAMILY: {
      id: "FAMILY",
      translationId: "Rodinný",
      queryValue: "rodinny",
    },
    MORE_GENERATION: {
      id: "MORE_GENERATION",
      translationId: "Vícegenerační",
      queryValue: "vicegeneracni",
    },
    VILLA: {
      id: "VILLA",
      translationId: "Vila",
      queryValue: "vila",
    },
    HUT: {
      id: "HUT",
      translationId: "Chata",
      queryValue: "chata",
    },
    LODGE: {
      id: "LODGE",
      translationId: "Chalupa",
      queryValue: "chalupa",
    },
    FARMSTEAD: {
      id: "FARMSTEAD",
      translationId: "Zemědělská usedlost",
      queryValue: "usedlost",
    },
    ON_KEY: {
      id: "ON_KEY",
      translationId: "Na klíč",
      queryValue: "na-klic",
    },
    MONUMENT_OTHER: {
      id: "MONUMENT_OTHER",
      translationId: "Památka/jiné",
      queryValue: "pamatka",
    },
  },
  HOUSE_CONVENIENCES: {
    POOL: {
      translationId: "Bazen",
      queryValue: "s-bazenem",
    },
    CELLAR: {
      translationId: "Sklep",
      queryValue: "sklep",
    },
    GARAGE: {
      translationId: "Garaz",
      queryValue: "s-garazem",
    },
    PARKING: {
      translationId: "Parkovani",
      queryValue: "s-parkovanim",
    },
    BARRIER_FREE: {
      translationId: "Bezbarierove",
      queryValue: "bezbarierove",
    },
  },
  OWNERSHIP: {
    PERSONAL: {
      apiId: "PERSONAL",
      translationId: "Osobní",
      queryValue: "osobni",
    },
    ASSOCIATION: {
      apiId: "ASSOCIATION",
      translationId: "Družstevní",
      queryValue: "druzstevni",
    },
    MUNICIPAL: {
      apiId: "MUNICIPAL",
      translationId: "Obecní",
      queryValue: "obecni",
    },
  },
  ENERGY_EFFICIENCY: {
    A: {
      translationId: "A",
      translationLong: "A - Mimořádně úsporná",
      queryValue: "a",
    },
    B: {
      translationId: "B",
      translationLong: "B - Velmi úsporná",
      queryValue: "b",
    },
    C: {
      translationId: "C",
      translationLong: "C - Úsporná",
      queryValue: "c",
    },
    D: {
      translationId: "D",
      translationLong: "D - Méně úsporná",
      queryValue: "d",
    },
    E: {
      translationId: "E",
      translationLong: "E - Nehospodárná",
      queryValue: "e",
    },
    F: {
      translationId: "F",
      translationLong: "F - Velmi nehospodárná",
      queryValue: "f",
    },
    G: {
      translationId: "G",
      translationLong: "G -  Mimořádně nehospodárná",
      queryValue: "g",
    },
  },
  MATERIAL: {
    BRICK: {
      translationId: "Cihlová",
      queryValue: "cihla",
    },
    PANEL: {
      translationId: "Panelová",
      queryValue: "panel",
    },
    ASSEMBLED: {
      translationId: "Montovaná",
      queryValue: "montovany",
    },
    SKELETON: {
      translationId: "Skeletová",
      queryValue: "skeleton",
    },
    STONE: {
      translationId: "Kamenná",
      queryValue: "kamen",
    },
    WOOD: {
      translationId: "Dřevěná",
      queryValue: "drevo",
    },
    MIXED: {
      translationId: "Smíšená",
      queryValue: "smiseny",
    },
  },
  CONDITION: {
    VERY_GOOD: {
      translationId: "Velmi dobrý",
      queryValue: "velmi-dobry",
    },
    GOOD: {
      translationId: "Dobrý",
      queryValue: "dobry",
    },
    BAD: {
      translationId: "Špatný",
      queryValue: "spatny",
    },
    NEW: {
      translationId: "Novostavba",
      queryValue: "novostavba",
    },
    PROJECT: {
      translationId: "Projekt",
      queryValue: "projekt",
    },
    IN_CONSTRUCTION: {
      translationId: "Ve výstavbě",
      queryValue: "ve-vystavbe",
    },
    PRE_RECONSTRUCTION: {
      translationId: "Před rekonstrukcí",
      queryValue: "pred-rekonstrukci",
    },
    POST_RECONSTRUCTION: {
      translationId: "Po rekonstrukci",
      queryValue: "po-rekonstrukci",
    },
    IN_RECONSTRUCTION: {
      translationId: "V rekonstrukci",
      queryValue: "v-rekonstrukci",
    },
    FOR_DEMOLITION: {
      translationId: "K demolici",
      queryValue: "k-demolici",
    },
  },
  CONVENIENCES: {
    CELLAR: {
      translationId: "Sklep",
      queryValue: "sklep",
    },
    DISHWASHER: {
      translationId: "Myčka",
      queryValue: "mycka",
    },
    WASHING_MACHINE: {
      translationId: "Pračka",
      queryValue: "pracka",
    },
    FRIDGE: {
      translationId: "Lednička",
      queryValue: "lednice",
    },
    BALCONY: {
      translationId: "Balkón",
      queryValue: "s-balkonem",
    },
  },
  OFFER_SORTING: {
    BEST: {
      translationId: "Nejlepší",
      queryValue: "nejlepsi",
    },
    LATEST: {
      translationId: "Nejnovější",
      queryValue: "nejnovejsi",
    },
    CHEAPEST: {
      translationId: "Nejlevnější",
      queryValue: "levne",
    },
  },
  PROPERTY_TYPE: {
    FLAT: {
      apiId: "FLAT",
      translationId: "Byt",
      queryValue: "bytu",
    },
    HOUSE: {
      apiId: "HOUSE",
      translationId: "Dům",
      queryValue: "domu",
    },
  },
  OFFER_TYPE: {
    SALE: {
      apiId: "SALE",
      translationId: "Prodej",
      queryValue: "prodej",
    },
    RENT: {
      apiId: "RENT",
      translationId: "Pronájem",
      queryValue: "pronajem",
    },
    COHABITING: {
      apiId: "COHABITING",
      translationId: "Spolubydlení",
      queryValue: "spolubydleni",
    },
  },
  DISPOSITIONS: {
    "1KK": {
      id: EApiDispositionId.RENT_1_PLUS_KK,
      translationId: "1+kk",
      queryValue: "1+kk",
    },
    "1+1": {
      id: EApiDispositionId.RENT_1_PLUS_1,
      translationId: "1+1",
      queryValue: "1+1",
    },
    "2KK": {
      id: EApiDispositionId.RENT_2_PLUS_KK,
      translationId: "2+kk",
      queryValue: "2+kk",
    },
    "2+1": {
      id: EApiDispositionId.RENT_2_PLUS_1,
      translationId: "2+1",
      queryValue: "2+1",
    },
    "3KK": {
      id: EApiDispositionId.RENT_3_PLUS_KK,
      translationId: "3+kk",
      queryValue: "3+kk",
    },
    "3+1": {
      id: EApiDispositionId.RENT_3_PLUS_1,
      translationId: "3+1",
      queryValue: "3+1",
    },
    "4KK": {
      id: EApiDispositionId.RENT_4_PLUS_KK,
      translationId: "4+kk",
      queryValue: "4+kk",
    },
    "4+1": {
      id: EApiDispositionId.RENT_4_PLUS_1,
      translationId: "4+1",
      queryValue: "4+1",
    },
    "5KK": {
      id: EApiDispositionId.RENT_5_PLUS_KK,
      translationId: "5+kk",
      queryValue: "5+kk",
    },
    "5+1": {
      id: EApiDispositionId.RENT_5_PLUS_1,
      translationId: "5+1",
      queryValue: "5+1",
    },
    "6KK": {
      id: EApiDispositionId.RENT_6_PLUS_KK,
      translationId: "6+kk",
      queryValue: "6+kk",
    },
    "6+1": {
      id: EApiDispositionId.RENT_6_PLUS_1,
      translationId: "6+1",
      queryValue: "6+1",
    },
    "7KK": {
      id: EApiDispositionId.RENT_7_PLUS_KK,
      translationId: "7+kk",
      queryValue: "7+kk",
    },
    "7+1": {
      id: EApiDispositionId.RENT_7_PLUS_1,
      translationId: "7+1",
      queryValue: "7+1",
    },
    ATYPICAL: {
      id: EApiDispositionId.RENT_ATYPICAL,
      translationId: "Atypický",
      queryValue: "atypicky",
    },
    HOUSE: {
      id: EApiDispositionId.RENT_HOUSE,
      translationId: "Dům",
      queryValue: "dum",
    },
  },
  USER_TYPE: {
    PRIVATE_LANDLORD: {
      id: "PRIVATE_LANDLORD",
      translationId: "Soukromý majitel",
      queryValue: "offersSorting.latest",
    },
    REAL_ESTATE_BROKER: {
      id: "REAL_ESTATE_BROKER",
      translationId: "Realitní makléř",
      queryValue: "offersSorting.best",
    },
  },
};

export const {
  OFFER_SORTING,
  PROPERTY_TYPE,
  OFFER_TYPE,
  USER_TYPE,
  DISPOSITIONS,
  CONDITION,
  CONVENIENCES,
  AVAILABILITY_TYPE,
  BUILDING_TYPE,
  COHABITING_TYPE,
  ENERGY_EFFICIENCY,
  FLAT_TYPE,
  FLOOR_MAX,
  FLOOR_MIN,
  FLOOR_TOTAL_MAX,
  FLOOR_TOTAL_MIN,
  FURNISHING,
  HOUSE_CONVENIENCES,
  HOUSE_TYPE,
  LOCATION_TYPE,
  MATERIAL,
  OBJECT_TYPE,
  OFFER_AGE,
  OWNERSHIP,
  PROTECTED_AREA,
  SURROUNDINGS,
  TOTAL_ROOMS,
} = CONFIG;
