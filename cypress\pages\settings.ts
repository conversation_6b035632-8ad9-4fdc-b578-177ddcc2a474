import { animalsType, animalSet } from "../constants/types";
import { confirmAlertModal, waitForPageLoaded } from "./core";

const el = {
  myProfileButton: "profil.button.editProfile",
  component: "profil.edit.component",
  progressBar: "profil.edit.progressBar",
  firstName: "profil.edit.nameInput",
  lastName: "profil.edit.lastNameInput",
  ocupation: "profil.edit.ocupation",
  rentStart: "offer.availability.dateYear",
  birth: "profil.edit.yearOfBirth",
  duration: "profil.select.acmDuration",
  animal: "profil.select.animal",
  child: "profil.select.children",
  phone: "profil.offerContact.phone",
  peopleCount: "profil.select.peopleCount",
  info: "global.textarea",
  saveButton: "profil.button.save",
  uploadPictButton: "uploadPictureButton",
  uploadPictInput: "uploadPictureInput",
  picture: "pictureProfile",
  approvePromo: "promo.sale.button.approval",
  alerModalConfirm: "alertModal.button",
  phonePrefix: "global.phoneInput.suggestPrefix",
  alertModal: {
    box: "alertModal",
    heading: "alertModal.heading",
    text: "alertModal.text",
    button: "alertModal.button",
    closeButton: "alertModal.closeButton",
  },
  warning: "offer.availability.warningText",
  invoices: "profile.documents.invoices",
  passwordChange: "profile.changePassword",
  logout: "profile.logout",
  passwordChangePage: "changePasswordSettings",
  oldPassword: "oldPassword",
  newPassword: "newPassword",
  newPasswordAgain: "newPasswordAgain",
  submitButton: "submitButton",
};

export function changePassword(oldPass: string, newPass: string) {
  cy.intercept("POST", "**/user/new-password").as("newPassword");
  cy.getByTestId(el.passwordChange).parent().click();
  cy.getByTestId(el.passwordChangePage).should("be.visible");
  cy.wait(500);
  cy.getByTestId(el.oldPassword).type(oldPass, { force: true, delay: 50 });
  cy.getByTestId(el.newPassword).type(newPass, { force: true, delay: 50 });
  cy.getByTestId(el.newPasswordAgain).type(newPass, { force: true, delay: 50 });
  cy.getByTestId(el.submitButton).click({ force: true });
  cy.wait("@newPassword").its("response.statusCode").should("eq", 200);
  confirmAlertModal();
}

export function checkElementsMyProfile() {
  const elements = [el.invoices, el.passwordChange, el.logout];

  elements.forEach((element) => {
    cy.getByTestId(element).should("be.visible");
  });
}

export function checkWarningText() {
  cy.getByTestId(el.warning).should("be.visible").and("have.length", 1);
}

export function goToMyLanlordProfilSettings() {
  waitForPageLoaded();
  /* cy.getByTestId(el.approvePromo)
    .should("be.visible", { timeout: 60000 })
    .click();
  cy.getByTestId(el.alerModalConfirm).click();
  cy.getByTestId(el.alerModalConfirm).should("not.exist");*/
  cy.getByTestId(el.myProfileButton).click({ force: true });
  waitForPageLoaded();
  cy.getByTestId(el.component).should("be.visible");
  checkElementsMyProfile();
}

function fillIfDefined(element: string, value: string) {
  value !== undefined
    ? cy
        .getByTestId(element)
        .clear({ force: true })
        .type(value + "{enter}", { force: true })
    : undefined;
}

export function fillMyProfile(
  name?: any,
  surname?: any,
  ocup?: any,
  birth?: any,
  // rentStart?: any,
  duration?: any,
  animal?: animalsType,
  peopleCount?: any,
  info?: any,
) {
  fillIfDefined(el.firstName, name);
  fillIfDefined(el.lastName, surname);
  fillIfDefined(el.ocupation, ocup);
  // fillIfDefined(el.rentStart, rentStart);
  fillIfDefined(el.birth, birth);
  animal !== undefined
    ? cy.getByTestId(el.animal).select(animalSet[animal], { force: true })
    : undefined;
  duration !== undefined
    ? cy.getByTestId(el.duration).select(duration, { force: true })
    : undefined;
  peopleCount !== undefined
    ? cy.getByTestId(el.peopleCount).select(peopleCount, { force: true })
    : undefined;
  fillIfDefined(el.info, info);
  cy.wait(1000);
}

export function uploadPicture(picture: any) {
  cy.getByTestId(el.uploadPictInput).attachFile(picture);
  cy.then(() => {
    cy.getByTestId(el.picture)
      .invoke("attr", "src")
      .then((src) => {
        expect(src).to.not.includes("avatar");
      });
  });
}

export function saveMyProfile() {
  cy.intercept("POST", "**/renter-card").as("saveProfile");
  cy.getByTestId(el.saveButton).click({ force: true });
  cy.wait("@saveProfile").its("response.statusCode").should("eq", 200);
  dealWithPopupForMobileInfo();
  confirmAlertModal();
  waitForPageLoaded();
}

export function saveMyProfilePicture() {
  cy.intercept("POST", "**/photo").as("photoSave");
  cy.getByTestId(el.saveButton).click({ force: true });
  cy.wait("@photoSave").its("response.statusCode").should("eq", 200);
  waitForPageLoaded();
}

export function editPhoneNumber(number: string, prefix: string) {
  cy.getByTestId(el.phonePrefix)
    .clear({ force: true })
    .type(prefix + "{enter}", { force: true });
  cy.getByTestId(el.phone)
    .clear({ force: true })
    .type(number + "{enter}", { force: true });
}

function dealWithPopupForMobileInfo() {}
