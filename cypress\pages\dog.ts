import { dispositionType, dogNotification } from "../constants/types";
import { json2array } from "../helper/helper";
import { waitForPageLoaded } from "./core";
import { urls } from "../constants/ulrs";

const el = {
  watchDogPage: "wathchDogPageContainer",
  h1: "h1heading",
  text: "text",
  inputAdress: "inputForAdress",
  searchInput: "searchAdressInput",
  submitButton: "continueButton",
  mapVisible: "mapOfSelectedAdress",
  selectdataModal2: "watchDogModalComponent",
  dispositions: "watchdog.filter.dispositions",
  prize: "watchdog.filter.price",
  minPrice: "setPriceFrom",
  maxPrice: "setPriceTo",
  submitSaveButton: "saveAndContinueButton",
  selectdataModal3: "3stepFormDog",
  intervalSelect: "intervalOfNotificationToggle",
  setImmediate: 'input[value="IMMEDIATE"]',
  daily: 'input[value="ONCE_PER_DAY"]',
  weekly: 'input[value="ONCE_PER_WEEK"]',
  premiumModal: "premiumModal",
  closeModalButton: "premiumModal.closeButton",
  iWantPremiumButton: '[data-test="submitPremiumButton"]>a',
  completeButton: "completeButton",
  dogNameInput: "dogNameInput",
  dogCreatedConfirm: "watchdogCreatedModal",
  dogOKModal: "dogSuccesModal",
  dogOKHeading: "dogHeadingSuccessModal",
  dogOKText: "dogSuccessText",
  dogOKConfirmButton: "dogSuccessConfirmButton",
  myDogsTitle: "myDogsTitle",
  myDogsComponent: "myDogComponentSingle",
  dogEditButton: "editDogButton",
  dogDeleteButton: "deleteButton",
  myDogCard: "myDogSingleValue",
  myDogHeading: "myDogCardHeading",
  myDogStatus: "dogStatus",
  myDogStatusActive: "dogStatusActive",
  myDogActiveButton: "dogStatusActive",
  myDogShowOffers: '[data-test="showMeAllOffersForThisDog"]>a',
  editDogForm: "editDogInfo",
  submitEditButton: "submitEditsButton",
  cancelEditButton: "cancelEditsButton",
  dispositionEditButton: "dispozitionEditSelect",
  alertModal: "alertModal",
  submitModal: "alertModal.button",
  cancelModal: "alertModal.cancelButton",
  dogNameEdigInput: "dogNameEditInput",
  dogPromo: "watchDogAdvLinePromo",
  dogResults: {
    dogResultsList: "watchDogResultsList",
    dogResultName: "watchDogNameResult",
    dogResultDispo: "watchDogResultDisposition",
    dogResultNum: "watchDogResultNumberOfResults",
    // dogResultList: "watchDogListOfResultsAll",
    // dogResultSingle: "watchDogSingleResult",
  },
  dogModal: {
    modal: "watchdogOfferModal",
    content: "dogModalContent",
    heading: "dogModalHeading",
    notification: "notificationDogSettingModal",
    saveButton: "saveDogButton",
  },
  search: {
    modal: "watchdogModal",
    input: "dogNameInput",
    saveButton: "saveDogButton",
    content: "watchDotSearchModalContent",
    heading: "dogModalHeading",
    notification: "watchDogNotification",
  },
  dogRegInput: "registrationEmailInput",
};

export function checkLoginModal() {
  cy.getByTestId(el.dogModal.modal).within(() => {
    cy.getByTestId(el.dogRegInput).should("be.visible");
  });
}

export function emptyDogElementsCheck() {
  const elements = [
    el.watchDogPage,
    el.h1,
    el.text,
    el.inputAdress,
    el.searchInput,
  ];

  elements.forEach((element) => {
    cy.getByTestId(element).should("be.visible");
  });
}

export function typeAdressIntoSearch(adress: string) {
  cy.getByTestId(el.searchInput)
    .scrollIntoView()
    .find("input")
    .type(adress, { force: true })
    .type("{enter}", { force: true });
  cy.getByTestId(el.submitButton).should("be.visible");
  cy.getByTestId(el.mapVisible).should("be.visible");
  cy.getByTestId(el.submitButton).click({ force: true });
}

function fillMinMaxValue(priceMin?: any, priceMax?: any) {
  cy.getByTestId(el.minPrice)
    .clear({ force: true })
    .type(priceMin === undefined ? "" : priceMin, { force: true, delay: 50 });
  cy.getByTestId(el.maxPrice)
    .clear({ force: true })
    .type(priceMax === undefined ? "" : priceMax, { force: true, delay: 50 });
}

export function selectSpecificsOfDog(
  disposition: dispositionType,
  priceMin?: any,
  priceMax?: any,
) {
  const elements = [
    el.watchDogPage,
    el.selectdataModal2,
    el.dispositions,
    el.prize,
    el.minPrice,
    el.maxPrice,
    el.submitSaveButton,
  ];
  elements.forEach((element) => {
    cy.getByTestId(element).should("be.visible");
  });

  cy.scrollTo("top");
  cy.getByTestId(el.dispositions).within(() => {
    cy.getByTestId(`disposition.${disposition}`).click({ force: true });
    cy.getByTestId(`selected.disposition.${disposition}`).should("be.visible");
  });
  fillMinMaxValue(priceMin, priceMax);
  cy.getByTestId(el.submitSaveButton).click({ force: true });
}

export function nameMyDog(name: string) {
  const elements = [
    el.selectdataModal3,
    el.dogNameInput,
    el.intervalSelect,
    el.completeButton,
  ];
  const values = [el.setImmediate, el.daily, el.weekly];

  values.forEach((value) => {
    cy.get(value).should("be.visible");
  });

  elements.forEach((element) => {
    cy.getByTestId(element).should("be.visible");
  });
  cy.getByTestId(el.dogNameInput)
    .clear({ force: true })
    .type(name, { force: true });
}

function checkImidiateNotificationPremiumModal() {
  cy.get(el.setImmediate).click({ force: true });
  cy.getByTestId(el.premiumModal).should("be.visible");
  cy.get(el.iWantPremiumButton).should("be.visible");
  cy.getByTestId(el.closeModalButton).click();
}

export function selectDogNotification(notif: dogNotification) {
  checkImidiateNotificationPremiumModal();
  cy.get(el[notif]).click({ force: true });
}

export function dogOKModal() {
  const okModal = [
    el.dogOKModal,
    el.dogOKHeading,
    el.dogOKHeading,
    el.dogOKText,
    el.dogOKConfirmButton,
  ];

  okModal.forEach((element) => {
    cy.getByTestId(element).should("be.visible");
  });

  cy.getByTestId(el.dogOKConfirmButton).click();
  cy.getByTestId(el.dogOKModal).should("not.exist");
}

export function saveDog(loginStatus?: boolean) {
  cy.intercept("POST", "**/watchdog").as("createWatchDog");
  cy.intercept("GET", "**/watchdogs").as("watchDogListRefreh");
  cy.getByTestId(el.completeButton).click({ force: true });
  loginStatus === undefined ? true : loginStatus;
  if (loginStatus === true) {
    cy.wait("@createWatchDog")
      .its("response")
      .then((resp) => {
        expect(resp?.statusCode).to.equal(200);
        expect(resp?.body).to.have.property("msg");
        expect(resp?.body).to.have.property("status");
        expect(resp?.body.status).to.equal("ok");
      });
    dogOKModal();

    cy.wait("@watchDogListRefreh")
      .its("response")
      .then((resp) => {
        expect(resp?.statusCode).to.equal(200);
      });
  }
}

export function confirmMyDogList(dogName: string) {
  const elements = [
    el.myDogsTitle,
    el.myDogsComponent,
    el.myDogHeading,
    el.myDogStatusActive,
    el.myDogStatusActive,
    el.dogDeleteButton,
    el.dogEditButton,
  ];

  elements.forEach((element) => {
    cy.getByTestId(element).should("be.visible");
  });

  cy.get(el.myDogShowOffers).should("be.visible");

  cy.getByTestId(el.myDogHeading).contains(dogName).should("be.visible");
}

function confirmAlertModalAndClose() {
  cy.getByTestId(el.alertModal).within(() => {
    cy.getByTestId(el.submitModal).click();
  });
  cy.getByTestId(el.alertModal).should("not.exist");
}

export function deleteDogWatch(dogName: string) {
  cy.intercept("DELETE", "**//watchdog/**").as("deleteDogWatch");

  cy.getByTestId(el.watchDogPage)
    .contains(dogName)
    .parents(`[data-test="${el.myDogsComponent}"]`)
    .within(() => {
      cy.getByTestId(el.dogDeleteButton).first().click();
    });
  cy.getByTestId(el.alertModal).within(() => {
    cy.getByTestId(el.submitModal).click();
  });
  cy.wait("@deleteDogWatch")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.equal(200);
      expect(resp?.body).to.have.property("status");
      expect(resp?.body.status).to.equal("ok");
    });
  confirmAlertModalAndClose();
}

export function editdogWatch(
  dogName: string,
  newName: string,
  oldDispo: dispositionType,
  newDispo: dispositionType,
  priceMin?: string,
  priceMax?: string,
) {
  cy.intercept("PUT", "**/watchdog/**").as("editdogWatch");
  cy.intercept("GET", "**/watchdogs").as("watchDogListRefreshEdit");
  cy.getByTestId(el.watchDogPage)
    .contains(dogName)
    .parents(`[data-test="${el.myDogsComponent}"]`)
    .within(() => {
      cy.getByTestId(el.dogEditButton).first().click();
    });
  cy.getByTestId(el.editDogForm).within(() => {
    cy.getByTestId(el.dogNameEdigInput)
      .clear({ force: true })
      .type(newName, { force: true });
    oldDispo === undefined || newDispo === undefined
      ? undefined
      : cy
          .getByTestId(`selected.disposition.${oldDispo}`)
          .click({ force: true })
          .getByTestId(`disposition.${newDispo}`)
          .click({ force: true });
    fillMinMaxValue(priceMin, priceMax);
    cy.getByTestId(el.cancelEditButton).should("be.visible");
    cy.getByTestId(el.submitEditButton).click({ force: true });
    cy.wait("@editdogWatch")
      .its("response")
      .then((resp) => {
        expect(resp?.statusCode).to.equal(200);
        expect(resp?.body).to.have.property("msg");
        expect(resp?.body).to.have.property("status");
        expect(resp?.body.status).to.equal("ok");
      });
  });
  confirmAlertModalAndClose();
  cy.wait("@watchDogListRefreshEdit")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.equal(200);
    });
}

export function visitDogOffers(dogName: string) {
  cy.intercept("GET", "**/watchdog/detail/**/").as("watchoDogListOffers");
  cy.getByTestId(el.watchDogPage)
    .contains(dogName)
    .parents(`[data-test="${el.myDogsComponent}"]`)
    .within(() => {
      cy.get(el.myDogShowOffers).first().click();
    });
  waitForPageLoaded();
  /* cy.wait("@watchoDogListOffers")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.equal(200);
      cy.url().should("include", resp?.body.id);
    });*/
}

export function resultsDogOffers(dogName: string) {
  json2array(el.dogResults).forEach((elements: any) => {
    cy.getByTestId(elements).should("be.visible");
  });
  cy.getByTestId(el.dogResults.dogResultName)
    .contains(dogName)
    .should("be.visible");
}

export function createWatchDogByModal(notif?: dogNotification) {
  cy.intercept("POST", "**/watchdog/add-by-offer").as("addDogByOffer");
  json2array(el.dogModal).forEach((elements: any) => {
    cy.getByTestId(elements).should("be.visible");
  });
  notif === undefined ? undefined : cy.get(el[notif]).click({ force: true });
  cy.getByTestId(el.dogModal.saveButton).click();
  cy.wait("@addDogByOffer")
    .its("request")
    .then((req) => {
      localStorage.setItem("dogOfferId", req.body.offer_id);
    });
  cy.getByTestId(el.dogCreatedConfirm).within(() => {
    cy.getByTestId(el.dogOKConfirmButton).click();
  });
  cy.getByTestId(el.dogCreatedConfirm).should("not.exist");
}

export function confirmDogCreatedByModal() {
  cy.intercept("GET", "**/watchdogs").as("watchDogsLoadg");
  cy.visit(urls.DOG);
  cy.wait("@watchDogsLoadg")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.equal(200);
    });
  cy.getByTestId(el.myDogCard).should("have.length.at.least", 1);
}

export function searchDogModal(dogName: string) {
  cy.intercept("POST", "**/watchdog").as("addDogBySearch");
  json2array(el.search).forEach((elements: any) => {
    cy.getByTestId(elements).should("be.visible");
  });
  cy.getByTestId(el.search.input).clear().type(dogName);
  cy.getByTestId(el.search.saveButton).focus().click({ force: true });
  cy.wait("@addDogBySearch")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.equal(200);
    });
  dogOKModal();
}
