// yarn cypress run --headless --spec "cypress/e2e/checkLogin.cy.ts"

import { faker } from "@faker-js/faker";

import * as core from "../pages/core";
import * as login from "../pages/login";

const fakerEmail = faker.internet.email();
const fakerEmailMobile = faker.internet.email();
const basicUser = {
  login: `${faker.word.noun().toLowerCase()}-${Math.round(
    Date.now() / 1000000,
  )}@ulovdomov.cz`,
  password: "testujeme",
};
const basicUserMobile = {
  login: `${faker.word.noun().toLowerCase()}-${Math.round(
    Date.now() / 1000000,
  )}@ulovdomov.cz`,
  password: "testujeme",
};
const regPassword = "ulovdomov";
const inHousing = "signUpModal.userType.inHousing";
const realEstate = "signUpModal.userType.realEstate";
const privateLanlord = "signUpModal.userType.privateLanlord";

describe("Log In, Log Out, Register Test", () => {
  context("Desktop view", () => {
    before(() => {
      cy.apiRegister(basicUser);
    });

    beforeEach(() => {
      cy.visit("/");
      core.checkCookies();
      cy.intercept("POST", "**/fe-api/user/email-exists").as("email-exist");
      cy.intercept("POST", "**/fe-api/forgotten-password").as("email-sent");
      cy.intercept("POST", "**/api/v1/user").as("email-reg");
      cy.intercept("GET", `**/${basicUser.login}`).as("login-mail");
    });

    it("TC051 | Check LoginModal No Data ", () => {
      login.goToLoginModal();
      login.enterBadDataToLogin();
      login.closeLoginModal();
    });

    it("TC052 | Check LoginModal Invalid Data ", () => {
      login.goToLoginModal();
      login.enterBadDataToLogin("testuju@");
      login.closeLoginModal();
    });

    it("TC053 | Check SignModal Password No Data", () => {
      login.goToLoginModal();
      login.fillLoginMail(basicUser.login);
      login.continueToPassword();
      login.enterBadDataToPassword();
      login.closeLoginModal();
    });

    it("TC054 | Check SignModal Password Invalid Data", () => {
      login.goToLoginModal();
      login.fillLoginMail(basicUser.login);
      login.continueToPassword();
      login.enterBadDataToPassword("123");
      login.closeLoginModal();
    });

    it("TC055 | Check LoginModal Succesful Login", () => {
      login.goToLoginModal();
      login.fillLoginMail(basicUser.login);
      login.continueToPassword();
      login.fillPassword(basicUser.password);
      login.checkLogInSucces(basicUser.login);
    });

    it("TC056 | Check Succesful Log Out", () => {
      login.goToLoginModal();
      login.fillLoginMail(basicUser.login);
      login.continueToPassword();
      login.fillPassword(basicUser.password);
      login.checkLogInSucces(basicUser.login);
      login.checkLogOutSucces();
    });

    it("TC057 | Check Forgotten Password Invalid Email", () => {
      login.goToLoginModal();
      login.fillLoginMail(basicUser.login);
      login.continueToPassword();
      login.closeLoginModal();
    });

    it("TC058 | Check Forgotten Password & Valid Status Code", () => {
      login.goToLoginModal();
      login.fillLoginMail(basicUser.login);
      login.continueToPassword();
      login.clickForgottenPass();
      login.clickResetPass();
      login.closeLoginModal();
    });

    it("TC059 | Check Forgotten Password & Invalid Status Code", () => {
      for (let i = 0; i < 4; i++) {
        login.goToLoginModal();
        login.fillLoginMail(basicUser.login);
        login.continueToPassword();
        login.clickForgottenPass();
        login.clickResetPass();
        login.closeLoginModal();
      }
    });

    it("TC060 | Check Registration Open", () => {
      login.goToLoginModal();
      login.fillLoginMail(fakerEmail);
      login.continueToPassword();
      login.closeLoginModal();
    });

    it("TC061 | Check Registration Incomplete Data", () => {
      login.goToLoginModal();
      login.fillLoginMail(fakerEmail);
      login.continueToPassword();
      login.fillRegPassword(regPassword);
      login.checkGreySubmitButton();
      login.closeLoginModal();
    });

    it("TC062 | Check Complete Registration", () => {
      [inHousing, realEstate, privateLanlord].forEach((role) => {
        const fakerEmails = faker.internet.email(); // pre nahodny email 3krat
        login.goToLoginModal();
        login.fillLoginMail(fakerEmails);
        login.continueToPassword();
        login.fillRegPassword(regPassword);
        login.clickRegRadioButton(role);
        login.submitRegistration(fakerEmails);
        login.checkLogOutSucces();
      });
    });
  });

  context("Mobile view", () => {
    before(() => {
      cy.apiRegister(basicUserMobile);
    });

    beforeEach(() => {
      cy.viewport(390, 844);
      cy.visit("/");
      core.checkCookies();
      cy.intercept("POST", "**/fe-api/user/email-exists").as("email-exist");
      cy.intercept("POST", "**/fe-api/forgotten-password").as("email-sent");
      cy.intercept("POST", "**/api/v1/user").as("email-reg");
      cy.intercept("GET", `**/${basicUserMobile.login}`).as("login-mail");
    });

    it("TC051 | Check LoginModal No Data ", () => {
      login.goToLoginModalMobile();
      login.enterBadDataToLogin();
      login.closeLoginModal();
    });

    it("TC052 | Check LoginModal Invalid Data ", () => {
      login.goToLoginModalMobile();
      login.enterBadDataToLogin("testuju@");
      login.closeLoginModal();
    });

    it("TC053 | Check SignModal Password No Data", () => {
      login.goToLoginModalMobile();
      login.fillLoginMail(basicUserMobile.login);
      login.continueToPassword();
      login.enterBadDataToPassword();
      login.closeLoginModal();
    });

    it("TC054 | Check SignModal Password Invalid Data", () => {
      login.goToLoginModalMobile();
      login.fillLoginMail(basicUserMobile.login);
      login.continueToPassword();
      login.enterBadDataToPassword("123");
      login.closeLoginModal();
    });

    it("TC055 | Check LoginModal Succesful Login", () => {
      login.goToLoginModalMobile();
      login.fillLoginMail(basicUserMobile.login);
      login.continueToPassword();
      login.fillPassword(basicUserMobile.password);
      login.checkLoginSuccessMobile();
    });

    it("TC056 | Check Succesful Log Out", () => {
      login.goToLoginModalMobile();
      login.fillLoginMail(basicUserMobile.login);
      login.continueToPassword();
      login.fillPassword(basicUserMobile.password);
      login.checkLoginSuccessMobile();
      login.checkLogOutSuccesMobile();
    });

    it("TC057 | Check Forgotten Password Invalid Email", () => {
      login.goToLoginModalMobile();
      login.fillLoginMail(basicUserMobile.login);
      login.continueToPassword();
      login.closeLoginModal();
    });

    it("TC058 | Check Forgotten Password & Valid Status Code", () => {
      login.goToLoginModalMobile();
      login.fillLoginMail(basicUserMobile.login);
      login.continueToPassword();
      login.clickForgottenPass();
      login.clickResetPass();
      login.closeLoginModal();
    });

    it("TC059 | Check Forgotten Password & Invalid Status Code", () => {
      for (let i = 0; i < 4; i++) {
        login.goToLoginModalMobile();
        login.fillLoginMail(basicUserMobile.login);
        login.continueToPassword();
        login.clickForgottenPass();
        login.clickResetPass();
        login.closeLoginModal();
      }
    });

    it("TC060 | Check Registration Open", () => {
      login.goToLoginModalMobile();
      login.fillLoginMail(fakerEmailMobile);
      login.continueToPassword();
      login.closeLoginModal();
    });

    it("TC061 | Check Registration Incomplete Data", () => {
      login.goToLoginModalMobile();
      login.fillLoginMail(fakerEmailMobile);
      login.continueToPassword();
      login.fillRegPassword(regPassword);
      login.checkGreySubmitButton();
      login.closeLoginModal();
    });

    it("TC062 | Check Complete Registration", () => {
      [inHousing, realEstate, privateLanlord].forEach((role) => {
        const fakerEmails = faker.internet.email(); // pre nahodny email 3krat
        login.goToLoginModalMobile();
        login.fillLoginMail(fakerEmails);
        login.continueToPassword();
        login.fillRegPassword(regPassword);
        login.clickRegRadioButton(role);
        login.submitRegistrationMobile();
        login.checkLogOutSuccesMobile();
      });
    });
  });
});
