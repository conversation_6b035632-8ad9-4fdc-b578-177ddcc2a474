import React from "react";
import { useRouter } from "next/router";

import NewMessages from "scenes/NewMessages";
import { CustomAppPage } from "src/types/types";

const Index: CustomAppPage = () => {
  const { query } = useRouter();

  const params = {
    offerId:
      query.messages && query.messages[0] === "inzerent"
        ? Number(query.messages[1]) || undefined
        : undefined,
    curDialogId:
      query.messages && query.messages[0] === "kandidat"
        ? Number(query.messages[1]) || undefined
        : undefined,
  };

  return <NewMessages {...params} />;
};

Index.hasHiddenFooter = true;

export default Index;
