import { urls } from "../constants/ulrs";
import { json2array } from "../helper/helper";

const el = {
  navbar: "navbar",
  udLogo: "navbar.logoUlovDomov",
  addOffer: "navbar.content.addOffer",
  loginButton: "navbar.content.loginButton",
  hamburgerMenu: "navbar.hamburgerButton.icon",
  sidebarLogin: "navbar.sidebar.loginButton",
  search: "navbar.search",
  watchdog: "navbar.watchdog",
  myChatbox: "navbar.myChatbox",
  myOffer: "navbar.myOffer",
  spinner: ".chakra-spinner",
  avatar: "navbar.content.userAvatar",
  userMail: "navbar.content.userAvatar.mail",
  dialog: '[role="dialog"]',
  userNavBar: {
    myProfile: "navbar.sidebar.myProfile.text",
    myBox: "navbar.sidebar.myBox",
    watchDog: "navbar.sidebar.watchDog",
    selfCheck: "navbar.sidebar.selfCheck",
    //    topBoard: "navbar.sidebar.topBoard",
    myAds: "navbar.sidebar.myAds",
    creditCheck: "navbar.sidebar.creditCheck",
    logoutButton: "navbar.sidebar.logoutButton",
    sections: "sectionOfContent",
  },
  navBarName: "navbar.content.userAvatar.name",
  footer: {
    content: "footer.content",
    content_afterGeneral: "footer.content.afterGeneral",
    content_afterMain: "footer.content.afterMain",
    content_final: "footer.content.final",
    content_final_copyright: "footer.content.final.copyright",
    content_final_list: "footer.content.final.list",
    content_final_list_cookies: "footer.content.final.list.cookies",
    content_final_list_cookies_arrow: "footer.content.final.list.cookies.arrow",
    content_final_list_cookies_text: "footer.content.final.list.cookies.text",
    content_final_list_dataProcessingPersonal_arrow:
      "footer.content.final.list.dataProcessingPersonal.arrow",
    content_final_list_dataProcessingPersonal_text:
      "footer.content.final.list.dataProcessingPersonal.text",
    content_final_list_pageMap_arrow: "footer.content.final.list.pageMap.arrow",
    content_final_list_pageMap_text: "footer.content.final.list.pageMap.text",
    content_final_list_termsAndConditions_arrow:
      "footer.content.final.list.termsAndConditions.arrow",
    content_final_list_termsAndConditions_text:
      "footer.content.final.list.termsAndConditions.text",
    content_general: "footer.content.general",
    content_general_list_1: "footer.content.general.list-1",
    content_general_list_1_career: "footer.content.general.list-1.career",
    content_general_list_1_career_arrow:
      "footer.content.general.list-1.career.arrow",
    content_general_list_1_career_text:
      "footer.content.general.list-1.career.text",
    content_general_list_1_contact_arrow:
      "footer.content.general.list-1.contact.arrow",
    content_general_list_1_contact_text:
      "footer.content.general.list-1.contact.text",
    content_general_list_2: "footer.content.general.list-2",
    content_general_list_2_creditCheck_arrow:
      "footer.content.general.list-2.creditCheck.arrow",
    content_general_list_2_creditCheck_text:
      "footer.content.general.list-2.creditCheck.text",
    content_general_list_2_idealniNajemce:
      "footer.content.general.list-2.idealniNajemce",
    content_general_list_2_idealniNajemce_arrow:
      "footer.content.general.list-2.idealniNajemce.arrow",
    content_general_list_2_idealniNajemce_text:
      "footer.content.general.list-2.idealniNajemce.text",
    content_general_list_2_priceList_arrow:
      "footer.content.general.list-2.priceList.arrow",
    content_general_list_2_priceList_text:
      "footer.content.general.list-2.priceList.text",
    content_general_public: "footer.content.general.public",
    content_general_public_radce_icon:
      "footer.content.general.public.radce.icon",
    content_general_public_radce_text:
      "footer.content.general.public.radce.text",
    content_general_public_social: "footer.content.general.public.social",
    content_general_public_social_fb: "footer.content.general.public.social.fb",
    content_general_public_social_fb_icon:
      "footer.content.general.public.social.fb.icon",
    content_general_public_social_ig: "footer.content.general.public.social.ig",
    content_general_public_social_ig_icon:
      "footer.content.general.public.social.ig.icon",
    content_general_public_social_linkedIn:
      "footer.content.general.public.social.linkedIn",
    content_general_public_social_linkedIn_icon:
      "footer.content.general.public.social.linkedIn.icon",
  },
  alertModal: {
    box: "alertModal",
    heading: "alertModal.heading",
    text: "alertModal.text",
    button: "alertModal.button",
    closeButton: "alertModal.closeButton",
  },
};

export function checkNavBarVisitor() {
  const elements = [
    el.udLogo,
    el.addOffer,
    el.loginButton,
    el.hamburgerMenu,
    el.watchdog,
    el.myChatbox,
    el.myOffer,
  ];

  cy.url().then((url) => {
    if (!url.includes(urls.SEARCH) || !url.includes("pronajem-bytu")) {
      // elements = [el.search, ...elements];
    }
    elements.forEach((element) => {
      cy.getByTestId(element).should("be.visible");
    });
  });

  cy.getByTestId(el.sidebarLogin).should("not.exist");
}

export function checkFooter() {
  json2array(el.footer).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function waitForPageLoaded() {
  cy.get(el.spinner).should("not.exist", { setTimeout: 45000 });
}

export function checkCookies() {
  cy.get("div").then(() => {});
}

export function checkNavBarUser() {
  cy.getByTestId(el.avatar).should("be.visible");
  cy.getByTestId(el.userMail).should("be.visible");

  const elements = [
    el.udLogo,
    el.addOffer,
    el.hamburgerMenu,
    el.watchdog,
    el.myChatbox,
    el.myOffer,
  ];

  elements.forEach((element) => {
    cy.getByTestId(element).should("be.visible");
  });

  cy.getByTestId(el.hamburgerMenu).click();
  json2array(el.userNavBar).forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
  cy.getByTestId(el.userNavBar.sections).should("have.length", 5);
  cy.get(".chakra-modal__content-container").click();
  cy.getByTestId(el.userNavBar.sections).should("not.exist");
}

export function checkVisibleNameInHeader(name: string) {
  cy.getByTestId(el.navBarName).contains(name).should("be.visible");
}

export function confirmAlertModal() {
  cy.getByTestId(el.alertModal.box).should("be.visible");
  cy.getByTestId(el.alertModal.heading).should("be.visible");
  cy.getByTestId(el.alertModal.closeButton).should("be.visible");
  cy.getByTestId(el.alertModal.button).click();
  cy.getByTestId(el.alertModal.box).should("not.exist");
}

export function checkNavBarVisitorMobile() {
  const mobile = [el.navbar, el.udLogo, el.addOffer, el.hamburgerMenu];

  mobile.forEach((element) => {
    cy.getByTestId(element).should("be.visible");
  });
}
