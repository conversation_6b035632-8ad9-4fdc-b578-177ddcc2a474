import { GetServerSideProps } from "next/types";

import InsertOfferWrapper from "components/InsertOfferWrapper";
import ContactForm from "scenes/InsertOffer/form/Contact";
import { CustomAppPage, EditOfferUnpaid } from "src/types/types";

const Contact: CustomAppPage<EditOfferUnpaid> = (props) => (
  <InsertOfferWrapper title="Kontakt u inzerátu" {...props}>
    <ContactForm buttonNextText="Další" />
  </InsertOfferWrapper>
);

Contact.hasHiddenFooter = true;

export default Contact;

export const getServerSideProps: GetServerSideProps = async ({ query }) => ({
  props: {
    id: query.id as string,
    status: query.status as string,
  },
});
