{"name": "ulovdomov-fe", "version": "2.0.0", "private": true, "scripts": {"dev": "yarn predev && next", "dev:ssl": "node server.js", "dev:no-error": "NEXT_PUBLIC_DISPLAY_ERRORS=no-display && yarn predev && next", "dev:dummy": "NEXT_PUBLIC_DATA_TYPE=dummy && yarn predev && next", "dev:inspect": "NODE_OPTIONS='--inspect' next dev", "build": "yarn predev && next build", "start:development": "NODE_ENV=production ENV=development next start", "start:staging": "NODE_ENV=production ENV=staging node .next/standalone/server.js", "start:production": "NODE_ENV=production ENV=production next start", "start:production-k8s": "NODE_ENV=production ENV=production node .next/standalone/server.js", "build:dev": "yarn predev && NODE_ENV=production ENV=development next build", "build:dev:no-static": "yarn predev && NODE_ENV=production ENV=development:no-static next build", "build:prod": "yarn optimalization && yarn predev && NODE_ENV=production ENV=production next build && ENV=production next-sitemap", "build:prod-k8s": "yarn optimalization && yarn predev && OUTPUT_ENV=standalone NODE_ENV=production ENV=production next build && ENV=production next-sitemap && cp -Rf public/. .next/standalone/public && cp -Rf .next/static/. .next/standalone/.next/static", "build:prod:no-static": "yarn predev && NODE_ENV=production ENV=production-nostatic next build --debug", "build:stage": "yarn predev && OUTPUT_ENV=standalone NODE_ENV=production ENV=staging next build && ENV=staging next-sitemap && cp -Rf public/. .next/standalone/public && cp -Rf .next/static/. .next/standalone/.next/static", "codegen": "yarn codegen:auth && yarn codegen:graphql && yarn codegen:crm && yarn codegen:new-ud", "codegen:graphql": "graphql-codegen --config codegen.yml  && yarn eslint:fix:codegen:graphql", "codegen:auth": "npx @rtk-query/codegen-openapi ./src/api/phpApi/codegen-openapi/auth.config.ts && yarn eslint:fix:codegen", "codegen:new-ud": "npx @rtk-query/codegen-openapi ./src/api/newPhpApi/openapi-config.ts && yarn eslint:fix:codegen:new-ud", "codegen:crm": "npx @rtk-query/codegen-openapi ./src/api/nodeApi/openapi-config.ts && yarn eslint:fix:codegen:crm", "cyrun": "cypress open", "eslint:fix:codegen": "npx eslint --ignore-path .gitignore 'src/api/phpApi/__generated__/*' --ext .ts,.tsx --fix", "eslint:fix:codegen:graphql": "npx eslint --ignore-path .gitignore 'src/graphql/*' --ext .ts,.tsx --fix", "eslint:fix:codegen:crm": "npx eslint --ignore-path .gitignore 'src/api/nodeApi/__generated__/*' --ext .ts,.tsx --fix", "eslint:fix:codegen:new-ud": "npx eslint --ignore-path .gitignore 'src/api/newPhpApi/__generated__/*' --ext .ts,.tsx --fix", "eslint:fix:imports": "eslint 'src/routeImports.ts' --fix", "eslint:fix:icons": "eslint 'src/components/Chakra/Icons' --ignore-path .gitignore --ext .ts,.tsx --fix", "eslint:fix": "eslint --ignore-path .gitignore '**/*.{js,jsx,ts,tsx,json}' --fix", "prettier:fix:icons": "prettier --ignore-path .gitignore --write 'src/components/Chakra/Icons' --ext .ts,.tsx", "prettier:fix:imports": "prettier --ignore-path .gitignore --write 'src/routeImports.ts'", "prettier:fix": "prettier --ignore-path .gitignore --write '**/*.{js,jsx,ts,tsx,json}'", "fix:all": "yarn prettier:fix && yarn eslint:fix", "build-routes": "node scripts/build-routes.js && yarn eslint:fix:imports && yarn prettier:fix:imports", "predev": "yarn build-routes", "export": "next export -o dist", "source:translations": "npx ts-node ./scripts/sourceTranslations.ts", "lint": "npx eslint . --ext .ts,.tsx,.js", "check:all": "yarn check:eslint && yarn check:types && yarn check:prettier", "check:types": "npx tsc --noEmit", "check:eslint": "yarn eslint --ignore-path .gitignore '**/*.{js,jsx,ts,tsx,json}'", "check:prettier": "prettier --check --ignore-path .gitignore --write '**/*.{js,jsx,ts,tsx,json}'", "source:icons": "yarn source:icons:partial && yarn eslint:fix:icons && yarn prettier:fix:icons", "source:icons:partial": "svgo -f icons && node ./scripts/sourceIcons.js", "tests:integration": "npx ts-node ./tests/lib/index.ts", "pubdist:beta": "node ./scripts/publishDist.js --target beta", "pubdist:prod": "npx ts-node ./scripts/publishDist.ts --target production", "chrome:no-security": "open -na Google\\ Chrome --args --user-data-dir= --disable-web-security --disable-site-isolation-trials", "madge": "npx madge --ts-config ./tsconfig.json --extensions ts,tsx --circular src/index.tsx", "postinstall": "husky install && yarn source:icons", "prepare": "husky install", "optimalization:icons": "svgo -f public/img/icons/flags", "optimalization": "yarn optimalization:icons", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook", "analyze": "source-map-explorer .next/standalone/static/**/*.js", "sitemap": "yarn next-sitemap"}, "devDependencies": {"@babel/core": "^7.20.12", "@faker-js/faker": "^8.0.2", "@graphql-codegen/cli": "2.16.5", "@mdx-js/react": "^1.6.22", "@sentry/webpack-plugin": "^1.20.0", "@storybook/addon-actions": "^6.5.16", "@storybook/addon-docs": "^6.5.16", "@storybook/addon-essentials": "^6.5.16", "@storybook/addon-interactions": "^6.5.16", "@storybook/addon-links": "^6.5.16", "@storybook/builder-webpack5": "^6.5.16", "@storybook/manager-webpack5": "^6.5.16", "@storybook/react": "^6.5.16", "@storybook/testing-library": "^0.0.13", "@types/amplitude-js": "^8.16.2", "@types/googletag": "^3.0.2", "@types/history": "^5.0.0", "@types/i18n": "^0.13.6", "@types/js-cookie": "^3.0.2", "@types/lodash.throttle": "^4.1.7", "@types/md5": "^2.3.2", "@types/ramda": "^0.28.23", "@types/react": "^18.0.28", "@types/react-datepicker": "^4.8.0", "@types/react-dom": "^18.0.11", "@types/react-redux": "^7.1.25", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^5.49.0", "@typescript-eslint/parser": "^5.49.0", "babel-loader": "^9.1.2", "circular-dependency-plugin": "^5.2.2", "cypress": "^13.15.0", "eslint": "^8.33.0", "eslint-config-next": "^13.1.6", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.6.10", "husky": "^8.0.3", "lint-staged": "^13.2.0", "next-sitemap": "^2.5.28", "prettier": "^2.8.3", "rimraf": "^3.0.2", "source-map-explorer": "^2.5.3", "svgo": "^2.8.0", "ts-node": "^10.9.1", "typescript": "4.9.4", "webpack": "^5.75.0", "yargs": "^17.6.2"}, "dependencies": {"@analytics/google-tag-manager": "^0.5.3", "@chakra-ui/cli": "^2.3.1", "@chakra-ui/icons": "^2.0.17", "@chakra-ui/react": "^2.5.1", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@hookform/resolvers": "^2.9.11", "@next/bundle-analyzer": "^13.4.7", "@next/font": "^13.2.1", "@reduxjs/toolkit": "^1.9.2", "@rooks/use-key": "^4.11.2", "@rtk-query/codegen-openapi": "^1.0.0", "@rtk-query/graphql-request-base-query": "^2.2.0", "@sentry/nextjs": "^6.19.7", "@storyblok/react": "^1.3.5", "amplitude-js": "^8.21.4", "analytics": "^0.8.1", "axios": "^1.6.2", "cypress-file-upload": "^5.0.8", "date-fns": "^2.29.3", "dotenv": "^16.4.5", "embla-carousel-react": "^8.5.2", "framer-motion": "^6.5.1", "graphql": "^16.6.0", "graphql-request": "^5.1.0", "history": "5.3.0", "i18next": "^22.4.10", "joi": "^17.7.0", "js-cookie": "^3.0.1", "libphonenumber-js": "^1.10.37", "maplibre-gl": "^2.4.0", "md5": "^2.3.0", "next": "13.1.6", "next-seo": "^5.15.0", "nookies": "^2.5.2", "query-string": "^8.1.0", "ramda": "^0.28.0", "react": "^18.2.0", "react-datepicker": "^4.9.0", "react-dom": "^18.2.0", "react-dropzone": "14.2.3", "react-hook-form": "7.43.1", "react-i18next": "^12.2.0", "react-redux": "^8.0.5", "sharp": "^0.31.3", "storyblok-rich-text-react-renderer": "^2.6.1", "unfetch": "^4.2.0", "uuid": "^9.0.0"}, "author": "UlovDomov.cz s.r.o.", "license": "ISC", "lint-staged": {"src/**/*.{js,ts?(x)}": ["yarn prettier:fix"], "src/**/*.{js,ts,tsx}": ["yarn eslint:fix"]}, "packageManager": "yarn@3.2.1"}