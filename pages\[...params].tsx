import axios from "axios";
import { GetServerSideProps } from "next";
import { useRouter } from "next/router";

import { getLocationFromUrl, isSearchPath } from "helpers/search";
import Search from "scenes/Search";
import { DEFAULT_BOUNDS_CZECHIA } from "src/consts";
import { CustomAppPage } from "src/types/types";
import { IGeoBounds } from "utils/geo";
import Location2JsonApiEndpoint from "utils/geo/api/Location2Json";
import { getFilterFromUrl } from "scenes/Search/getFilterFromUrl";
import { FindRequest } from "@ud/api/newPhpApi/__generated__/newPhpApi.generated";
import { isOldSearchPath } from "helpers/search/paths";
import { getRedirectLink } from "helpers/search/getRedirectLink";

const Index: CustomAppPage<{
  count: number;
  location: string | null;
  bounds: IGeoBounds;
  pageIndex: number;
}> = ({ count, location, bounds, pageIndex }) => {
  const { query } = useRouter();
  const filter = getFilterFromUrl(query);

  const emptyResults = {
    count,
    offers: [],
    markers: [],
    pageIndex,
    totalPages: 10,
  };

  return (
    <Search
      initialResults={emptyResults}
      initialFilter={{ ...filter, bounds }}
      initialLocation={location ?? ""}
    />
  );
};

const getBounds = async (
  loc2Class: Location2JsonApiEndpoint,
  location?: string,
) => {
  if (!location) {
    return DEFAULT_BOUNDS_CZECHIA;
  }

  try {
    const { data: loc2Data } = await axios.post(
      process.env.NEXT_PUBLIC_API_URL + loc2Class.resolveUri(),
      {
        location,
      },
    );

    return (
      loc2Class.mapResponseData(0, loc2Data)?.bounds || DEFAULT_BOUNDS_CZECHIA
    );
  } catch {
    return DEFAULT_BOUNDS_CZECHIA;
  }
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  resolvedUrl,
  query: { params },
}) => {
  const newPath = (params as Array<string>).join("/");

  if (resolvedUrl.includes("%20")) {
    const replaced = resolvedUrl.replaceAll("%20", ";").replaceAll("%20", ";");

    return {
      redirect: {
        destination: replaced,
        permanent: true,
      },
    };
  }

  if (isOldSearchPath(newPath)) {
    return {
      redirect: {
        destination: getRedirectLink(query, true),
        permanent: true,
      },
    };
  }
  if (!isSearchPath(newPath)) {
    return {
      redirect: {
        destination: "/404",
        permanent: false,
      },
    };
  }

  const loc2jsonClass = new Location2JsonApiEndpoint();

  const location = getLocationFromUrl(query);

  const filter = getFilterFromUrl(query);
  if (!filter.bounds) {
    filter.bounds = await getBounds(loc2jsonClass, location);
  }
  const mainBody: FindRequest = {
    offerType: filter.offerType,
    bounds: filter.bounds,
  };

  try {
    const { data: realData } = await axios.post(
      `${process.env.NEXT_PUBLIC_NEW_PHP_API_URL}/v1/offer/find?page=${filter.pageIndex}&perPage=20&sorting=${filter.sorting}`,
      mainBody,
      { withCredentials: false },
    );

    return {
      props: {
        count: realData.extraData.total || 0,
        location: location ?? null,
        bounds: filter.bounds,
        pageIndex: realData.extraData.currentPage || 0,
      },
    };
  } catch {
    return {
      props: {
        count: 0,
        location: location ?? null,
        bounds: filter.bounds,
        pageIndex: 0,
      },
    };
  }
};

Index.hasHiddenFooter = true;

export default Index;
