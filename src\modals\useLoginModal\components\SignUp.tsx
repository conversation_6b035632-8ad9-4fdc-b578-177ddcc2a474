import Jo<PERSON> from "joi";
import { Stack, Text } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { FC, useEffect, useState } from "react";

import useRouter from "hooks/useRouter";
import useGlobalActions from "hooks/useGlobalActions";
import useForm, { OnFormSubmitType } from "hooks/useForm";
import useUserCRM from "hooks/useUserCRM";
import { TrackEventProps, useTracking } from "contexts/Tracking";
import {
  useCreateHistoryMutation,
  usePostAuthRegistrationMutation,
  UserBodyWithConsents,
} from "@ud/api";
import { ETypeUser } from "utils/user";
import UserType, { getTypeUser } from "./UserType";
import { ELoginStep } from "../types";
import AnimatedInput from "components/Chakra/AnimatedInput";
import Checkbox from "components/Chakra/Checkbox";
import Button from "components/Chakra/Button";
import { IUserTypeItemConfig } from "@ud/config/userType";

interface IProps {
  email: string;
  onSuccess: () => void;
  setStep: (step: ELoginStep) => void;
  typeUser?: IUserTypeItemConfig;
  isTypeUser?: boolean;
  trackEventPayload?: TrackEventProps;
}

interface IFormValues {
  email: string;
  password: string;
  isConsent: boolean;
  typeUser: number;
}

const SignUp: FC<IProps> = ({
  email,
  onSuccess,
  setStep,
  typeUser,
  isTypeUser,
  trackEventPayload,
}) => {
  const [isLoading, setLoading] = useState<boolean>(false);
  const [signUpNewUserApi] = usePostAuthRegistrationMutation();
  const [createUserHistory] = useCreateHistoryMutation();
  const { createUser } = useUserCRM();
  const { asPath } = useRouter();
  const [, typePage] = window.location.pathname.split("/");
  const initialValues: IFormValues = {
    email,
    password: "",
    isConsent: false,
    typeUser: ETypeUser.NONE,
  };
  const { refreshUser } = useGlobalActions();
  const { trackEvent } = useTracking();
  const { t } = useTranslation();

  const FormSchema = Joi.object({
    email: Joi.string().email({ tlds: false }).required(),
    password: Joi.string().min(6).max(30).required(),
    isConsent: Joi.boolean(),
    typeUser: Joi.number().custom((value, helpers) => {
      if (typeUser?.apiId && !isTypeUser) {
        return helpers.error("any.invalid");
      }

      return value;
    }),
  });

  const { registerWithError, handleSubmit, setError, watch, setValue } =
    useForm<IFormValues>(FormSchema, {
      defaultValues: initialValues,
    });

  const page = asPath === "/" ? "homepage" : typePage;

  const watchTypeUser = watch("typeUser");
  const isConsentWatch = watch("isConsent");

  const createConsent = async () => {
    const data: UserBodyWithConsents = {
      registrationDate: new Date().toISOString(),
      email,
      consents: [
        {
          consentType: "MARKETING",
          isAccepted: isConsentWatch,
          acceptFrom: page,
          acceptDate: new Date().toISOString(),
        },
      ],
      offers: [],
    };
    const valueUserType = watchTypeUser
      ? getTypeUser({ typeId: watchTypeUser })
      : typeUser;
    if (valueUserType) {
      data.type = valueUserType.newApiId;
    }
    const resUser = await createUser(data);
    if (resUser?.id) {
      createUserHistory({
        id: resUser.id,
        historyBody: {
          type: "REGISTRATION",
          date: new Date().toISOString(),
          page,
        },
      });
    }
  };

  useEffect(() => {
    trackEvent({
      "dl.eCat": page,
      "dl.eAct": "dialog.shown",
      "dl.eLab": "Type user – registrace",
    });
  }, []);

  const handleData: OnFormSubmitType<IFormValues> = ({
    email: userEmail,
    password,
  }) => {
    setLoading(true);
    const callAsync = async () => {
      try {
        const { accessToken, refreshToken } = await signUpNewUserApi({
          body: { email: userEmail, password },
        }).unwrap();

        if (!refreshToken || !accessToken) {
          throw Error("API ERROR");
        }

        localStorage.setItem("REFRESH_TOKEN", refreshToken);

        await refreshUser();

        if (trackEventPayload) {
          trackEvent(trackEventPayload);
        }
        createConsent();
        onSuccess();
      } catch (err) {
        const error = err as { data?: { error: string } };
        if (error && error?.data?.error === "ERR_MSG_INVALID_EMAIL") {
          setStep(ELoginStep.IDENTIFICATION_WITH_INVALID_EMAIL);
        } else {
          setStep(ELoginStep.IDENTIFICATION);
        }
        setError(
          "email",
          new Error(t("formErrors.suspiciousEmail") || undefined),
        );
      } finally {
        setLoading(false);
      }
    };
    callAsync();
  };

  return (
    <form
      onSubmit={handleSubmit(handleData)}
      data-test="loginModal.signUp.form"
    >
      <AnimatedInput
        {...registerWithError("password")}
        dataTest="loginModal.signUp.form.input"
        type="password"
        autoComplete="new-password"
        label="Nové heslo"
      />
      <Stack>
        <Checkbox
          _hover={{ ringColor: "none" }}
          isChecked={isConsentWatch}
          {...registerWithError("isConsent")}
          label={
            <Text
              variant="small_2"
              data-test="loginModal.signUp.form.consent.text"
            >
              Chci dostávat e-mailem{" "}
              <Button
                onClick={() => setStep(ELoginStep.GDPR_POP_UP)}
                dataTest="loginModal.signUp.form.consent.text.gdpr"
                variant="link"
                size="sm"
              >
                novinky a tipy
              </Button>{" "}
              ze světa pronájmů.
            </Text>
          }
          data-test="loginModal.signUp.form.consent"
          onKeyDown={(e) => {
            if (e.code === "Enter" || e.code === "Space") {
              e.preventDefault();
              setValue("isConsent", !isConsentWatch);
            }
          }}
        />
      </Stack>
      {!isTypeUser && (
        <UserType
          setValue={setValue}
          typeUser={watchTypeUser}
          margin={{ mb: "32px", mt: ["8px", "16px"] }}
          page={page}
        />
      )}
      <Button
        dataTest="loginModal.signUp.form.button"
        type="submit"
        w="100%"
        mb="10px"
        isLoading={isLoading}
        isDisabled={typeUser === undefined && watchTypeUser === ETypeUser.NONE}
      >
        Vytvořit účet a pokračovat
      </Button>
    </form>
  );
};

export default SignUp;
