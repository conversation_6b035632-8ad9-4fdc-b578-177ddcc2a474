import React from "react";
import {
  GetServerSidePropsContext,
  GetServerSidePropsResult,
  NextPage,
} from "next";
// import ToCheck from "scenes/Redirects/ToCheck";

// const Index: NextPage = () => {
//   return <ToCheck />;
// };

// export default Index;

const Index: NextPage = () => <div />;

export async function getServerSideProps({}: GetServerSidePropsContext): Promise<
  GetServerSidePropsResult<undefined>
> {
  return {
    redirect: {
      permanent: false,
      destination: "/provereni-rizikovosti-osob",
    },
  };
}

export default Index;
