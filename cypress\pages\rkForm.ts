import { json2array } from "../helper/helper";

const el = {
  heading: "rkform.heading",
  text: "rkform.text",
  points: "rkform.points",
  coworkButton: "rkform.coworkButton",
  tenantText: "rkform.tenantText",
  tenantPoints: "rkform.tenantPoints",
  protectClientsHeading: "rkform.protectClientsHeading",
  protectClientsText1: "rkform.protectClientsText1",
  protectClientsText2: "rkform.protectClientsText2",
  creditCheckButton: "rkform.creditCheckButton",
  patternReport: "rkform.downloadPatternReport",
  advertiseHeading: "rkform.advertiseHeading",
  advertiseItems: "rkform.advertiseItems",
  formHeading: "rkform.formHeading",
  formText: "rkform.formText",
  agency: "rkform.agency",
  software: "rkform.sotfware",
  name: "rkform.name",
  email: "rkform.email",
  phone: "global.phoneInput.phoneNumberInput",
  phoneDiv: "global.phoneInput",
  formSubmit: "rkform.submitButton",
  contactBadge: "rkform.contactBadge",
  coopOverview: "rkform.CoopOverview",
};

const alertModal = {
  heading: "alertModal.heading",
  text: "alertModal.text",
  button: "alertModal.button",
};

export function checkElements() {
  json2array(el).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function checkAlertModal() {
  json2array(alertModal).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function checkLinks() {
  cy.getByTestId(el.patternReport)
    .invoke("attr", "href")
    .then((text) => {
      cy.getByTestId(el.patternReport)
        .should("have.attr", "href")
        .and("include", text);
    });
  cy.getByTestId(el.creditCheckButton)
    .invoke("attr", "href")
    .then((text) => {
      cy.getByTestId(el.creditCheckButton)
        .should("have.attr", "href")
        .and("include", text);
    });
  cy.getByTestId(el.coopOverview)
    .invoke("attr", "href")
    .then((text) => {
      cy.getByTestId(el.coopOverview)
        .should("have.attr", "href")
        .and("include", text);
    });
}

export function fillForm(data: any) {
  cy.getByTestId(el.agency).clear().type(data.agencyName);
  cy.getByTestId(el.software).clear().type(data.software);
  cy.getByTestId(el.name).clear().type(data.name);
  cy.getByTestId(el.email).clear().type(data.email);
  cy.getByTestId(el.phone).clear().type(data.phone);
}

export function submitFormButton() {
  cy.getByTestId(el.formSubmit).click({ force: true });
  cy.wait("@contact-form")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.eq(200);
      cy.getByTestId(el.formSubmit).click({ force: true });
      checkAlertModal();
      cy.getByTestId(alertModal.button).click({ force: true });
      checkElements();
    });
}

export function submitFormButtonInvalidData() {
  cy.getByTestId(el.formSubmit).click({ force: true });
  cy.get("input:invalid").should("have.length", 1);
}
