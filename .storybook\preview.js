import React from "react";
import { addDecorator } from "@storybook/react";

import { ChakraProvider } from "@chakra-ui/react";
import theme from "../src/themes/storybookTheme";

export const parameters = {
  actions: { argTypesRegex: "^on[A-Z].*" },
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/,
    },
  },
};

addDecorator((storyFn) => (
  <ChakraProvider theme={theme}>{storyFn()}</ChakraProvider>
));
