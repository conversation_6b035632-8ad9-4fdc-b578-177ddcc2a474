import { captureException } from "@sentry/nextjs";
import {
  getStoryblokApi,
  StoryblokComponent,
  StoryData,
  useStoryblokState,
} from "@storyblok/react";
import { GetServerSideProps, NextPage } from "next";
import axios from "axios";
import { parseCookies } from "nookies";

import NewHomepage from "scenes/NewHomepage";
import StoryblokTypeUserProvider from "contexts/StoryblokTypeUser";
import { FindData } from "@ud/api/newPhpApi/__generated__/newPhpApi.generated";
import { NEW_ENDPOINT_COOKIE_NAME } from "utils/api/consts";

const Index: NextPage<{
  story: StoryData | null;
  preview: boolean;
  listingsHouseSale: FindData["offers"];
  listingsFlatRent: FindData["offers"];
}> = ({
  story: initialStory,
  preview,
  listingsFlatRent,
  listingsHouseSale,
}) => {
  const story = useStoryblokState(initialStory || undefined, {}, preview);
  const storyBlok = story ? (
    <StoryblokComponent blok={story.content} preview={preview} />
  ) : null;

  return (
    <StoryblokTypeUserProvider>
      <NewHomepage
        blogComponents={storyBlok}
        listingsFlatRent={listingsFlatRent}
        listingsHouseSale={listingsHouseSale}
      />
    </StoryblokTypeUserProvider>
  );
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const storyblokApi = getStoryblokApi();
  const cookies = parseCookies(context);
  const endpointUrl =
    cookies[NEW_ENDPOINT_COOKIE_NAME] ||
    process.env.NEXT_PUBLIC_NEW_PHP_API_URL;

  const apiParams = {
    version: context.preview ? "draft" : undefined,
    token: context.preview
      ? process.env.NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN
      : undefined,
    resolve_relations: ["root_card.root"],
  };

  let resStory = null;
  let listingsFlatRent: FindData["offers"] = [];
  let listingsHouseSale: FindData["offers"] = [];

  try {
    // Get Storyblok data
    const storyRes = await storyblokApi.get("cdn/stories/homepage", apiParams);
    resStory = storyRes?.data.story || null;
  } catch (err) {
    captureException(err);

    return {
      notFound: true,
      props: {
        story: null,
        preview: context.preview || false,
        listingsFlatRent: [],
        listingsHouseSale: [],
      },
    };
  }

  try {
    const [flatRentRes, houseSaleRes] = await Promise.all([
      axios({
        method: "get",
        url: `${endpointUrl}/v1/offer/latest`,
        data: {
          propertyType: "flat",
          offerType: "rent",
        },
      }),
      axios({
        method: "get",
        url: `${endpointUrl}/v1/offer/latest`,
        data: {
          propertyType: "house",
          offerType: "sale",
        },
      }),
    ]);

    listingsFlatRent = flatRentRes?.data?.offers || [];
    listingsHouseSale = houseSaleRes?.data?.offers || [];
  } catch (err) {
    captureException(err);
  }

  return {
    props: {
      story: resStory,
      preview: context.preview || false,
      listingsFlatRent,
      listingsHouseSale,
    },
  };
};

export default Index;
