import React from "react";
import { NextPage } from "next";
import { Box } from "@chakra-ui/react";

import Game from "scenes/Game";
import { isBrowser } from "helpers/global";
import useRouter from "hooks/useRouter";

const Index: NextPage = () => {
  const { navigateTo } = useRouter();
  if (isBrowser() && sessionStorage?.getItem("UDFANTOM") !== "active") {
    navigateTo("/");

    return <Box />;
  }

  return <Game />;
};

export default Index;
