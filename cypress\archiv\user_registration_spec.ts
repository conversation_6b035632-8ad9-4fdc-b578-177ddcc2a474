// @ts-nocheck
/**
 * TEST /nastaveni
 */

Cypress.on("uncaught:exception", (err) => {
  // ignor konkrétních errorů <PERSON>
  if (err.message.includes("__tcfapi is not defined")) {
    return false;
  }
  if (err.message.includes("Script error.")) {
    return false;
  }

  // return false; na konci bude ignorovat všechny uncaught exceptions errory
});

const getByDatatest = (dataTest) =>
  // eslint-disable-next-line
  cy.get('[data-test="' + dataTest + '"]');
const clickByDatatest = (dataTest) => getByDatatest(dataTest).click();
const typeByDatatest = (dataTest, value) => getByDatatest(dataTest).type(value);
const selectByDatatest = (dataTest, option) =>
  getByDatatest(dataTest).select(option);

const generateNewUser = () => {
  const password = "ulovdomov";
  const date = new Date();
  const email = `brendy_${date.getTime()}@ulovdomov.cz`;

  return { email, password };
};

describe("Tests user registration and profile actions 🔨", () => {
  const { email, password } = generateNewUser();
  let user = {};

  before(function () {
    cy.fixture("users.json").then(function (data) {
      user = data.mockUsers[0];
    });
  });

  it("Register human 🤖", () => {
    // Při testování produkce se musí ještě odkliknout cookie modál
    // cy.contains(
    //   "#CybotCookiebotDialogBodyLevelButtonLevelOptinAllowAll",
    // ).click();

    cy.visit("/");
    clickByDatatest("navbar.loginButton");
    typeByDatatest("loginModal.inputEmail", email);
    clickByDatatest("loginModal.buttonNext");
    cy.wait(300);
    typeByDatatest("loginModal.inputPassword", password);
    clickByDatatest("loginModal.buttonSignIn");

    // assert
    cy.window()
      .its("sessionStorage")
      .invoke("getItem", "ACCESS_TOKEN")
      .should("exist");
    cy.window()
      .its("localStorage")
      .invoke("getItem", "REFRESH_TOKEN")
      .should("exist");
    getByDatatest("navbar.userAvatar").should("exist");
  });

  it("Visit human page 🤖", () => {
    clickByDatatest("navbar.userAvatar");
    cy.wait(500);

    // assert
    cy.url().should("include", "/nastaveni");
    getByDatatest("profil.layout.userEmail").should("contain", email);
  });

  it("Change human information 🤖", () => {
    clickByDatatest("profil.button.editProfile");

    // assert
    cy.url().should("include", "/nastaveni/moje-vizitka");

    const {
      birth,
      animal,
      children,
      lastName,
      firstName,
      telephone,
      occupation,
      peopleCount,
      acmDuration,
      informations,
    } = user;
    typeByDatatest("profil.input.firstName", firstName);
    typeByDatatest("profil.input.lastName", lastName);
    typeByDatatest("profil.input.occupation", occupation);
    typeByDatatest("profil.input.birth", birth);
    selectByDatatest("profil.select.acmDuration", acmDuration);
    selectByDatatest("profil.select.animal", animal);
    selectByDatatest("profil.select.children", children);
    typeByDatatest("profil.input.telephone", telephone);
    selectByDatatest("profil.select.peopleCount", peopleCount);
    typeByDatatest("profil.textarea.informations", informations);
    clickByDatatest("profil.button.save");
    cy.contains("Rozumím").click();
    clickByDatatest("profil.layout.linkProfil");

    // assert
    cy.url().should("include", "/nastaveni");
    getByDatatest("profil.layout.userName").should(
      "contain",
      `${firstName} ${lastName}`,
    );

    // CYTODO: tady by se mělo reloadnout page a zkontrolovat změnu dat
  });

  it("Change landlord information 🤖", () => {
    clickByDatatest("profil.button.editLandlordProfile");

    // assert
    cy.url().should("include", "/nastaveni/profil-pronajimatele");

    const { firstName, lastName, occupation, telephone, landlordType } = user;
    typeByDatatest("profil.input.landlordFirstName", firstName);
    typeByDatatest("profil.input.landlordLastName", lastName);
    typeByDatatest("profil.input.landlordPhone", telephone);
    selectByDatatest("profil.select.landlordType", landlordType);
    typeByDatatest("profil.input.companyName", occupation);
    clickByDatatest("profil.button.save");
    cy.contains("Rozumím").click();
    clickByDatatest("profil.layout.linkProfil");

    // assert
    cy.url().should("include", "/nastaveni");
    getByDatatest("profil.layout.userName").should(
      "contain",
      `${firstName} ${lastName}`,
    );

    // CYTODO: tady by se mělo reloadnout page a zkontrolovat změnu dat
  });

  it("Log off human 🤖", () => {
    clickByDatatest("profil.layout.linkLogout");
    cy.wait(500);

    // assert
    cy.url().should("not.contain", "/nastaveni");
    cy.window()
      .its("sessionStorage")
      .invoke("getItem", "ACCESS_TOKEN")
      .should("not.exist");
    cy.window()
      .its("localStorage")
      .invoke("getItem", "REFRESH_TOKEN")
      .should("not.exist");
  });

  it("Destroy humanity 🤖", () => {
    expect(true).to.equal(true);
  });
});
