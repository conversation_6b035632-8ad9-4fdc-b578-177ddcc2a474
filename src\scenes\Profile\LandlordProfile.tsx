import * as React from "react";
import <PERSON><PERSON> from "joi";
import { Stack } from "@chakra-ui/react";
import { captureException } from "@sentry/nextjs";

import { useAlertModal } from "modals/useAlertModal";
import GetLandlordCardApiEndpoint from "utils/user/api/GetLandlordCard";
import { EApiLandlordType, ILandlord } from "utils/user";
import ChangeAvatarApiEndpoint from "utils/user/api/ChangeAvatar";
import { LANDLORD_TYPES } from "config/landlordTypes";
import ImageUpload from "components/ImageUpload";
import PhoneInput, { JoiPhoneValidator } from "components/Chakra/PhoneInput";
import {
  useLazyGetLogoQuery,
  useUploadLogoMutation,
  GetLogoApiResponse,
} from "@ud/api/nodeApi/__generated__/nodeApi.generated";
import ProfileLayout from "./Layout";
import Head from "./components/Head";
import AnimatedInput from "components/Chakra/AnimatedInput";
import Select from "components/Chakra/Selectbox";
import Button from "components/Chakra/Button";
import ResponsiveGrid from "components/Chakra/ResponsiveGrid";
import useForm, { OnFormSubmitType } from "hooks/useForm";
import useApiEndpoint from "hooks/useApiEndpoint";
import useGlobalState from "hooks/useGlobalState";
import useUserCRM from "hooks/useUserCRM";
import { usePhoneVerificationModal } from "modals/usePhoneVerificationModal";
import {
  useLazyGetV1VerifyPhoneCheckQuery,
  usePostV1LandlordUpdateMutation,
} from "@ud/api/newPhpApi/__generated__/newPhpApi.generated";

const FormSchema = Joi.object({
  firstName: Joi.string().allow(""),
  lastName: Joi.string().allow(""),
  landlordTypeId: Joi.number(),
  companyName: Joi.string().allow(""),
  file: Joi.any(),
  logo: Joi.any(),
  phone: JoiPhoneValidator(true),
});

interface IValues extends Omit<ILandlord, "photoPath" | "landlordTypeId"> {
  file: File | null;
  logo: File | null;
  landlordTypeId: EApiLandlordType | "1";
}

const LandlordProfile: React.FunctionComponent = () => {
  const formDataRef = React.useRef<IValues | null>(null);
  const { user } = useGlobalState();
  const [response, setResponse] = React.useState<ILandlord | null>(null);
  const [logoFromCRM, setLogoFromCRM] =
    React.useState<GetLogoApiResponse | null>(null);
  const { openModal: openAlertModal } = useAlertModal();
  const { openModal: openPhoneVerificationModal } = usePhoneVerificationModal();
  const [postLandlordUpdateApi] = usePostV1LandlordUpdateMutation();
  const [getPhoneCheck] = useLazyGetV1VerifyPhoneCheckQuery();
  const [uploadLogo] = useUploadLogoMutation();
  const [getLogo, getLogoResponse] = useLazyGetLogoQuery();
  const { getUserByEmail, createUser, updateUser } = useUserCRM();

  const { callEndpoint: changeAvatarApi } = useApiEndpoint(
    ChangeAvatarApiEndpoint,
  );
  const { callEndpoint: getLandlordCardApi } = useApiEndpoint(
    GetLandlordCardApiEndpoint,
  );

  const {
    registerWithError,
    handleSubmit,
    setValue,
    watch,
    formState,
    errors,
    clearErrors,
  } = useForm<IValues>(FormSchema, { keepDataOnSuccess: true });

  React.useEffect(() => {
    getLandlordCardApi().then(({ data }) => {
      setResponse(data);
      setValue("firstName", data?.firstName || "");
      setValue("lastName", data?.lastName || "");
      setValue(
        "phone",
        data?.phone
          ? { prefix: data.phone.prefix, number: data.phone.number }
          : { prefix: "+420", number: "" },
      );
      setValue("companyName", data?.companyName || "");
      setValue("landlordTypeId", data?.landlordTypeId || EApiLandlordType.NONE);
    });
  }, []);

  const landType = watch("landlordTypeId");
  const logoWatch = watch("logo");
  const firstNameWatch = watch("firstName");
  const lastNameWatch = watch("lastName");
  const companyNameWatch = watch("companyName");
  const phonePrefix = watch("phone.prefix");
  const phoneNumber = watch("phone.number");

  React.useEffect(() => {
    if (user) {
      getLogo({ userId: user.id });
    }
  }, [user]);

  React.useEffect(() => {
    if (getLogoResponse.data) {
      setLogoFromCRM(getLogoResponse.data);
    }
  }, [getLogoResponse]);

  const landlordTypes = React.useMemo(
    () => [
      ...LANDLORD_TYPES.map((x) => ({
        value: x.apiId,
        label: x.translationId,
      })),
    ],
    [],
  );

  const handleData: OnFormSubmitType<IValues> = async ({
    file,
    logo,
    ...rest
  }) => {
    if (phonePrefix && phoneNumber) {
      try {
        const { data } = await getPhoneCheck({
          number: phoneNumber,
          prefix: phonePrefix,
        }).unwrap();

        if (!data?.verified) {
          formDataRef.current = { ...rest, file, logo };

          openPhoneVerificationModal({
            number: phoneNumber,
            prefix: phonePrefix,
            onSuccess: () => {
              if (formDataRef.current) {
                handleData(formDataRef.current);
              }
            },
          });

          return;
        }
      } catch (error) {
        captureException(error);

        openAlertModal({
          animation: "error",
          title: "Něco se nepovedlo",
        });

        return;
      }
    }

    if (file) {
      const data = new FormData();
      data.append("0", file);
      const res = await changeAvatarApi(data);
      if (res.data) {
        // url = res.data.photoUrl;
      }
    }
    if (logo && user) {
      const userFromCRM = await getUserByEmail(user.email);
      if (userFromCRM) {
        await updateUser(userFromCRM.id, {
          userId: user.id,
        });
      } else {
        await createUser({
          email: user.email,
          userId: user.id,
          consents: [],
          offers: [],
        });
      }

      const data = new FormData();
      data.append("file", logo);

      await uploadLogo({
        userId: user.id,
        body: data as unknown as { file: Blob },
      });
    }

    await postLandlordUpdateApi({
      updateLandlordRequest: {
        firstName: rest.firstName,
        lastName: rest.lastName,
        contactPhone: { prefix: phonePrefix, number: phoneNumber },
        // photoPath: url,
        userLandlordTypeId: rest.landlordTypeId as EApiLandlordType,
        companyName:
          rest.landlordTypeId === EApiLandlordType.REAL_ESTATE_BROKER
            ? rest.companyName
            : "",
      },
    });

    openAlertModal({
      animation: "check",
      title: "Změny byly uloženy",
      submitText: "Děkuji",
    });
  };

  return (
    <ProfileLayout
      breadcrumbsProps={{
        list: [
          { to: "/nastaveni", title: "Profil" },
          { title: "Profil pronajímatele" },
        ],
      }}
    >
      {response && (
        <form onSubmit={handleSubmit(handleData)}>
          <Head
            title="Profil pronajímatele"
            subtitle="Udržujte své údaje aktuální, ať vás zájemci můžou lépe oslovit."
          />
          <ResponsiveGrid
            gridTemplateColumns="repeat(2, 1fr)"
            gridGap="10px"
            mobile={{ gridTemplateColumns: "1fr", gridRowGap: "20px" }}
          >
            <AnimatedInput
              {...registerWithError("firstName")}
              label="Jméno"
              value={firstNameWatch}
              dataTest="profil.input.landlordFirstName"
            />
            <AnimatedInput
              {...registerWithError("lastName")}
              label="Příjmení"
              value={lastNameWatch}
              dataTest="profil.input.landlordLastName"
            />
          </ResponsiveGrid>
          <ResponsiveGrid
            gridTemplateColumns="repeat(2, 1fr)"
            gridGap="10px"
            mobile={{ gridTemplateColumns: "1fr", gridRowGap: "20px" }}
          >
            <Stack mt="21px" spacing="0px">
              <PhoneInput
                dataTest="profil.input.landlordPhone"
                error={errors.phone}
                setValue={setValue}
                background="inherit"
                clearErrors={clearErrors}
                selectBoxValue={phonePrefix}
                phoneNumberValue={phoneNumber}
              />
            </Stack>
            <Select
              {...registerWithError("landlordTypeId")}
              label="Pronajímám jako"
              onChange={(e) => {
                const type = e.currentTarget.value;
                setValue("landlordTypeId", parseInt(type));
              }}
              dataTest="profil.select.landlordType"
              background="ud-white"
            >
              {landlordTypes.map((d) => (
                <option key={d.value} value={d.value}>
                  {d.label}
                </option>
              ))}
            </Select>
          </ResponsiveGrid>
          {(landType === EApiLandlordType.REAL_ESTATE_BROKER ||
            landType === "1") && (
            <AnimatedInput
              {...registerWithError("companyName")}
              label="Jméno realitní kanceláře"
              value={companyNameWatch}
              dataTest="profil.input.companyName"
            />
          )}
          <Stack
            direction={{ base: "column", md: "row" }}
            w={[
              "100%",
              landType &&
              parseInt(landType.toString()) ===
                EApiLandlordType.REAL_ESTATE_BROKER
                ? "100%"
                : "50%",
            ]}
            spacing={{ base: 4, md: 2 }}
          >
            <ImageUpload
              label="Profilová fotka"
              uploadLabel="Nahrát fotku"
              photoPath={response.photoPath}
              photoAlt={`${response.firstName} ${response.lastName}`}
              isPhotoRounded
              name="file"
              onPhotoChange={(file) => setValue("file", file)}
            />
            {landType &&
              parseInt(landType.toString()) ===
                EApiLandlordType.REAL_ESTATE_BROKER && (
                <ImageUpload
                  label="Logo realitní kanceláře"
                  description="Doporučené rozlišení 300x50 px"
                  uploadLabel="Nahrát logo"
                  photoPath={
                    logoFromCRM
                      ? `data:${logoFromCRM?.mimetype};base64,${Buffer.from(
                          // @ts-ignore
                          logoFromCRM?.data,
                        ).toString("base64")}`
                      : "/img/scenes/Profile/empty_image.svg"
                  }
                  photoAlt={`${response.firstName} ${response.lastName}`}
                  name="logo"
                  isSmall={!Boolean(logoFromCRM) && !logoWatch}
                  onPhotoChange={(logo) => setValue("logo", logo)}
                />
              )}
          </Stack>
          <Button
            isLoading={formState.isSubmitting}
            dataTest="profil.button.save"
            type="submit"
            mt="20px"
          >
            Uložit
          </Button>
        </form>
      )}
    </ProfileLayout>
  );
};

export default LandlordProfile;
