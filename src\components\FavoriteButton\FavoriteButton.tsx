import {
  FC,
  useContext,
  MouseEvent,
  ReactNode,
  useEffect,
  useState,
} from "react";
import { Text, Center, ButtonProps, Box } from "@chakra-ui/react";

import { GlobalStateContext, GlobalActionsContext } from "contexts/GlobalState";
import { ESessionItem } from "helpers/sessions";
import CustomLink from "components/Chakra/CustomLink";
import { useAlertModal } from "modals/useAlertModal";
import { useLoginModal } from "modals/useLoginModal";
import IconIconBookmarkNew from "components/Chakra/Icons/IconBookmarkNew";
import IconIconBookmarkSelectedNew from "components/Chakra/Icons/IconBookmarkSelectedNew";
import Button from "components/Chakra/Button";
import { EventPayload } from "src/types/types";
import useRouter from "hooks/useRouter";
import { getPageType, sendFavoriteConversion } from "helpers/conversions";
import { isSearchPath } from "helpers/search";

interface IProps extends Omit<ButtonProps, "children">, EventPayload {
  offerId: number;
  onRemove?: () => void;
  onAdd?: () => void;
  withText?: boolean;
  children?: ReactNode | ((isInFavorite: boolean) => ReactNode);
}

const FavoriteButton: FC<IProps> = ({
  offerId,
  onRemove,
  onAdd,
  withText,
  loginEvent,
  children,
  ...props
}) => {
  const { user } = useContext(GlobalStateContext);
  const { addOfferToFavorites, removeOfferFromFavorites } =
    useContext(GlobalActionsContext);
  const { asPath } = useRouter();

  const { openModal: openAlertModal } = useAlertModal();
  const { openModal: openLoginModal } = useLoginModal();

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const isInFavorite =
    (isClient && user?.favoriteOffersIds.includes(offerId)) ?? false;

  const getECat = () => {
    if (isSearchPath(asPath)) {
      return "search";
    }
    if (asPath.includes("/inzerat/")) {
      return "offer_detail";
    }
    if (asPath.includes("/nastaveni/")) {
      return "profile";
    }

    return "";
  };

  const triggerAddToFavorites = (): void => {
    onAdd?.();
    addOfferToFavorites(offerId);

    sendFavoriteConversion(getPageType(asPath));

    openAlertModal({
      trackEventPayload: {
        "dl.eCat": getECat(),
        "dl.eAct": "dialog.shown",
        "dl.eLab": "Uložen inzerát",
      },
      animation: "check",
      title: "Přidáno do oblíbených",
      children: (
        <Text>
          Své oblíbené inzeráty najdete v záložce{" "}
          <CustomLink to="/moje-schranka/[[...messages]]" showUnderline>
            Moje schránka
          </CustomLink>
          , abyste je rovnou mohli kontaktovat.
        </Text>
      ),
      submitText: "Děkuji",
    });
  };

  const triggerRemoveFromFavorites = (): void => {
    onRemove?.();
    removeOfferFromFavorites(offerId);

    openAlertModal({
      animation: "check",
      title: "Odebráno z oblíbených",
      children: "Určitě si najdete i jiného šampióna.",
      submitText: "Děkuji",
    });
  };

  const onClickFavouriteButton = (
    e: MouseEvent<HTMLButtonElement, globalThis.MouseEvent>,
  ): void => {
    e.stopPropagation();
    e.preventDefault();

    if (isInFavorite) {
      triggerRemoveFromFavorites();
    } else if (user) {
      triggerAddToFavorites();
    } else {
      sessionStorage.setItem(
        ESessionItem.IS_REGISTER_FROM_FAVORITE.toString(),
        "true",
      );

      openLoginModal({
        onSuccessfulLogin: triggerAddToFavorites,
        loginEvent,
      });
    }
  };

  const renderContent = () => {
    if (!isClient) {
      return null;
    }

    if (typeof children === "function") {
      return children(isInFavorite);
    }

    if (children) {
      return children;
    }

    return (
      <Center>
        {isInFavorite ? (
          <IconIconBookmarkSelectedNew
            fill="ud-primary"
            h="20px"
            w="auto"
            alignItems="center"
          />
        ) : (
          <IconIconBookmarkNew h="20px" w="auto" />
        )}
        <Text
          display={withText ? "block" : ["none", null, "block"]}
          ml="8px"
          fontWeight="bold"
          color="ud-primary"
        >
          {isInFavorite ? "Odebrat" : "Uložit"}
        </Text>
      </Center>
    );
  };

  return (
    <>
      {typeof children === "function" || children ? (
        <Box
          cursor="pointer"
          aria-label="save"
          onClick={onClickFavouriteButton}
          {...props}
        >
          {renderContent()}
        </Box>
      ) : (
        <Button
          variant="clean"
          cursor="pointer"
          aria-label="save"
          onClick={onClickFavouriteButton}
          {...props}
        >
          {renderContent()}
        </Button>
      )}
    </>
  );
};

export default FavoriteButton;

