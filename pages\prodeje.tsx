import { captureException } from "@sentry/nextjs";
import {
  getStoryblokApi,
  StoryblokComponent,
  StoryData,
  useStoryblokState,
} from "@storyblok/react";
import { GetServerSideProps, NextPage } from "next";

const Page: NextPage<{ story: StoryData; preview: boolean }> = ({
  story: initialStory,
  preview,
}) => {
  const story = useStoryblokState(initialStory, {}, preview);

  return <StoryblokComponent blok={story.content} preview={preview} />;
};
export default Page;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const storyblokApi = getStoryblokApi();
  const apiParams = {
    version: context.preview ? "draft" : undefined,
    token: context.preview
      ? process.env.NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN
      : undefined,
    resolve_relations: ["root_card.root", "intro_header.root"],
  };
  const res = await storyblokApi
    .get("cdn/stories/prodeje", apiParams)
    .catch((error) => {
      captureException(error);
    });
  if (res && "data" in res) {
    return {
      props: {
        story: res.data.story,
        key: res.data.story.id,
        preview: context.preview || null,
      },
    };
  }

  return {
    notFound: true,
  };
};
