import { NextPage, GetStaticProps, GetStaticPaths } from "next";
import { Stack, Text } from "@chakra-ui/react";
import {
  StoryData,
  getStoryblokApi,
  useStoryblokState,
  StoryblokComponent,
} from "@storyblok/react";
import { captureException } from "@sentry/nextjs";

import useRouter from "hooks/useRouter";

const ArticlePage: NextPage<{ story: StoryData; preview: boolean }> = ({
  story,
  preview,
}) => {
  const { isFallback } = useRouter();

  story = useStoryblokState(story, {}, preview);

  if (isFallback) {
    return (
      <Stack position="fixed" w="100%" h="100%" justify="center" align="center">
        <Text color="ud-primary">Načítání č<PERSON>ánku</Text>
      </Stack>
    );
  }

  return <StoryblokComponent blok={story.content} preview={preview} />;
};
export default ArticlePage;

const USE_STATIC =
  process.env.NODE_ENV === "production" &&
  process.env.ENABLE_STATIC_DATA === "True";

export const getStaticProps: GetStaticProps = async (context) => {
  const storyblokApi = getStoryblokApi();
  const apiParams = {
    version: context.preview ? "draft" : null,
    token: context.preview
      ? process.env.NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN
      : null,
    resolve_relations: [
      "article_page.root",
      "article_page.author",
      "article_page.category",
      "article_banner.article",
    ],
  };

  try {
    const res = await storyblokApi
      .get(`cdn/stories/radce/clanky/${context.params?.slug}`, apiParams)
      .catch((error) => {
        captureException(error);
      });

    if (res && "data" in res) {
      return {
        props: {
          story: res.data.story,
          key: res.data.story.id,
          preview: context.preview || false,
        },
        revalidate: 1,
      };
    }

    return {
      redirect: {
        permanent: false,
        destination: "/radce",
      },
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: "/radce",
      },
      props: {},
    };
  }
};

export const getStaticPaths: GetStaticPaths = async () => {
  const paths: Array<{ params: { slug: string } }> = [];

  if (USE_STATIC) {
    const storyblokApi = getStoryblokApi();
    const res = await storyblokApi.get("cdn/links/").catch((error) => {
      captureException(error);
    });
    if (res && "data" in res) {
      Object.keys(res.data.links).forEach((linkKey) => {
        if (
          res.data.links[linkKey].is_folder ||
          !res.data.links[linkKey].slug.includes("radce/clanky/")
        ) {
          return;
        }
        const splittedSlug = res.data.links[linkKey].slug.split("/");
        paths.push({ params: { slug: splittedSlug[splittedSlug.length - 1] } });
      });
    }
  }

  return {
    paths,
    fallback: true,
  };
};
