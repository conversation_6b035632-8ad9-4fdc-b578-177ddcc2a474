name: Cypress Tests

on:
  workflow_dispatch:
    inputs:
      base_url:
        required: true
        type: string
        description: "Write Environment URL to run tests against with https://"

  workflow_call:
    inputs:
      base_url:
        required: true
        type: string

jobs:
  Main-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    strategy:
      fail-fast: false
    steps:
      - name: Adding markdown
        run: echo '#### Tests running on ${{ inputs.base_url }}' >> $GITHUB_STEP_SUMMARY
      - name: Checkout
        uses: actions/checkout@v3

      - uses: browser-actions/setup-chrome@latest
        with:
          chrome-version: stable
      - run: |
          echo "BROWSER_PATH=$(which chrome)" >> $GITHUB_ENV
      - name: Run e2e tests
        uses: cypress-io/github-action@v5
        # continue-on-error: true
        id: sc_tests
        env:
          CI_BUILD_ID: ${{ github.run_id }}-${{ github.run_attempt }}
        with:
          config: |
            baseUrl=${{ inputs.base_url }}
            video=false
            spec: cypress/e2e/**/*

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-screenshots
          path: tests/cypress/screenshots/**
          if-no-files-found: ignore

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-videos
          path: tests/cypress/videos/**
          if-no-files-found: ignore
