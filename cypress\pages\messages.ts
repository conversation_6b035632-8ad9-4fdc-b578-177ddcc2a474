const el = {
  messageBox: "specificMessageBox",
  messageInput: "inputMessage",
  submitButton: "submitMessageButton",
};

export function checkSpecificMessage(message: string) {
  cy.getByTestId(el.messageBox).contains(message).should("be.visible");
}

export function checkNameOfBuyer(name?: string) {
  cy.getByTestId("renterName")
    .contains(name === undefined ? "Jméno neuvedeno" : name)
    .should("be.visible");
}

export function typeAndSendMessage(message: string) {
  cy.getByTestId(el.messageInput).clear().type(message);
  cy.intercept("POST", "**/add").as("sendMessage");
  cy.getByTestId(el.submitButton).click();
  cy.wait("@sendMessage")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.equal(200);
    });
}
