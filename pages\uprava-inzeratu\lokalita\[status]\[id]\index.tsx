import { GetServerSideProps } from "next/types";

import InsertOfferWrapper from "components/InsertOfferWrapper";
import LocationForm from "scenes/InsertOffer/form/Location";
import { CustomAppPage, EditOfferUnpaid } from "src/types/types";

const Location: CustomAppPage<EditOfferUnpaid> = (props) => (
  <InsertOfferWrapper title="Adresa nemovitosti" {...props}>
    <LocationForm buttonNextText="Další" />
  </InsertOfferWrapper>
);

Location.hasHiddenFooter = true;

export default Location;

export const getServerSideProps: GetServerSideProps = async ({ query }) => ({
  props: {
    id: query.id as string,
    status: query.status as string,
  },
});
