import { json2array } from "../helper/helper";

const el = {
  main: {
    map: "mapOfLeases",
    leftPanel: "searchScroller",
    content: "searchContent",
    dogButton: "dogSetupButton",
    editButton: "editSearchButton",
    preview: "previewOfferLeases",
    galeryPreview: "previewGallerySmall",
    titlePreview: "headingOfLeasesPreview",
    saveButton: "saveOfferFromPreview",
    actionButtons: "actionButtonsOnPreview",
    contactButton: "contactButton",
    showMore: "showMoreButton",
    topSection: "searchTopSection",
    count: "countOfOffers",
  },
  drawer: {
    leftPanel: "leftDrawer",
    dogButton: "watchDogSubmit",
    size: "sizeOfTheHouse",
    price: "priceSection",
  },
  dogPromo: "watchDogPromoButtonVariable",
};

export function generalElementsCheckMobile() {
  const mobile = [
    el.main.content,
    el.main.leftPanel,
    el.main.editButton,
    el.main.dogButton,
    el.main.preview,
  ];
  mobile.forEach((element: any) => {
    cy.getByTestId(element).should("be.visible");
  });
}

export function watchDogPromoVarOpen() {
  cy.getByTestId(el.dogPromo).first().click();
}

export function makeSpecificSearchOpenDogModal(type: string) {
  cy.wait("@offerCount0")
    .its("response")
    .then((resp) => {
      if (resp?.body.isPossibleCreateWatchdog === true) {
        type === "search" ? createWatchDog() : undefined;
        type === "promo" ? watchDogPromoVarOpen() : undefined;
      } else {
        type === "promo" ? watchDogPromoVarOpen() : undefined;
        type === "search"
          ? cy.getByTestId(el.main.editButton).click()
          : undefined;

        cy.getByTestId(el.drawer.leftPanel).within(() => {
          cy.intercept("POST", "**/offerCount").as("offerCount1");
          cy.getByTestId("1+kk").click();
          cy.wait("@offerCount1")
            .its("response")
            .then((resp0) => {
              if (resp0?.body.isPossibleCreateWatchdog === true) {
                cy.getByTestId(el.drawer.dogButton).click();
              } else {
                cy.intercept("POST", "**/offerCount").as("offerCount2");
                cy.getByTestId(el.drawer.size)
                  .find("input")
                  .eq(1)
                  .type("50" + "{enter}");
                cy.wait("@offerCount2")
                  .its("response")
                  .then((resp1) => {
                    if (resp1?.body.isPossibleCreateWatchdog === true) {
                      cy.getByTestId(el.drawer.dogButton).click();
                    } else {
                      cy.intercept("POST", "**/offerCount").as("offerCount3");
                      cy.getByTestId(el.drawer.price)
                        .find("input")
                        .eq(1)
                        .type("5000" + "{enter}");
                      cy.wait("@offerCount3")
                        .its("response")
                        .then((resp2) => {
                          if (resp2?.body.isPossibleCreateWatchdog === true) {
                            cy.getByTestId(el.drawer.dogButton).click();
                          }
                        });
                    }
                  });
              }
            });
        });
      }
    });
}

export function generalElementsCheck() {
  json2array(el.main).forEach((element: any) => {
    cy.getByTestId(element).first().scrollIntoView().should("be.visible");
  });
}

export function createWatchDog() {
  cy.getByTestId(el.main.topSection)
    .should("be.visible")
    .within(() => {
      cy.getByTestId(el.main.dogButton).click();
    });
}
