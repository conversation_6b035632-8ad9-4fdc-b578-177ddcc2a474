import { GetServerSideProps } from "next/types";

import InsertOfferWrapper from "components/InsertOfferWrapper";
import ContactForm from "scenes/InsertOffer/form/Contact";
import { CustomAppPage, EditOfferPaid } from "src/types/types";

const Contact: CustomAppPage<EditOfferPaid> = (props) => (
  <InsertOfferWrapper title="Kontakt u inzerátu" {...props}>
    <ContactForm buttonNextText="Další" />
  </InsertOfferWrapper>
);

Contact.hasHiddenFooter = true;

export default Contact;

export const getServerSideProps: GetServerSideProps = async ({ query }) => ({
  props: {
    id: query.id as string,
    paidId: query.paidId as string,
    status: query.status as string,
  },
});
