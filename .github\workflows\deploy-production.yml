name: K8S Deploy Production

on:
  release:
    types:
      - released

env:
  PROJECT: ud-fe
  DOCKER_REGISTRY: registry.digitalocean.com/ulovdomov-be

jobs:
  build:
    timeout-minutes: 30
    runs-on: [self-hosted, runner-heavy]
    name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> – sestavení
    steps:
      - uses: actions/checkout@v3

      - name: Get latest release
        id: last_release
        uses: oprypin/find-latest-tag@v1
        with:
          repository: ${{ github.repository }}
          releases-only: true

      - name: Build
        run: DOCKER_BUILDKIT=1 docker build . --file Dockerfile.k8s.prod --tag ${{ env.PROJECT }}:${{ steps.last_release.outputs.tag }}

      - name: Push image to registry
        run: |
          docker tag ${{ env.PROJECT }}:${{ steps.last_release.outputs.tag }} ${{ env.DOCKER_REGISTRY }}/${{ env.PROJECT }}:${{ steps.last_release.outputs.tag }}
          docker push ${{ env.DOCKER_REGISTRY }}/${{ env.PROJECT }}:${{ steps.last_release.outputs.tag }}

      - name: Slack Notify - error build
        if: failure()
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": ":exclamation: Při buildu <${{ github.event.repository.html_url }}/releases/tag/${{ steps.last_release.outputs.tag }}|${{ steps.last_release.outputs.tag }}> nastala chyba. Release nebude nasazen. :exclamation:"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Clean runner
        run: docker system prune -a -f

  deploy:
    runs-on: [self-hosted, k8s-stage-fast]
    name: Produkční prostředí – spuštění
    needs: build
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v3

      - name: Set up kubectl
        uses: matootie/dokube@v1.4.0
        with:
          personalAccessToken: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          clusterName: ulovdomov-prod
          namespace: ${{ env.PROJECT }}

      - uses: azure/setup-helm@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Get last release
        id: last_release
        uses: oprypin/find-latest-tag@v1
        with:
          repository: ${{ github.repository }}
          releases-only: true


      - name: Slack Notify - start deploy
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": ":rocket: deploy <${{ github.event.repository.html_url }}/releases/tag/${{ steps.last_release.outputs.tag }}|${{ steps.last_release.outputs.tag }}> byl zahájen :crossed_fingers:"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Install Helm Charts
        run: helm repo add ondrejsika https://helm.oxs.cz

      - name: Update Helm release
        run: |
          helm upgrade ${{ env.PROJECT }} -i \
          ondrejsika/one-image \
          -f .k8s/values.yml \
          --set image=${{ env.DOCKER_REGISTRY }}/${{ env.PROJECT }}:${{ steps.last_release.outputs.tag }} \
          -n ${{ env.PROJECT }} \
          --create-namespace

      - name: Wait to rollout
        run: sleep 180

      - name: Slack Notify - error deploy
        if: failure()
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": ":exclamation: Při deployi <${{ github.event.repository.html_url }}/releases/tag/${{ steps.last_release.outputs.tag }}|${{ steps.last_release.outputs.tag }}> nastala chyba. Je nutné zkontrolovat stav deploye. :exclamation: https://www.ulovdomov.cz :link:"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

  notify:
    name: Produkční prostředí – notifikace
    needs: deploy
    runs-on: ubuntu-latest
    timeout-minutes: 1
    steps:
      - uses: actions/checkout@v3

      - name: Slack Notify - finish
        run: ./ops/slack_notify.sh ${{ secrets.SLACK_WEBHOOK_URL }}
