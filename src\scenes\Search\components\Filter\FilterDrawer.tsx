import {
  <PERSON>,
  Drawer,
  <PERSON>er<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>lex,
  <PERSON><PERSON>,
  Spinner,
  Stack,
  Text,
  useDisclosure,
} from "@chakra-ui/react";
import { FC, useCallback, useEffect, useState } from "react";
import { isEmpty } from "ramda";

import { useTracking } from "contexts/Tracking";
import Button from "components/Chakra/Button";
import { IconCloseCircle, IconDelete, IconDog, IconDropdown } from "@ud/icons";
import { FilterErrorsProvider, FilterStateProvider } from "./context";
import Flat from "./Flat";
import House from "./House";
import { Location } from "./Sections/Location";
import { TFilterErrors, TFilterState } from "./types";
import { OfferType, PropertyType } from "./Sections/GeneralSections";
import { useNewSearchActions, useNewSearchState } from "contexts/Search";
import {
  floorCountRangeMax,
  floorCountRangeMin,
  floorRangeMax,
  floorRangeMin,
  propertyType,
} from "@ud/config/prodeje/config";
import Search from "components/Chakra/Icons/Search";
import { getInflection, getRequestBodyForFind } from "scenes/Search/utils";
import { TFilter } from "scenes/Search/types";
import {
  BoundsRequest,
  usePostV1OfferCountMutation,
} from "@ud/api/newPhpApi/__generated__/newPhpApi.generated";
import SearchLink from "components/Chakra/SearchLink";
import { createSearchUrl } from "scenes/Search/createSearchUrl";
import { WATCHDOG_OFFER_LIMIT } from "scenes/Search/consts";
import { useWatchdogModal } from "modals/useWatchdogModal";
import useApiEndpoint from "hooks/useApiEndpoint";
import Location2JsonApiEndpoint from "utils/geo/api/Location2Json";
import AllProperties from "./AllProperties";

// PAVEL doplni availabilityType a budem moct zmazat
type PureFilterType = Omit<
  TFilterState,
  "location" | "isDisabledSubmit" | "availabilityDate"
>;

const filterStatePropertyTable: Record<
  keyof typeof propertyType,
  Array<keyof PureFilterType>
> = {
  flat: [
    "disposition",
    "furnished",
    "convenience",
    "flatType",
    "floor",
    "floorCount",
  ],
  house: [
    "houseConvenience",
    "houseType",
    "buildingType",
    "objectType",
    "roomCount",
    "protectedArea",
  ],
};

const filterStateGeneralTable: Array<keyof PureFilterType> = [
  "offerType",
  "propertyType",
  "locationType",
  "floorArea",
  "price",
  "buildingCondition",
  "material",
  "energyEfficiencyRating",
  "ownership",
  "surroundingType",
  "offerAge",
  "availabilityType",
  "isNoCommission",
];

const buildDrawerStateFromFilter = ({
  filter,
  location,
}: {
  filter: TFilter;
  location?: string;
}): TFilterState => ({
  // general
  offerType: filter.offerType,
  propertyType: filter.propertyType,
  location: location ?? "",
  floorArea: filter.floorArea ?? {},
  price: filter.price ?? {},
  buildingCondition: filter.buildingCondition ?? [],
  material: filter.material ?? [],
  energyEfficiencyRating: filter.energyEfficiencyRating ?? [],
  ownership: filter.ownership ?? [],
  locationType: filter.locationType ?? [],
  surroundingType: filter.surroundingType ?? [],
  offerAge: filter.offerAge ?? "month",
  availabilityType: filter.availabilityType ?? "asap",
  // PAVEL dokonci
  availabilityDate: new Date(),

  // flat
  disposition: filter.disposition ?? [],
  furnished: filter.furnished ?? [],
  flatType: filter.flatType ?? [],
  convenience: filter.convenience ?? [],
  floor: {
    min: (filter.floor?.min?.toString() as keyof typeof floorRangeMin) ?? null,
    max: (filter.floor?.max?.toString() as keyof typeof floorRangeMax) ?? null,
  },
  floorCount: {
    min:
      (filter.floorCount?.min?.toString() as keyof typeof floorCountRangeMin) ??
      null,
    max:
      (filter.floorCount?.max?.toString() as keyof typeof floorCountRangeMax) ??
      null,
  },

  // house
  houseType: filter.houseType ?? [],
  houseConvenience: filter.houseConvenience ?? [],
  buildingType: filter.buildingType ?? [],
  objectType: filter.objectType ?? [],
  roomCount: filter.roomCount ?? [],
  protectedArea: filter.protectedArea ?? [],

  // cohabiting
  colivingType: filter.colivingType ?? [],

  // rent
  isNoCommission: filter.isNoCommission,
});

export const FilterDrawer: FC<{ isScrolling: boolean }> = ({ isScrolling }) => {
  const { trackEvent } = useTracking();

  const { isOpen, onOpen, onClose } = useDisclosure();
  const [formErrors, setFormErrors] = useState<TFilterErrors>([]);
  const { filter, location, filtersAppliedCount } = useNewSearchState();
  const [newBounds, setNewBounds] = useState<BoundsRequest | undefined>(
    filter.bounds ?? undefined,
  );
  const { setSearchFilter, setOpenDrawerCallback } = useNewSearchActions();

  const [isWdDisabled, setIsWdDisabled] = useState(false);
  const { callEndpoint: location2JsonApi } = useApiEndpoint(
    Location2JsonApiEndpoint,
  );

  const [offerCountApi, offerCount] = usePostV1OfferCountMutation();

  const { openModal: openWatchdogModal } = useWatchdogModal();

  const [state, setState] = useState<TFilterState>(
    buildDrawerStateFromFilter({ location, filter }),
  );

  const [isLocationChanged, setIsLocationChanged] = useState(false);

  const buildFilter = useCallback(
    (oldFilter: TFilter) => {
      const newFilter: TFilter = {
        offerType: state.offerType,
        propertyType: state.propertyType,
        pageIndex: 1,
        sorting: oldFilter.sorting,
      };

      filterStateGeneralTable.forEach((filterKey) => {
        if (!state[filterKey] || isEmpty(state[filterKey])) {
          delete newFilter[filterKey];

          return;
        }
        // @ts-ignore
        newFilter[filterKey] = state[filterKey];
      });

      if (!state.propertyType) {
        filterStatePropertyTable.flat.forEach((filterKey) => {
          delete newFilter[filterKey];
        });
        filterStatePropertyTable.house.forEach((filterKey) => {
          delete newFilter[filterKey];
        });
      } else {
        const selector =
          state.offerType === "coliving" ? "flat" : state.propertyType;

        filterStatePropertyTable[selector].forEach((filterKey) => {
          if (!state[filterKey] || isEmpty(state[filterKey])) {
            delete newFilter[filterKey];

            return;
          }

          // @ts-ignore
          newFilter[filterKey] = state[filterKey];
        });

        filterStatePropertyTable[
          selector === "flat" ? "house" : "flat"
        ].forEach((filterKey) => {
          delete newFilter[filterKey];
        });

        if (state.offerType === "coliving") {
          delete newFilter.propertyType;
          delete newFilter.disposition;
          newFilter.colivingType = state.colivingType;
        }
      }

      return {
        ...newFilter,
        bounds: state.location ? undefined : oldFilter.bounds,
      };
    },
    [state],
  );

  const handleWatchdog = () => {
    openWatchdogModal({
      filter: { ...buildFilter(filter), bounds: newBounds },
      location: location ?? "",
    });
  };

  const handleOnClick = () => {
    if (formErrors.length > 0) {
      return;
    }

    trackEvent({
      "dl.eCat": "search",
      "dl.eAct": "filters.clicked",
      "dl.eLab": "Zobrazit inzeráty",
    });
    setSearchFilter((old) => buildFilter(old), state.location);
    onClose();
  };

  const handleDelete = () => {
    setSearchFilter((old) => ({
      offerType: old.offerType,
      propertyType: old.propertyType,
      pageIndex: 1,
      sorting: old.sorting,
      bounds: old.bounds,
    }));
    trackEvent({
      "dl.eCat": "search",
      "dl.eAct": "link.clicked",
      "dl.eLab": "Resetovat filtry",
    });
    onClose();
  };

  useEffect(() => {
    const count = offerCount.data?.data.count;
    const condition =
      typeof count === "undefined" ||
      count > WATCHDOG_OFFER_LIMIT ||
      state.offerType === "sale";
    setIsWdDisabled(condition);
  }, [offerCount, state.offerType]);

  useEffect(() => {
    setOpenDrawerCallback(onOpen);
  }, [onOpen, setOpenDrawerCallback]);

  useEffect(() => {
    setState((old) => ({ ...old, location: location ?? "" }));
  }, [location]);

  useEffect(() => {
    isOpen &&
      trackEvent({
        "dl.eCat": "search",
        "dl.eAct": "dialog.shown",
        "dl.eLab": "Upravit hledání",
      });
  }, [isOpen, trackEvent]);

  useEffect(() => {
    setState(buildDrawerStateFromFilter({ filter, location }));
  }, [filter, location]);

  useEffect(() => {
    const newFilter = buildFilter(filter);

    if (isLocationChanged) {
      location2JsonApi({ location: state.location }).then(({ data }) => {
        if (data) {
          newFilter.bounds = data.bounds;
          setNewBounds(data.bounds);
          offerCountApi({ findRequest: getRequestBodyForFind(newFilter) });
        }
      });

      return;
    }

    newFilter.bounds = filter.bounds;
    offerCountApi({ findRequest: getRequestBodyForFind(newFilter) });
  }, [
    buildFilter,
    filter,
    isLocationChanged,
    location2JsonApi,
    offerCountApi,
    state,
  ]);

  const renderTemplate = () => {
    if (!state.propertyType) {
      return <AllProperties />;
    }

    if (state.offerType === "coliving" || state.propertyType === "flat") {
      return <Flat />;
    }

    return <House />;
  };

  const isSales = state.offerType === "sale";

  return (
    <FilterStateProvider value={{ state, setState }}>
      <FilterErrorsProvider
        value={{ errorsState: formErrors, setErrorsState: setFormErrors }}
      >
        <Button
          onClick={onOpen}
          data-test="editSearchButton"
          leftIcon={<Search fillWith="ud-gray" boxSize="18px" mt="2px" />}
          trackEventPayload={{
            "dl.eCat": "search",
            "dl.eAct": "filters.clicked",
            "dl.eLab": "Upravit hledání",
          }}
          w={[`${isScrolling ? "60%" : "93%"}`, "50%"]}
          variant="outline"
          borderColor="ud-gray"
          borderWidth="1px"
          color="ud-gray"
          _hover={{ backgroundColor: "ud-grey-100" }}
          bg="ud-white"
          boxShadow={["md", "unset"]}
          _focus={{ backgroundColor: "ud-primary-100" }}
        >
          <Text>Upravit hledání</Text>
          {filtersAppliedCount && filtersAppliedCount > 0 ? (
            <Box
              bg="ud-primary"
              borderRadius="full"
              color="white"
              h="20px"
              px="5px"
              ml="5px"
            >
              {filtersAppliedCount}
            </Box>
          ) : null}
        </Button>
        <Drawer isOpen={isOpen} placement="left" onClose={onClose} size="md">
          <DrawerOverlay />
          <DrawerContent>
            <DrawerHeader borderWidth="1px" borderBottomColor="ud-grey-300">
              <Flex justify="space-between" align="center">
                <Button variant="link" onClick={handleDelete}>
                  <IconDelete fillWith="ud-primary" />
                  <Text color="ud-primary" size="medium" pl="3px">
                    Smazat filtry
                  </Text>
                </Button>
                <Heading
                  textAlign="center"
                  as="h3"
                  variant="h3m"
                  color="ud-black"
                  mr="30px"
                >
                  Upravit hledání
                </Heading>
                <Button
                  variant="clean"
                  aria-label="Zavřít"
                  onClick={() => {
                    trackEvent({
                      "dl.eCat": "search",
                      "dl.eAct": "filters.clicked",
                      "dl.eLab": "Zavřít dialog",
                    });
                    onClose();
                  }}
                >
                  <IconCloseCircle boxSize="30px" />
                </Button>
              </Flex>
            </DrawerHeader>

            <DrawerBody px={["10px", "20px"]}>
              <OfferType />
              <PropertyType />
              <Location
                setIsLocChanged={(isChanged) => setIsLocationChanged(isChanged)}
              />
              {renderTemplate()}
            </DrawerBody>

            <DrawerFooter borderWidth="1px" borderTopColor="ud-grey-300">
              <Flex
                w="100%"
                direction={{ base: "column", sm: "row" }}
                justify="space-between"
                gap={{ base: "10px", sm: "unset" }}
              >
                <Button
                  leftIcon={<IconDog fillWith="ud-primary" />}
                  variant="outline"
                  textColor="ud-grey-600"
                  w={{ base: "100%", sm: "33%" }}
                  position="relative"
                  isDisabled={isWdDisabled}
                  onClick={handleWatchdog}
                >
                  Hlídací pes
                  {isWdDisabled && (
                    <Stack
                      position="absolute"
                      top="-42px"
                      right={{ base: "50%", sm: "-60px" }}
                      align="center"
                      zIndex="999"
                    >
                      <Box
                        p="4px 8px"
                        bg="ud-black"
                        borderRadius="6px"
                        color="ud-white"
                        position="relative"
                        fontWeight="light"
                        fontSize="sm"
                      >
                        <Box
                          position="absolute"
                          bottom="-18px"
                          left="50%"
                          transform="translate(-50%, -50%)"
                        >
                          <IconDropdown fill="ud-black" />
                        </Box>
                        {isSales ? "Jenom pronájmy" : "Zpřesněte hledání"}
                      </Box>
                      <Box
                        px="8px"
                        borderRadius="100%"
                        bg="ud-black"
                        color="ud-white"
                      >
                        ?
                      </Box>
                    </Stack>
                  )}
                </Button>
                <SearchLink
                  variant="button"
                  href={createSearchUrl({
                    filter: buildFilter(filter),
                    location: state.location,
                  })}
                  onClick={handleOnClick}
                  w={{ base: "100%", sm: "65%" }}
                  _disabled={
                    formErrors.length > 0
                      ? { opacity: 0.6, cursor: "not-allowed" }
                      : undefined
                  }
                >
                  {offerCount.isLoading ? (
                    <Spinner boxSize="20px" />
                  ) : (
                    `Zobrazit ${getInflection({
                      offerCount: offerCount.data?.data.count ?? 0,
                      type: state.offerType,
                    })}`
                  )}
                </SearchLink>
              </Flex>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      </FilterErrorsProvider>
    </FilterStateProvider>
  );
};
