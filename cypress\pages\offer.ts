import { faker } from "@faker-js/faker";

import { json2array } from "../helper/helper";
import { checkSignUpModalElements } from "./login";
import { waitForPageLoaded } from "./core";
import { urls } from "../constants/ulrs";
import * as data from "../constants/data";

interface FloorOption {
  [key: string]: string;
}

const isVisible = (elem: any) =>
  !!(elem.offsetWidth || elem.offsetHeight || elem.get(0).getClientRects());

const el = {
  addOfferButton: "navbar.content.addOffer",
  heading: "offer.heading",
  text: "offer.text",
  form: "offer.form",
  avatarEmail: "navbar.content.userAvatar.mail",
  regPasswordInput: "loginModal.signUp.form.input",
  regButton: "loginModal.signUp.form.button",
  globalMapBox: "global.mapBox",
  globalShowPass: "global.showPass",
  globalInputBoxText: "global.inputBox.text",
  globalWriteBox: "global.writeBox",
  globalWriteBoxText: "global.writeBox.text",
  globalSelectBox: "global.selectBox",
  globalFormBox: "global.form.input",
  globalDateBox: "global.dateBox.div",
  globalDateBoxText: "global.dateBox.text",
  scroll: {
    dispo: {
      dispoDiv: "offer.scroll.dispo",
      selectButton: "offer.scroll.dispo.dispositionId",
      option: {
        optionDisabled: "offer.scroll.dispo.dispositionDisabled",
        option1kk: "offer.scroll.dispo.disposition.1+kk",
        option1plus1: "offer.scroll.dispo.disposition.1+1",
        option2kk: "offer.scroll.dispo.disposition.2+kk",
        option2plus1: "offer.scroll.dispo.disposition.2+1",
        option3kk: "offer.scroll.dispo.disposition.3+kk",
        option3plus1: "offer.scroll.dispo.disposition.3+1",
        option4kk: "offer.scroll.dispo.disposition.4+kk",
        option4plus1: "offer.scroll.dispo.disposition.4+1",
        option5kk: "offer.scroll.dispo.disposition.5+kk",
        option5plus1: "offer.scroll.dispo.disposition.5+1",
        option6kk: "offer.scroll.dispo.disposition.6+kk",
        option6plus1: "offer.scroll.dispo.disposition.6+1",
        option7kk: "offer.scroll.dispo.disposition.7+kk",
        option7plus1: "offer.scroll.dispo.disposition.7+1",
        optionAtypical: "offer.scroll.dispo.disposition.atypical",
        optionHouse: "offer.scroll.dispo.disposition.house",
        optionColiving: "offer.scroll.dispo.disposition.coliving",
      },
    },
    area: {
      areaDiv: "offer.scroll.area",
      input: "offer.scroll.area.input",
    },
    floor: {
      floorNumberSelect: "offer.scroll.floorNumber",
      option: {
        disabled: "offer.scroll.floorNumberDisabled",
        n2: "offer.scroll.floorNumber.n2",
        n1: "offer.scroll.floorNumber.n1",
      } as FloorOption,
    },
  },
  address: {
    addressDiv: "offer.address",
    town: "offer.adrress.town",
    townText: "offer.address.town.townText",
    townSearch: "offer.address.town.searchInput",
    townSectionPopover: "offer.address.town.popover",
    street: "offer.address.street",
    streetText: "offer.address.street.streetText",
    streetSearch: "offer.address.street.searchInput",
    streetSectionPopver: "offer.address.street.popover",
  },
  rental: {
    rentalDiv: "offer.rental",
    price: "offer.rental.price",
    commission: "offer.rental.commission",
    fees: "offer.rental.fees",
    deposit: "offer.rental.deposit",
  },
  furnishing: {
    furnishingDiv: "offer.furnishing",
    furnishingSelect: "offer.furnishing.select",
    option: {
      optionDisabled: "offer.furnishing.selectDisabled",
      optionNone: "offer.furnishing.selectNone",
      optionPart: "offer.furnishing.selectPartially",
      optionFull: "offer.furnishing.selectFull",
    },
    labels: {
      label: "offer.furnishing.labels",
      washingMachine: "offer.furnishing.labels.washingMachine",
      dishWasher: "offer.furnishing.labels.dishWasher",
      fridge: "offer.furnishing.labels.fridge",
      cellar: "offer.furnishing.labels.cellar",
      balcony: "offer.furnishing.labels.balcony",
    },
  },
  availability: {
    availabilityDiv: "offer.availability",
    text: "offer.availability.text",
    immediately: "offer.availability.immediately",
    from: "offer.availability.from",
    dateDay: "offer.availability.dateDay",
    dateMonth: {
      monthSelect: "offer.availability.dateMonthSelect",
      january: "offer.availablility.dateMonthSelect.january",
      february: "offer.availablility.dateMonthSelect.february",
      march: "offer.availablility.dateMonthSelect.march",
      april: "offer.availablility.dateMonthSelect.april",
      may: "offer.availablility.dateMonthSelect.may",
      juny: "offer.availablility.dateMonthSelect.juny",
      july: "offer.availablility.dateMonthSelect.july",
      august: "offer.availablility.dateMonthSelect.august",
      september: "offer.availablility.dateMonthSelect.september",
      october: "offer.availablility.dateMonthSelect.october",
      november: "offer.availablility.dateMonthSelect.november",
      december: "offer.availablility.dateMonthSelect.december",
    },
    dateYear: "offer.availability.dateYear",
    warningText: "offer.availability.warningText",
    energyDemand: {
      energySelect: "offer.availability.energy",
      option: {
        disabled: "offer.availability.energyDisabled",
        dontKnow: "offer.availability.energyDontKnow",
        a: "offer.availability.energyA",
        b: "offer.availability.energyB",
        c: "offer.availability.energyC",
        d: "offer.availability.energyD",
        e: "offer.availability.energyE",
        f: "offer.availability.energyF",
        g: "offer.availability.energyG",
        unspecified: "offer.availability.energyUnspecified",
      },
    },
    submit: "offer.submit",
    submitButton: "offer.submitButton",
    protectionText: "offer.protectionText",
    protectionLink: "offer.protectionLink",
    idealninajemceOffer: "idealninajemce.offer",
    ulovdomovOffer: "ulovdomov.offer",
    avatarEmail: "navbar.content.userAvatar.mail",
  },

  methodOfPublication: {
    start: "methodOfPublication.start",
    startButton: "methodOfPublication.start.button",
    vip: "methodOfPublication.vip",
    vipButton: "methodOfPublication.vip.button",
    standard: "methodOfPublication.standard",
    standardButton: "methodOfPublication.standard.button",
  },
  paymentVip: {
    paymentHeading: "payment.heading",
    paymentItems: "payment.items",
    paymentPromoCodeButton: "payment.discountCode.button",
    submitPaymentMethod: "submitPaymetMethod",
  },
  paymentCode: {
    paymentDiscountCode: "paymentMethod.discountCode",
    paymentDiscountCodeHeading: "paymentMethod.discountCode.heading",
    paymentDiscountCodeInput: "paymentMethod.discountCode.input",
    paymentDiscountCodeSubmit: "paymentMethod.discountCode.submit",
  },
  alertModal: {
    box: "alertModal",
    heading: "alertModal.heading",
    text: "alertModal.text",
    button: "alertModal.button",
    closeButton: "alertModal.closeButton",
    cancelButton: "alertModal.cancelButton",
  },
  description: "offer.description",
  globalTextArea: "global.textarea",
  globalPhotos: "offer.photos",
  globalPhotosStack: "global.importPhoto.stack",
  globalPhotosText: "global.importPhoto.text",
  contact: "offer.contact",
  contactText: "offer.contact.text",
  account: "offer.account",
  accountLogin: "offer.account.login",
  accountLoginButton: "offer.account.loginButton",
  name: {
    nameDiv: "offer.name",
    firstName: "offer.name.firstName",
    lastName: "offer.name.lastName",
  },
  userType: "offer.userType",
  userTypePrivate: "offer.userType.privateLanlord",
  userTypeEstate: "offer.userType.realEstate",
  userTypeName: "offer.userType.companyName",
  contactLabel: {
    label: "offer.contactLabel",
    labelEmail: "offer.contactLabel.email",
    labelPhoneForm: "global.phoneInput",
    labelPhone: "global.phoneInput.phoneSuggest",
    labelPhonePrefix: "global.phoneInput.suggestPrefix",
    labelPhoneSection: "global.phoneInput.suggestPrefixSection", // milion cisel -> vyhladavat podla textu
    labelPhoneNumber: "global.phoneInput.phoneNumber",
    labelPhoneNumberForm: "global.form.input",
    labelPhoneNumberInput: "global.phoneInput.phoneNumberInput",
  },
  submit: "offer.submit",
  submitButton: "offer.submitButton",
  protectionText: "offer.protectionText",
  protectionLink: "offer.protectionLink",
  watchDogButton: "offerDetail.watchdogBanner.createDoggo",
  watchDogBaner: "offerDetail.watchdogBanner",
  watchDogtext: "offerDetail.watchdogBanner.text",
  offerRightPanel: "offerDetail.rightPanel",
  dogPromo: "watchDogPromo",
  dogPromoButton: "watchDogPromoButton",
  offerSingle: "watchDogSingleResult",
  actionButtons: "actionButtonsOnPreview",
  stopOrPublishButton: "global.stopOrPublishButton",
  offers: {
    box: "myOffers",
    heading: "myOffers.heading",
    text: "myOffers.text",
    addButton: "myOffers.addButton",
    list: "myOffers.offers",
    showOfferButton: "myOffers.showOfferButton",
  },
  deleteOffer: "offerDetail.inactive.trash",
  editOffersButton: "myOffers.editButton",
  offerPrice: "offerDetail.price",
  offerLocation: "offerDetail.perex.location",
  offerTitle: "offerDetail.perex.title",
  offerOwnerName: "offerDetail.contact.owner.name",
  offerOwnerType: "offerDetail.contact.owner.type",
  offerDetailFees: "offerDetail.fees",
  offerDetailCaution: "offerDetail.caution",
  offerDetailDescription: "offerDetail.description",
  offerKeyValueInfo: "offerDetail.keyValueInfo",
  offerDetailEditButton: "offerDetail.editButton",
  editForm: {
    heading: "editForm.heading",
    listingText: "editForm.editListingText",
    listingButton: "editForm.editListingButton",
    dispositionSelect: "editForm.dispositionSelect",
    areaInput: "editForm.areaInput",
    floorNumberSelect: "editForm.floorNumberSelect",
    townSearchInput: "offer.address.town.searchInput",
    streetSearchInput: "offer.address.street.searchInput",
    rentalPrice: "editForm.rentalPrice",
    hasCommission: "editForm.hasCommission",
    monthlyFeesPrice: "editForm.monthlyFeesPrice",
    depositPrice: "editForm.depositPrice",
    furnishing: "editForm.furnishing",
    hasWashingMachine: "editForm.hasWaschingMachine",
    hasDishwasher: "editForm.hasDishwasher",
    hasFridge: "editForm.hasFridge",
    hasCellar: "editForm.hasCellar",
    hasBalcony: "editForm.hasBalcony",
    immediatelly: "editForm.immediately",
    date: "editForm.date",
    dateDay: "offer.availability.dateDay",
    dateMonth: "offer.availability.dateMonthSelect",
    dateYear: "offer.availability.dateYear",
    energySelect: "editForm.energy",
    textarea: "global.textarea",
    uploadPhotos: "global.importPhoto.stack",
    submitButton: "editForm.submitButon",
  },
  pass: "loginModal.signIn.form.passwordInput",
  inActiveOffer: {
    div: "offerDetail.inactive",
    headind: "offerDetail.inactive.heading",
    edit: "offerDetail.inactive.edit",
    stats: "offerDetail.inactive.stats",
    trash: "offerDetail.inactive.trash",
    messageIcon: "offerDetail.inactive.messages.icon",
    activateButton: "offerDetail.inactive.activate",
    inactiveButton: "offerDetail.inactiveButton",
  },
  inactiveDog: "offerDetail.inactive.dog",
  inactiveText: "offerDetail.inactive.text",
  noLivingHeading: "noLiving.heading",
  noLivingText: "noLiving.text",
};

const possibleFloorNumbers = [];
const floorNumberTexts = [];

// pridanie 33 podlazi a pridruzeny text
for (let i = -2; i <= 30; i++) {
  let value;
  if (i === -2) {
    value = "n2";
  } else if (i === -1) {
    value = "n1";
  } else if (i === 0) {
    value = "p0";
  } else {
    value = `p${i}`;
  }

  let text;
  if (i === -2) {
    text = "2. podzemní podlaží";
  } else if (i === -1) {
    text = "1. podzemní podlaží";
  } else if (i === 0) {
    text = "Přízemí";
  } else {
    text = `${i}. patro`;
  }

  possibleFloorNumbers.push(value);
  floorNumberTexts.push(text);
}

const randomDispoIndex = Math.floor(
  Math.random() * data.possibleDispoValues.length,
);
const randomFloorNumberIndex = Math.floor(
  Math.random() * possibleFloorNumbers.length,
);
const randomFurnishingIndex = Math.floor(
  Math.random() * data.possibleFurnishingValues.length,
);
const randomEnergyIndex = Math.floor(
  Math.random() * data.possibleEnergyValues.length,
);
const randomMonthIndex = Math.floor(
  Math.random() * data.possibleMonthValues.length,
);

const randomSelectData = {
  dispo: {
    value: data.possibleDispoValues[randomDispoIndex], // dispozice
    text: data.dispoTexts[randomDispoIndex],
  },
  floorNumber: {
    value: possibleFloorNumbers[randomFloorNumberIndex], // poschodie
    text: floorNumberTexts[randomFloorNumberIndex],
  },
  furnishing: {
    value: data.possibleFurnishingValues[randomFurnishingIndex], // vybavenost
    text: data.furnishingTexts[randomFurnishingIndex],
  },
  energy: {
    value: data.possibleEnergyValues[randomEnergyIndex], // energeticka narocnost
    text: data.energyTexts[randomEnergyIndex],
  },
  dateMonth: {
    value: data.possibleMonthValues[randomMonthIndex],
  },
};

// pridani 30 podlazi do options
for (let i = 0; i <= 30; i++) {
  el.scroll.floor.option[`p${i}`] = `offer.scroll.floorNumber.p${i}`;
}

let dispoText = randomSelectData.dispo.text;
if (dispoText === "Dům") {
  dispoText = "domu";
} else if (dispoText === "Spolubydlení") {
  dispoText = "spolubydlení";
}

const offerAddressUrl = "/inzerat/pronajem-praha-zizkov-praha-3-italska/";
let myOfferId = "";

function deactivatedElements() {
  json2array(el.inActiveOffer).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

function deactivatedElementsAsAnonym() {
  const elements = [
    el.inActiveOffer.div,
    el.inActiveOffer.headind,
    el.inactiveText,
    el.inactiveDog,
  ];
  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

export function createWatchDogFromOffer() {
  cy.getByTestId(el.offerRightPanel).within(() => {
    cy.getByTestId(el.watchDogBaner).should("be.visible");
    cy.getByTestId(el.watchDogBaner).should("be.visible");
    cy.getByTestId(el.watchDogButton).click();
  });
}

export function offerFormDispoElements() {
  json2array(el.scroll.dispo.option).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function offerFormAreaElements() {
  json2array(el.scroll.area).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function offerFormFloorElements() {
  json2array(el.scroll.floor.option).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function offerFormAddressElements() {
  json2array(el.address).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function offerFormRentalElements() {
  json2array(el.rental).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function offerFormFurnishingLabelsElements() {
  json2array(el.furnishing.labels).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function offerFormFurnishingOptionElements() {
  json2array(el.furnishing.option).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function offerFormDateMonthElements() {
  json2array(el.availability.dateMonth).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function offerFormEnergyOptionElements() {
  json2array(el.availability.energyDemand.option).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function offerFormNameElements() {
  json2array(el.name).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function offerFormContactElements() {
  json2array(el.contactLabel).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function methodOfPublicationElements() {
  json2array(el.methodOfPublication).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function paymentVipElements() {
  json2array(el.paymentVip).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function discountCodeElements() {
  json2array(el.paymentCode).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function alertModalElements() {
  json2array(el.alertModal).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function editFormElements() {
  json2array(el.editForm).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
}

export function alertModalElementsWithoutCancelButton() {
  const elements = [
    el.alertModal.box,
    el.alertModal.heading,
    el.alertModal.button,
    el.alertModal.closeButton,
    el.alertModal.text,
  ];
  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

export function alertModalElementsWithoutText() {
  const elements = [
    el.alertModal.box,
    el.alertModal.heading,
    el.alertModal.button,
    el.alertModal.closeButton,
  ];
  elements.forEach((element: string) => {
    cy.getByTestId(element).should("be.visible");
  });
}

export function checkOffersCzechPrivateLandlordImmedately(txt: any) {
  waitForPageLoaded();
  json2array(el.offers).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
  cy.getByTestId(el.offers.list)
    .find(`[data-test="${el.offers.showOfferButton}"]`)
    .should("be.length.gte", 1);
  cy.getByTestId(el.offers.showOfferButton).click({ force: true });
  cy.getByTestId(el.offerLocation).should("contain", txt.street);
  cy.getByTestId(el.offerTitle).should("contain", txt.area);
  cy.getByTestId(el.offerTitle).should("contain", dispoText);
  cy.getByTestId(el.offerPrice)
    .contains(txt.price)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerOwnerName).should(
    "contain",
    `${txt.fakerFirstName} ${txt.fakerLastName}`,
  );
  cy.getByTestId(el.offerOwnerType).should("contain", "Soukromý pronajímatel");
  cy.getByTestId(el.offerDetailDescription).should("contain", txt.description);
  cy.getByTestId(el.offerDetailFees)
    .contains(txt.fees)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerDetailCaution)
    .contains(txt.deposit)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerKeyValueInfo).within(() => {
    cy.contains("div", "Provize").find("a").should("contain", "Ne");
    cy.contains("div", "Druh").find("p").should("contain", "Pronájem");
    cy.contains("div", "Dispozice")
      .find("a")
      .should("contain", randomSelectData.dispo.text);
    cy.contains("div", "Výměra").find("a").should("contain", txt.area);
    cy.contains("div", "Podlaží")
      .find("p")
      .should("contain", randomSelectData.floorNumber.text);
    cy.contains("div", "Vybavení")
      .find("a")
      .should("contain", randomSelectData.furnishing.text);
    cy.contains("div", "Energetická náročnost")
      .find("p")
      .should("contain", randomSelectData.energy.text);
    cy.contains("div", "K nastěhování").find("p").should("contain", "Ihned");
  });
}

export function clickDateFrom() {
  cy.getByTestId(el.availability.from).click({ force: true });
}

export function clickSubmitButtonNoData() {
  cy.getByTestId(el.submitButton).click();
  checkWarningsCzech();
}

export function clickSubmitButtonBadData() {
  cy.getByTestId(el.submitButton).click();
  checkWarningsBadTataCzech();
}

export function clickSubmitButton(password: string, txt: any) {
  cy.getByTestId(el.submitButton).click();
  cy.wait("@email-exist")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.be.oneOf([200, 404]);
      if (resp?.statusCode === 200) {
        cy.getByTestId(el.avatarEmail).should("be.visible");
      } else if (resp?.statusCode === 404) {
        checkSignUpModalElements();
        cy.getByTestId(el.regPasswordInput).type(password);
        cy.getByTestId(el.regButton).click();
        cy.getByTestId(el.avatarEmail).should("contain", txt.fakerEmail);
      }
    });
}

function checkWarningsCzech() {
  cy.getByTestId(el.globalSelectBox).should("contain", "Povinné pole");
  cy.getByTestId(el.scroll.area.areaDiv).should("contain", "Musí být číslo");
  cy.getByTestId(el.address.town).should("contain", "Musíte vyplnit město.");
  cy.getByTestId(el.address.street).should("not.exist");
  cy.getByTestId(el.globalFormBox).should("contain", "Musí být číslo");
  cy.getByTestId(el.globalDateBox).should(
    "contain",
    "Datum nesmí být v minulosti",
  );
  cy.getByTestId(el.contactLabel.labelPhoneForm).should(
    "contain",
    "Povinné pole",
  );
}

function checkWarningsBadTataCzech() {
  cy.getByTestId(el.address.street).should("contain", "Musíte vyplnit ulici.");
  cy.getByTestId(el.contactLabel.labelPhoneForm).should(
    "contain",
    "Tohle nevypadá jako pravé číslo",
  );
}

export function fillBadData(txt: any) {
  cy.getByTestId(el.scroll.area.input)
    .clear({ force: true })
    .type(txt.area.toString());
  cy.getByTestId(el.address.townSearch).clear({ force: true }).type(txt.town);
  cy.getByTestId(el.address.townSectionPopover).should("be.visible");
  cy.getByTestId(el.address.streetText).should("be.visible");
  cy.getByTestId(el.address.streetSearch)
    .clear({ force: true })
    .type("txt.street"); // to je naschval
  cy.getByTestId(el.address.streetSectionPopver).should("be.visible");
  cy.getByTestId(el.rental.price)
    .clear({ force: true })
    .type(txt.price.toString());
  cy.getByTestId(el.rental.fees)
    .clear({ force: true })
    .type(txt.fees.toString());
  cy.getByTestId(el.rental.deposit)
    .clear({ force: true })
    .type(txt.deposit.toString());
  cy.getByTestId(el.globalTextArea)
    .clear({ force: true })
    .type(txt.description);
  cy.getByTestId(el.name.firstName).clear().type(faker.person.firstName());
  cy.getByTestId(el.name.lastName).clear().type(faker.person.lastName());
  cy.getByTestId(el.contactLabel.labelEmail).clear().type(txt.fakerEmail);
  cy.getByTestId(el.contactLabel.labelPhoneNumberInput)
    .clear()
    .type(faker.phone.number("0## ### ###"));
}

export function fillValidDataCzechLandlordImmediately(txt: any) {
  cy.getByTestId(el.scroll.area.input)
    .should("be.visible")
    .clear({ force: true })
    .type(txt.area.toString());
  cy.getByTestId(el.address.townSearch).clear({ force: true }).type(txt.town);
  cy.getByTestId(el.address.townSectionPopover).should("be.visible");
  cy.getByTestId(el.address.streetText).should("be.visible");
  cy.wait(500);
  cy.getByTestId(el.address.streetSearch)
    .should("be.visible")
    .clear({ force: true })
    .type(txt.street);
  cy.getByTestId(el.address.streetSectionPopver).should("be.visible");
  cy.getByTestId(el.globalMapBox).should("be.visible");
  cy.getByTestId(el.rental.price)
    .clear({ force: true })
    .type(txt.price.toString());
  cy.getByTestId(el.rental.fees)
    .clear({ force: true })
    .type(txt.fees.toString());
  cy.getByTestId(el.rental.deposit)
    .clear({ force: true })
    .type(txt.deposit.toString());
  cy.getByTestId(el.globalTextArea)
    .clear({ force: true })
    .type(txt.description);
  cy.getByTestId(el.name.firstName).clear().type(txt.fakerFirstName);
  cy.getByTestId(el.name.lastName).clear().type(txt.fakerLastName);
  cy.getByTestId(el.contactLabel.labelEmail).clear().type(txt.fakerEmail);
  cy.getByTestId(el.contactLabel.labelPhoneNumberInput)
    .clear()
    .type(faker.phone.number("777 ### ###"));
}

export function selectData() {
  cy.selectOption(
    el.scroll.dispo.selectButton,
    randomSelectData.dispo.value,
    true,
  );
  cy.selectOption(
    el.scroll.floor.floorNumberSelect,
    randomSelectData.floorNumber.value,
    true,
  );
  cy.selectOption(
    el.furnishing.furnishingSelect,
    randomSelectData.furnishing.value,
    true,
  );
  cy.selectOption(
    el.availability.energyDemand.energySelect,
    randomSelectData.energy.value,
    true,
  );
}

export function clickUlovdomovOffer() {
  waitForPageLoaded();
  cy.getByTestId(el.availability.ulovdomovOffer).click();
  methodOfPublicationElements();
}

export function clickVipListing() {
  waitForPageLoaded();
  cy.getByTestId(el.methodOfPublication.vipButton).click({ force: true });
  paymentVipElements();
}

export function clickAndFillDiscountCode(discountCode: string) {
  waitForPageLoaded();
  cy.getByTestId(el.paymentVip.paymentPromoCodeButton).click({ force: true });
  discountCodeElements();
  cy.getByTestId(el.paymentCode.paymentDiscountCodeInput).type(discountCode);
  cy.getByTestId(el.paymentCode.paymentDiscountCodeSubmit).click();
  alertModalElementsWithoutCancelButton();
  cy.getByTestId(el.alertModal.button).click();
}

export function submitPayment() {
  waitForPageLoaded();
  cy.getByTestId(el.paymentVip.submitPaymentMethod).click();
  cy.url().should("include", urls.MY_OFFERS.PAYMENT_SUCC);
  alertModalElementsWithoutText();
  cy.getByTestId(el.alertModal.button).click();
  cy.wait("@offerId")
    .its("response")
    .then((resp) => {
      myOfferId = resp?.body.id;

      return myOfferId;
    });
}

export function publishOffer(offerId: string) {
  cy.getByTestId(`offerId=${offerId}`).within(() => {
    cy.getByTestId(el.stopOrPublishButton).click({ force: true });
  });
  cy.getByTestId(el.alertModal.box).within(() => {
    cy.getByTestId(el.alertModal.button).should("be.visible");
    cy.getByTestId(el.alertModal.cancelButton).click();
  });
  cy.getByTestId(el.alertModal.box).should("not.exist");
  cy.intercept("POST", "**/activate").as("active");
  cy.getByTestId(`offerId=${offerId}`).within(() => {
    cy.getByTestId(el.stopOrPublishButton).click({ force: true });
  });
  cy.getByTestId(el.alertModal.box).within(() => {
    cy.getByTestId(el.alertModal.button).click();
  });
  cy.wait("@active");
}

export function checkWarnings() {
  cy.getByTestId(el.globalSelectBox).should("contain", "Povinné pole");
  cy.getByTestId(el.scroll.area.areaDiv).should("contain", "Musí být číslo");
  cy.getByTestId(el.address.town).should("contain", "Musíte vyplnit město.");
  cy.getByTestId(el.address.street).should("not.exist");
  cy.getByTestId(el.globalFormBox).should("contain", "Musí být číslo");
  cy.getByTestId(el.globalDateBox).should(
    "contain",
    "Datum nesmí být v minulosti",
  );
  cy.getByTestId(el.contactLabel.labelPhoneForm).should(
    "contain",
    "Povinné pole",
  );
}

export function openPromoWatchDog() {
  cy.getByTestId(el.dogPromo).should("be.visible");
  cy.getByTestId(el.dogPromoButton).click();
}

export function goToFirstOfferFromList() {
  cy.intercept("GET", "**/related").as("related");
  cy.getByTestId(el.offerSingle)
    .first()
    .within(() => {
      cy.getByTestId(el.actionButtons).find("a").click();
    });
  waitForPageLoaded();
  cy.wait("@related")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.equal(200);
    });
}

export function editCzechOfferFromDetail(txt: any) {
  cy.getByTestId(el.offerDetailEditButton).click({ force: true });
  waitForPageLoaded();
  editFormElements();
  cy.wait(500);
  cy.getByTestId(el.editForm.date).click({ force: true });
  cy.getByTestId(el.editForm.dateDay)
    .clear({ force: true })
    .type(txt.dateDay.toString());
  cy.selectOption(
    el.editForm.dateMonth,
    randomSelectData.dateMonth.value,
    true,
  );
  cy.getByTestId(el.editForm.dateYear)
    .clear({ force: true })
    .type(txt.dateYear.toString());
  cy.getByTestId(el.editForm.submitButton).click();
  cy.wait("@myOffers");
}

export function checkOffersEditedCzech(txt: any) {
  waitForPageLoaded();
  json2array(el.offers).forEach((element: any) => {
    cy.getByTestId(`${element}`).should("be.visible");
  });
  cy.getByTestId(el.offers.list)
    .find(`[data-test="${el.offers.showOfferButton}"]`)
    .should("be.length.gte", 1);
  cy.getByTestId(el.offers.showOfferButton).click({ force: true });
  cy.getByTestId(el.offerLocation).should("contain", txt.street);
  cy.getByTestId(el.offerTitle).should("contain", txt.area);
  cy.getByTestId(el.offerTitle).should("contain", dispoText);
  cy.getByTestId(el.offerPrice)
    .contains(txt.price)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerOwnerName).should(
    "contain",
    `${txt.fakerFirstName} ${txt.fakerLastName}`,
  );
  cy.getByTestId(el.offerOwnerType).should("contain", "Soukromý pronajímatel");
  cy.getByTestId(el.offerDetailDescription).should("contain", txt.description);
  cy.getByTestId(el.offerDetailFees)
    .contains(txt.fees)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerDetailCaution)
    .contains(txt.deposit)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerKeyValueInfo).within(() => {
    cy.contains("div", "Provize").find("a").should("contain", "Ne");
    cy.contains("div", "Druh").find("p").should("contain", "Pronájem");
    cy.contains("div", "Dispozice")
      .find("a")
      .should("contain", randomSelectData.dispo.text);
    cy.contains("div", "Výměra").find("a").should("contain", txt.area);
    cy.contains("div", "Podlaží")
      .find("p")
      .should("contain", randomSelectData.floorNumber.text);
    cy.contains("div", "Vybavení")
      .find("a")
      .should("contain", randomSelectData.furnishing.text);
    cy.contains("div", "Energetická náročnost")
      .find("p")
      .should("contain", randomSelectData.energy.text);
    cy.contains("div", "K nastěhování")
      .find("p")
      .should(
        "contain",
        `${txt.dateDay}. ${randomSelectData.dateMonth.value}. ${txt.dateYear}`,
      );
  });
}

export function editCzechOfferFromOffers() {
  cy.visit(urls.MY_OFFERS.URL);
  cy.getByTestId(el.editOffersButton).click({ force: true });
  waitForPageLoaded();
  editFormElements();
  cy.getByTestId(el.editForm.submitButton).click();
  cy.wait("@myOffers");
}

export function deActivateOffer() {
  cy.visit(urls.MY_OFFERS.URL);
  waitForPageLoaded();
  cy.wait("@myOffers")
    .its("response")
    .then((resp) => {
      expect(resp?.body.offers[0]).to.have.property("id");
      myOfferId = resp?.body.offers[0].id;
      cy.getByTestId(`offerId=${myOfferId}`).within(() => {
        cy.getByTestId(el.stopOrPublishButton).click({ force: true });
      });
      cy.getByTestId(el.alertModal.box).within(() => {
        cy.getByTestId(el.alertModal.button).should("be.visible");
        cy.getByTestId(el.alertModal.cancelButton).click();
      });
      cy.getByTestId(el.alertModal.box).should("not.exist");
      cy.intercept("POST", "**/deactivate").as("deactive");
      cy.getByTestId(`offerId=${myOfferId}`).within(() => {
        cy.getByTestId(el.stopOrPublishButton).click({ force: true });
      });
      cy.getByTestId(el.alertModal.box).within(() => {
        cy.getByTestId(el.alertModal.button).click();
      });
      cy.wait("@deactive");
    });
}

export function checkOfferError() {
  cy.visit(offerAddressUrl + myOfferId);
  cy.getByTestId(el.noLivingHeading).should("be.visible");
  cy.getByTestId(el.noLivingText).should("be.visible");
  cy.getByTestId(el.noLivingHeading).should("contain", "Tady nikdo nebydlí");
}

export function checkDeactivatedOffer(txt: any) {
  cy.visit(offerAddressUrl + myOfferId);
  deactivatedElements();
  cy.getByTestId(el.offerTitle).should("contain", txt.area);
  cy.getByTestId(el.offerTitle).should("contain", dispoText);
  cy.getByTestId(el.offerTitle).should("contain", dispoText);
  cy.getByTestId(el.offerDetailDescription).should("contain", txt.description);
  cy.getByTestId(el.offerDetailFees)
    .contains(txt.fees)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerDetailCaution)
    .contains(txt.deposit)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerKeyValueInfo).within(() => {
    cy.contains("div", "Provize").find("a").should("contain", "Ne");
    cy.contains("div", "Druh").find("p").should("contain", "Pronájem");
    cy.contains("div", "Dispozice")
      .find("a")
      .should("contain", randomSelectData.dispo.text);
    cy.contains("div", "Výměra").find("a").should("contain", txt.area);
    cy.contains("div", "Podlaží")
      .find("p")
      .should("contain", randomSelectData.floorNumber.text);
    cy.contains("div", "Vybavení")
      .find("a")
      .should("contain", randomSelectData.furnishing.text);
    cy.contains("div", "Energetická náročnost")
      .find("p")
      .should("contain", randomSelectData.energy.text);
    cy.contains("div", "K nastěhování")
      .find("p")
      .should(
        "contain",
        `${txt.dateDay}. ${randomSelectData.dateMonth.value}. ${txt.dateYear}`,
      );
  });
}

export function checkDeactivatedOfferAsOther(txt: any) {
  cy.visit(offerAddressUrl + myOfferId);
  deactivatedElementsAsAnonym();
  cy.getByTestId(el.offerTitle).should("contain", txt.area);
  cy.getByTestId(el.offerTitle).should("contain", dispoText);
  cy.getByTestId(el.offerTitle).should("contain", dispoText);
  cy.getByTestId(el.offerDetailDescription).should("contain", txt.description);
  cy.getByTestId(el.offerDetailFees)
    .contains(txt.fees)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerDetailCaution)
    .contains(txt.deposit)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerKeyValueInfo).within(() => {
    cy.contains("div", "Provize").find("a").should("contain", "Ne");
    cy.contains("div", "Druh").find("p").should("contain", "Pronájem");
    cy.contains("div", "Dispozice")
      .find("a")
      .should("contain", randomSelectData.dispo.text);
    cy.contains("div", "Výměra").find("a").should("contain", txt.area);
    cy.contains("div", "Podlaží")
      .find("p")
      .should("contain", randomSelectData.floorNumber.text);
    cy.contains("div", "Vybavení")
      .find("a")
      .should("contain", randomSelectData.furnishing.text);
    cy.contains("div", "Energetická náročnost")
      .find("p")
      .should("contain", randomSelectData.energy.text);
    cy.contains("div", "K nastěhování")
      .find("p")
      .should(
        "contain",
        `${txt.dateDay}. ${randomSelectData.dateMonth.value}. ${txt.dateYear}`,
      );
  });
}

export function checkNonEditedOffer(txt: any) {
  cy.visit(offerAddressUrl + myOfferId);
  cy.getByTestId(el.offerLocation).should("contain", txt.street);
  cy.getByTestId(el.offerTitle).should("contain", txt.area);
  cy.getByTestId(el.offerTitle).should("contain", dispoText);
  cy.getByTestId(el.offerPrice)
    .contains(txt.price)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerOwnerName).should(
    "contain",
    `${txt.fakerFirstName} ${txt.fakerLastName}`,
  );
  cy.getByTestId(el.offerOwnerType).should("contain", "Soukromý pronajímatel");
  cy.getByTestId(el.offerDetailDescription).should("contain", txt.description);
  cy.getByTestId(el.offerDetailFees)
    .contains(txt.fees)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerDetailCaution)
    .contains(txt.deposit)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerKeyValueInfo).within(() => {
    cy.contains("div", "Provize").find("a").should("contain", "Ne");
    cy.contains("div", "Druh").find("p").should("contain", "Pronájem");
    cy.contains("div", "Dispozice")
      .find("a")
      .should("contain", randomSelectData.dispo.text);
    cy.contains("div", "Výměra").find("a").should("contain", txt.area);
    cy.contains("div", "Podlaží")
      .find("p")
      .should("contain", randomSelectData.floorNumber.text);
    cy.contains("div", "Vybavení")
      .find("a")
      .should("contain", randomSelectData.furnishing.text);
    cy.contains("div", "Energetická náročnost")
      .find("p")
      .should("contain", randomSelectData.energy.text);
    cy.contains("div", "K nastěhování").find("p").should("contain", "Ihned");
  });
}

export function checkOffer(txt: any) {
  cy.visit(offerAddressUrl + myOfferId);
  cy.reload();
  cy.getByTestId(el.offerLocation).should("contain", txt.street);
  cy.getByTestId(el.offerTitle).should("contain", txt.area);
  cy.getByTestId(el.offerTitle).should("contain", dispoText);
  cy.getByTestId(el.offerPrice)
    .contains(txt.price)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerOwnerName).should(
    "contain",
    `${txt.fakerFirstName} ${txt.fakerLastName}`,
  );
  cy.getByTestId(el.offerOwnerType).should("contain", "Soukromý pronajímatel");
  cy.getByTestId(el.offerDetailDescription).should("contain", txt.description);
  cy.getByTestId(el.offerDetailFees)
    .contains(txt.fees)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerDetailCaution)
    .contains(txt.deposit)
    .then((elem) => {
      expect(isVisible(elem)).to.be.true;
    });
  cy.getByTestId(el.offerKeyValueInfo).within(() => {
    cy.contains("div", "Provize").find("a").should("contain", "Ne");
    cy.contains("div", "Druh").find("p").should("contain", "Pronájem");
    cy.contains("div", "Dispozice")
      .find("a")
      .should("contain", randomSelectData.dispo.text);
    cy.contains("div", "Výměra").find("a").should("contain", txt.area);
    cy.contains("div", "Podlaží")
      .find("p")
      .should("contain", randomSelectData.floorNumber.text);
    cy.contains("div", "Vybavení")
      .find("a")
      .should("contain", randomSelectData.furnishing.text);
    cy.contains("div", "Energetická náročnost")
      .find("p")
      .should("contain", randomSelectData.energy.text);
    cy.contains("div", "K nastěhování")
      .find("p")
      .should(
        "contain",
        `${txt.dateDay}. ${randomSelectData.dateMonth.value}. ${txt.dateYear}`,
      );
  });
}

export function deleteOffer() {
  cy.visit(urls.MY_OFFERS.URL);
  waitForPageLoaded();
  cy.wait("@myOffers")
    .its("response")
    .then((resp) => {
      expect(resp?.body.offers[0]).to.have.property("id");
      myOfferId = resp?.body.offers[0].id;
      cy.getByTestId(`offerId=${myOfferId}`).within(() => {
        cy.getByTestId(el.deleteOffer).click({ force: true });
      });
      cy.getByTestId(el.alertModal.button).click();
      cy.getByTestId(el.offers.list).children("div").should("have.length", "0");
    });
  cy.wait("@myOffers");
}

export function visitOffer() {
  cy.visit(offerAddressUrl + myOfferId);
}

export function offerPassword(pass: string) {
  const password: any = pass !== undefined ? pass : faker.internet.password();
  cy.getByTestId(el.pass).clear().type(password);
}
