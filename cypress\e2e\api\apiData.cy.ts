import { getUdApiUrl } from "../../helper/helper";

it("Check offerData", () => {
  cy.request({
    method: "GET",
    url: `${getUdApiUrl()}/offer/data?limit=5`,
    headers: {
      authorization: `Bearer ${Cypress.env("STAGING_APIDATA_TOKEN")}`,
    },
  })
    .then((resp) => {
      expect(resp.status).equal(200);
      expect(resp.body).has.property("data");
    })
    .then((resp) => {
      expect(typeof resp.body.data).to.eq("object");
      expect(resp.body.data.length).not.to.eq(0);

      resp.body.data.forEach((obj: any) => {
        expect(obj).has.property("id").to.be.not.null;
        expect(obj).has.property("description").to.be.not.null;
        expect(obj).has.property("url").to.be.not.null;
        expect(obj).has.property("parameters").to.be.not.null;
        expect(typeof obj.parameters).to.eq("object").to.be.not.null;

        obj.parameters.forEach((param: any) => {
          expect(param).has.property("name").to.be.not.null;
          expect(param).has.property("value").to.be.not.undefined;
        });
      });
    });
});
