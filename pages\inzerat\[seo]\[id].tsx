import { captureException } from "@sentry/nextjs";
import axios from "axios";
import { GetServerSideProps, NextPage } from "next";
import {
  getStoryblokApi,
  StoryblokComponent,
  StoryData,
} from "@storyblok/react";
import { parseCookies } from "nookies";

import useRouter from "hooks/useRouter";
import OfferDetail from "scenes/OfferDetail";
import { DetailResponse } from "@ud/api/newPhpApi/__generated__/newPhpApi.generated";
import { OfferDeleted } from "scenes/OfferDeleted";
import { NEW_ENDPOINT_COOKIE_NAME } from "utils/api/consts";

const Index: NextPage<{
  offer: DetailResponse;
  story: StoryData | null;
}> = ({ offer, story }) => {
  const { isFallback } = useRouter();

  const storyBlok = story ? <StoryblokComponent blok={story.content} /> : null;

  if (offer.data.status === "DELETED") {
    return <OfferDeleted />;
  }

  return (
    <OfferDetail
      offer={offer.data}
      isFallback={isFallback}
      blogComponents={storyBlok}
    />
  );
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const xForwardedFor = context.req.headers["x-forwarded-for"];
  const ip = Array.isArray(xForwardedFor)
    ? xForwardedFor[0].split(",")[0].trim()
    : xForwardedFor?.split(",")[0].trim() ||
      context.req.socket?.remoteAddress ||
      null;

  const storyblokApi = getStoryblokApi();
  const apiParams = {
    version: context.preview ? "draft" : undefined,
    token: context.preview
      ? process.env.NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN
      : undefined,
    resolve_relations: ["root_card.root"],
  };

  let resStory;
  try {
    resStory = await storyblokApi.get("cdn/stories/offer-detail", apiParams);
  } catch (err) {
    captureException(err);
  }

  const { params } = context;
  const idAsString = params?.id;

  if (!parseInt(idAsString?.toString() || "NaN")) {
    return { notFound: true };
  }

  const id = parseInt(idAsString?.toString() ?? "0");

  const cookies = parseCookies(context);
  const endpointUrl =
    cookies[NEW_ENDPOINT_COOKIE_NAME] ||
    process.env.NEXT_PUBLIC_NEW_PHP_API_URL;

  try {
    const res = await axios.get(
      `${endpointUrl}/v1/offer/detail?offerId=${id}`,
      {
        headers: {
          "x-forwarded-for": ip,
        },
      },
    );
    const offer = res.data;

    if (offer) {
      return {
        props: {
          offer,
          story: resStory?.data.story || null,
        },
      };
    }

    return { notFound: true };
  } catch (error) {
    return {
      notFound: true,
    };
  }
};

export default Index;
