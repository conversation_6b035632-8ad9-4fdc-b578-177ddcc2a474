import { GetServerSideProps } from "next/types";

import InsertOfferWrapper from "components/InsertOfferWrapper";
import TypeInsertForm from "scenes/InsertOffer/form/TypeInsert";
import { CustomAppPage, EditOfferUnpaid } from "src/types/types";

const TypeInsert: CustomAppPage<EditOfferUnpaid> = (props) => (
  <InsertOfferWrapper title="Vyberte si z výhodné inzerce" {...props}>
    <TypeInsertForm buttonNextText="Další" />
  </InsertOfferWrapper>
);

TypeInsert.hasHiddenFooter = true;

export default TypeInsert;

export const getServerSideProps: GetServerSideProps = async ({ query }) => ({
  props: {
    id: query.id as string,
    status: query.status as string,
  },
});
