import { newPhpApi as api } from "../newPhpApi";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    getRobotsTxt: build.query<GetRobotsTxtApiResponse, GetRobotsTxtApiArg>({
      query: () => ({ url: `/robots.txt` }),
    }),
    getV1Health: build.query<GetV1HealthApiResponse, GetV1HealthApiArg>({
      query: () => ({ url: `/v1/health` }),
    }),
    postV1LandlordUpdate: build.mutation<
      PostV1LandlordUpdateApiResponse,
      PostV1LandlordUpdateApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/landlord/update`,
        method: "POST",
        body: queryArg.updateLandlordRequest,
      }),
    }),
    getV1MakeDataStoresByDataId: build.query<
      GetV1MakeDataStoresByDataIdApiResponse,
      GetV1MakeDataStoresByDataIdApiArg
    >({
      query: (queryArg) => ({ url: `/v1/make/data-stores/${queryArg.dataId}` }),
    }),
    postV1OfferCount: build.mutation<
      PostV1OfferCountApiResponse,
      PostV1OfferCountApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/offer/count`,
        method: "POST",
        body: queryArg.findRequest,
      }),
    }),
    postV1OfferCreate: build.mutation<
      PostV1OfferCreateApiResponse,
      PostV1OfferCreateApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/offer/create`,
        method: "POST",
        body: queryArg.createRequest,
      }),
    }),
    getV1OfferData: build.query<
      GetV1OfferDataApiResponse,
      GetV1OfferDataApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/offer/data`,
        params: { lastOfferId: queryArg.lastOfferId, limit: queryArg.limit },
      }),
    }),
    getV1OfferDetail: build.query<
      GetV1OfferDetailApiResponse,
      GetV1OfferDetailApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/offer/detail`,
        params: { offerId: queryArg.offerId, counter: queryArg.counter },
      }),
    }),
    getV1OfferDetailRaw: build.query<
      GetV1OfferDetailRawApiResponse,
      GetV1OfferDetailRawApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/offer/detail-raw`,
        params: { offerId: queryArg.offerId },
      }),
    }),
    postV1OfferFind: build.mutation<
      PostV1OfferFindApiResponse,
      PostV1OfferFindApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/offer/find`,
        method: "POST",
        body: queryArg.findRequest,
        params: {
          page: queryArg.page,
          perPage: queryArg.perPage,
          sorting: queryArg.sorting,
        },
      }),
    }),
    getV1OfferLatest: build.query<
      GetV1OfferLatestApiResponse,
      GetV1OfferLatestApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/offer/latest`,
        body: queryArg.latestRequest,
      }),
    }),
    getV1OfferRelated: build.query<
      GetV1OfferRelatedApiResponse,
      GetV1OfferRelatedApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/offer/related`,
        params: { offerId: queryArg.offerId },
      }),
    }),
    putV1OfferStatusChange: build.mutation<
      PutV1OfferStatusChangeApiResponse,
      PutV1OfferStatusChangeApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/offer/status-change`,
        method: "PUT",
        body: queryArg.statusChangeRequest,
      }),
    }),
    getV1VerifyPhoneCheck: build.query<
      GetV1VerifyPhoneCheckApiResponse,
      GetV1VerifyPhoneCheckApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/verify/phone-check`,
        params: {
          prefix: queryArg.prefix,
          number: queryArg.number,
          otpCode: queryArg.otpCode,
          sentSmsId: queryArg.sentSmsId,
        },
      }),
    }),
    postV1VerifyPhoneResend: build.mutation<
      PostV1VerifyPhoneResendApiResponse,
      PostV1VerifyPhoneResendApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/verify/phone-resend`,
        method: "POST",
        body: queryArg.phoneResendRequest,
      }),
    }),
    postV1VerifyPhoneSend: build.mutation<
      PostV1VerifyPhoneSendApiResponse,
      PostV1VerifyPhoneSendApiArg
    >({
      query: (queryArg) => ({
        url: `/v1/verify/phone-send`,
        method: "POST",
        body: queryArg.phoneSendRequest,
      }),
    }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as newPhpApi };
export type GetRobotsTxtApiResponse = unknown;
export type GetRobotsTxtApiArg = void;
export type GetV1HealthApiResponse =
  /** status 200 Return true, if application is OK */ HealthResponse;
export type GetV1HealthApiArg = void;
export type PostV1LandlordUpdateApiResponse =
  /** status 200 Return updated landlord id */ UpdateLandlordResponse;
export type PostV1LandlordUpdateApiArg = {
  updateLandlordRequest: UpdateLandlordRequest;
};
export type GetV1MakeDataStoresByDataIdApiResponse =
  /** status 200 Return true, if application is OK */ DataStoresResponse;
export type GetV1MakeDataStoresByDataIdApiArg = {
  dataId?: number;
};
export type PostV1OfferCountApiResponse =
  /** status 200 Count offers by filter */ CountResponse;
export type PostV1OfferCountApiArg = {
  findRequest: FindRequest;
};
export type PostV1OfferCreateApiResponse =
  /** status 200 Create or Update offer by data */ CreateResponse;
export type PostV1OfferCreateApiArg = {
  createRequest: CreateRequest;
};
export type GetV1OfferDataApiResponse =
  /** status 200 Offer data */ OfferDataResponse;
export type GetV1OfferDataApiArg = {
  lastOfferId?: number;
  limit?: number;
};
export type GetV1OfferDetailApiResponse =
  /** status 200 Offer detail */ DetailResponse;
export type GetV1OfferDetailApiArg = {
  offerId?: number;
  counter?: ("detail" | "detail-page" | "map") | null;
};
export type GetV1OfferDetailRawApiResponse =
  /** status 200 Offer raw detail */ RawDetailResponse;
export type GetV1OfferDetailRawApiArg = {
  offerId?: number;
};
export type PostV1OfferFindApiResponse =
  /** status 200 Find offers by filter */ FindResponse;
export type PostV1OfferFindApiArg = {
  page?: number;
  perPage?: number;
  sorting?: "cheapest" | "latest" | "best";
  findRequest: FindRequest;
};
export type GetV1OfferLatestApiResponse =
  /** status 200 Get latest offers by propertyType and offerFunction */ LatestResponse;
export type GetV1OfferLatestApiArg = {
  latestRequest: LatestRequest;
};
export type GetV1OfferRelatedApiResponse =
  /** status 200 OfferRelated related list */ RelatedResponse;
export type GetV1OfferRelatedApiArg = {
  offerId?: number;
};
export type PutV1OfferStatusChangeApiResponse =
  /** status 200 Activate, deactivate or delete offer */ StatusChangeResponse;
export type PutV1OfferStatusChangeApiArg = {
  statusChangeRequest: StatusChangeRequest;
};
export type GetV1VerifyPhoneCheckApiResponse =
  /** status 200 Check phone number by code verification */ PhoneCheckResponse;
export type GetV1VerifyPhoneCheckApiArg = {
  prefix?: string;
  number?: string;
  otpCode?: string;
  sentSmsId?: string;
};
export type PostV1VerifyPhoneResendApiResponse =
  /** status 200 Create or Update offer by data */ PhoneResendResponse;
export type PostV1VerifyPhoneResendApiArg = {
  phoneResendRequest: PhoneResendRequest;
};
export type PostV1VerifyPhoneSendApiResponse =
  /** status 200 Create or Update offer by data */ PhoneSendResponse;
export type PostV1VerifyPhoneSendApiArg = {
  phoneSendRequest: PhoneSendRequest;
};
export type HealthData = {
  ok: boolean;
};
export type HealthResponse = {
  success: boolean;
  data: HealthData;
};
export type UpdateLandlordResponseData = {
  userId?: number | null;
};
export type UpdateLandlordResponse = {
  success: boolean;
  data: UpdateLandlordResponseData;
};
export type Phone = {
  prefix: string;
  number: string;
};
export type LandlordInvoiceData = {
  name: string;
  identificationNumber?: string | null;
  vatNumber?: string | null;
  address?: string | null;
};
export type UpdateLandlordRequest = {
  companyName?: string | null;
  contactPhone: Phone;
  firstName: string;
  lastName: string;
  userLandlordTypeId?: number | null;
  invoiceData?: LandlordInvoiceData | null;
};
export type Price = {
  min: number;
  max: number;
};
export type Filter = {
  conveniences: Array<string>;
  dispositions: Array<string>;
  location: string;
  offerType: string;
  price: Price;
  propertyType: string;
  sorting: string;
};
export type DataStoresData = {
  url: string;
  filter: Filter;
};
export type DataStoresResponse = {
  data: DataStoresData;
};
export type CountData = {
  count: number;
};
export type CountResponse = {
  success: boolean;
  data: CountData;
};
export type GeoCoordinates = {
  lat: number;
  lng: number;
};
export type BoundsRequest = {
  northEast: GeoCoordinates;
  southWest: GeoCoordinates;
};
export type PriceRange = {
  min?: number;
  max?: number;
};
export type FloorAreaRange = {
  min?: number;
  max?: number;
};
export type GardenAreaRange = {
  min?: number;
  max?: number;
};
export type FloorRange = {
  min?: number;
  max?: number;
};
export type FloorCountRange = {
  min?: number;
  max?: number;
};
export type FindRequest = {
  propertyType?: "flat" | "house" | "land" | "commercial" | "other";
  offerType: "sale" | "rent" | "auctions" | "coliving";
  colivingType?: Array<"fullRoom" | "sharedRoom">;
  disposition?: Array<
    | "onePlusKk"
    | "onePlusOne"
    | "twoPlusKk"
    | "twoPlusOne"
    | "threePlusKk"
    | "threePlusOne"
    | "fourPlusKk"
    | "fourPlusOne"
    | "fivePlusOne"
    | "fivePlusKk"
    | "sixAndMore"
  >;
  houseType?: Array<
    | "cottage"
    | "monumentOther"
    | "familyHouse"
    | "villa"
    | "turnKey"
    | "chalet"
    | "agriculturalFarm"
  >;
  bounds: BoundsRequest;
  roomCount?: Array<
    | "oneRoom"
    | "twoRooms"
    | "threeRooms"
    | "fourRooms"
    | "fivePlusRooms"
    | "atypical"
  >;
  price?: PriceRange;
  floorArea?: FloorAreaRange;
  gardenArea?: GardenAreaRange;
  furnished?: Array<"yes" | "no" | "partial">;
  isNoCommission?: boolean;
  convenience?: Array<
    | "balcony"
    | "loggia"
    | "terrace"
    | "washingMachine"
    | "fridge"
    | "dishwasher"
    | "cellar"
    | "garden"
    | "lift"
  >;
  houseConvenience?: Array<
    "cellar" | "garage" | "pool" | "parking" | "wheelchairAccessible"
  >;
  buildingType?: Array<"groundFloor" | "multiStorey">;
  objectType?: Array<"row" | "corner" | "inBlock" | "detached">;
  flatType?: Array<"maisonette" | "loft" | "attic">;
  buildingCondition?: Array<
    | "veryGood"
    | "good"
    | "bad"
    | "inConstruction"
    | "project"
    | "newBuild"
    | "demolition"
    | "preReconstruction"
    | "postReconstruction"
    | "inReconstruction"
  >;
  material?: Array<
    "wood" | "brick" | "stone" | "assembled" | "panel" | "skeleton" | "mixed"
  >;
  floor?: FloorRange;
  floorCount?: FloorCountRange;
  energyEfficiencyRating?: Array<"a" | "b" | "c" | "d" | "e" | "f" | "g">;
  ownership?: Array<"personal" | "cooperative" | "municipal">;
  locationType?: Array<
    | "cityCenter"
    | "quietPart"
    | "busyPart"
    | "outskirts"
    | "housingEstate"
    | "semiIsolated"
    | "isolated"
  >;
  surroundingType?: Array<
    | "residential"
    | "businessResidential"
    | "business"
    | "commercial"
    | "industrial"
    | "rural"
    | "recreational"
    | "unusedRecreational"
  >;
  protectedArea?: Array<
    "protectionZone" | "nationalPark" | "protectedLandscapeArea"
  >;
  availabilityType?: "asap" | "date";
  availabilityDate?: string;
  offerAge?: "day" | "week" | "month";
};
export type CreateData = {
  offerId: number;
};
export type CreateResponse = {
  success: boolean;
  data: CreateData;
};
export type CreateRequest = {
  offerId?: number | null;
  emailAddress?: string | null;
  phone?: Phone | null;
  disposition?:
    | (
        | "onePlusKk"
        | "onePlusOne"
        | "twoPlusKk"
        | "twoPlusOne"
        | "threePlusKk"
        | "threePlusOne"
        | "fourPlusKk"
        | "fourPlusOne"
        | "fivePlusOne"
        | "fivePlusKk"
        | "sixAndMore"
        | "atypical"
        | "commercial"
        | "housing"
        | "field"
        | "forests"
        | "meadows"
        | "gardens"
        | "otherLand"
        | "offices"
        | "warehouses"
        | "production"
        | "commercialSpace"
        | "accommodation"
        | "restaurant"
        | "agricultural"
        | "otherCommercial"
        | "cottage"
        | "monumentOther"
        | "other"
        | "familyHouse"
        | "rentalHouse"
        | "villa"
        | "garage"
        | "turnKey"
        | "chalet"
        | "agriculturalFarm"
        | "ponds"
        | "room"
        | "orchardsVineyards"
        | "virtualOffice"
        | "wineCellar"
        | "atticSpace"
        | "garageParking"
        | "mobileHome"
        | "multiGenerationalHouse"
        | "medicalPractice"
        | "apartments"
      )
    | null;
  offerType: "sale" | "rent" | "auctions" | "coliving";
  propertyType: "flat" | "house" | "land" | "commercial" | "other";
  colivingType?: ("fullRoom" | "sharedRoom") | null;
  price?: number | null;
  depositPrice?: number | null;
  monthlyFeesPrice?: number | null;
  priceUnit?:
    | (
        | "perRealEstate"
        | "perMonth"
        | "perSqM"
        | "perSqMPerMonth"
        | "perSqMPerYear"
        | "perYear"
        | "perDay"
        | "perHour"
        | "perSqMPerDay"
        | "perSqMPerHour"
      )
    | null;
  priceNote?: string | null;
  availability: "asap" | "date";
  firstTourDate?: string | null;
  firstTourDateTo?: string | null;
  furnishing: "yes" | "no" | "partial";
  usableArea: number;
  estateArea?: number | null;
  floorCount?: number | null;
  roomCount?:
    | (
        | "oneRoom"
        | "twoRooms"
        | "threeRooms"
        | "fourRooms"
        | "fivePlusRooms"
        | "atypical"
      )
    | null;
  floorLevel?: number | null;
  conveniences: Array<
    | "balcony"
    | "loggia"
    | "terrace"
    | "washingMachine"
    | "fridge"
    | "dishwasher"
    | "cellar"
    | "garden"
    | "lift"
  >;
  houseConveniences: Array<
    "cellar" | "garage" | "pool" | "parking" | "wheelchairAccessible"
  >;
  ownership?: ("personal" | "cooperative" | "municipal") | null;
  buildingType?: ("groundFloor" | "multiStorey") | null;
  buildingCondition?:
    | (
        | "veryGood"
        | "good"
        | "bad"
        | "inConstruction"
        | "project"
        | "newBuild"
        | "demolition"
        | "preReconstruction"
        | "postReconstruction"
        | "inReconstruction"
      )
    | null;
  material?:
    | (
        | "wood"
        | "brick"
        | "stone"
        | "assembled"
        | "panel"
        | "skeleton"
        | "mixed"
      )
    | null;
  flatType?: ("maisonette" | "loft" | "attic") | null;
  objectType?: ("row" | "corner" | "inBlock" | "detached") | null;
  locationType?:
    | (
        | "cityCenter"
        | "quietPart"
        | "busyPart"
        | "outskirts"
        | "housingEstate"
        | "semiIsolated"
        | "isolated"
      )
    | null;
  surroundingType: Array<
    | "residential"
    | "businessResidential"
    | "business"
    | "commercial"
    | "industrial"
    | "rural"
    | "recreational"
    | "unusedRecreational"
  >;
  protectedArea?:
    | ("protectionZone" | "nationalPark" | "protectedLandscapeArea")
    | null;
  energyEfficiencyRating?: ("a" | "b" | "c" | "d" | "e" | "f" | "g") | null;
  typeInsert?: ("privateLandlord" | "realEstateBroker") | null;
  description: string;
  videoTourUrl?: string | null;
  matterportUrl?: string | null;
  typePriceInsert?: ("start" | "standard" | "vip") | null;
  priceCommission?: boolean | null;
  builtUpArea?: number | null;
  specificDate?: string | null;
  village?: number | null;
  street?: number | null;
  streetNumber?: number | null;
};
export type OfferParameterData = {
  name: string;
  value?: any | null;
};
export type OfferData = {
  id: number;
  title: string;
  description: string;
  url: string;
  parameters: Array<OfferParameterData>;
};
export type OfferDataResponse = {
  success: boolean;
  data: Array<OfferData>;
};
export type LocationDetail = {
  id: number;
  name: string;
};
export type Nearby = {
  name: string;
  type: "mall" | "travelPoint";
  value: string;
};
export type PriceResponse = {
  value: number;
  currency: string;
};
export type Owner = {
  id: number;
  type?: string | null;
  firstName: string;
  surname: string;
  photo?: string | null;
  phone: string;
  isIn: boolean;
};
export type Photo = {
  id: number;
  path: string;
  alt: string;
};
export type MatterportUrl = {
  title: string;
  value: string;
};
export type PriceFormatedResponse = {
  title: string;
  value: string;
};
export type OfferFunctionOption = {
  title: string;
  id: "sale" | "rent" | "auctions" | "coliving";
};
export type OfferFunctionParameter = {
  title: string;
  options: Array<OfferFunctionOption>;
};
export type PropertyTypeOption = {
  title: string;
  id: "flat" | "house" | "land" | "commercial" | "other";
};
export type PropertyTypeParameter = {
  title: string;
  options: Array<PropertyTypeOption>;
};
export type RoomCountOption = {
  title: string;
  seo: string;
  id:
    | "oneRoom"
    | "twoRooms"
    | "threeRooms"
    | "fourRooms"
    | "fivePlusRooms"
    | "atypical";
};
export type RoomCountParameter = {
  title: string;
  seo: string;
  options: Array<RoomCountOption>;
};
export type DispositionOption = {
  title: string;
  seo: string;
  id:
    | "onePlusKk"
    | "onePlusOne"
    | "twoPlusKk"
    | "twoPlusOne"
    | "threePlusKk"
    | "threePlusOne"
    | "fourPlusKk"
    | "fourPlusOne"
    | "fivePlusOne"
    | "fivePlusKk"
    | "sixAndMore"
    | "atypical"
    | null;
};
export type DispositionParameter = {
  title: string;
  seo: string;
  options: Array<DispositionOption>;
};
export type HouseTypeOption = {
  title: string;
  seo: string;
  id:
    | "cottage"
    | "monumentOther"
    | "familyHouse"
    | "villa"
    | "turnKey"
    | "chalet"
    | "agriculturalFarm"
    | "multiGeneration"
    | null;
};
export type HouseTypeParameter = {
  title: string;
  seo: string;
  options: Array<HouseTypeOption>;
};
export type Balcony = {
  title: string;
  seo: string;
  value: string;
};
export type Pool = {
  title: string;
  seo: string;
  value: string;
};
export type BuildingConditionOption = {
  title: string;
  seo: string;
  id:
    | "veryGood"
    | "good"
    | "bad"
    | "inConstruction"
    | "project"
    | "newBuild"
    | "demolition"
    | "preReconstruction"
    | "postReconstruction"
    | "inReconstruction";
};
export type BuildingConditionParameter = {
  title: string;
  seo: string;
  options: Array<BuildingConditionOption>;
};
export type BuildingTypeOption = {
  title: string;
  seo: string;
  id: "groundFloor" | "multiStorey";
};
export type BuildingTypeParameter = {
  title: string;
  seo: string;
  options: Array<BuildingTypeOption>;
};
export type Cellar = {
  title: string;
  seo: string;
  value: string;
};
export type EstateArea = {
  title: string;
  seo: string;
  value: string;
};
export type FloorNumber = {
  title: string;
  seo: string;
  value: number;
};
export type Garage = {
  title: string;
  seo: string;
  value: string;
};
export type Loggia = {
  title: string;
  seo: string;
  value: string;
};
export type ObjectTypeOption = {
  title: string;
  seo: string;
  id: "row" | "corner" | "inBlock" | "detached";
};
export type ObjectTypeParameter = {
  title: string;
  seo: string;
  options: Array<ObjectTypeOption>;
};
export type OwnershipOption = {
  title: string;
  seo: string;
  id: "personal" | "cooperative" | "municipal";
};
export type OwnershipParameter = {
  title: string;
  seo: string;
  options: Array<OwnershipOption>;
};
export type ParkingLots = {
  title: string;
  seo: string;
  value: string;
};
export type Terrace = {
  title: string;
  seo: string;
  value: string;
};
export type UsableArea = {
  title: string;
  seo: string;
  value: string;
};
export type AcceptanceYear = {
  title: string;
  seo: string;
  value: string;
};
export type LowEnergy = {
  title: string;
  seo: string;
  value: string;
};
export type PriceNegotiation = {
  title: string;
  seo: string;
  value: string;
};
export type EnergyEfficiencyOption = {
  title: string;
  seo: string;
  id: "a" | "b" | "c" | "d" | "e" | "f" | "g";
};
export type EnergyEfficiencyParameter = {
  title: string;
  seo: string;
  options: Array<EnergyEfficiencyOption>;
};
export type FinishDate = {
  title: string;
  value: string;
};
export type FirstTourDate = {
  title: string;
  value: string;
};
export type FirstTourDateTo = {
  title: string;
  value: string;
};
export type FlatTypeOption = {
  title: string;
  seo: string;
  id: "maisonette" | "loft" | "attic";
};
export type FlatTypeParameter = {
  title: string;
  seo: string;
  options: Array<FlatTypeOption>;
};
export type FloorArea = {
  title: string;
  seo: string;
  value: string;
};
export type Floors = {
  title: string;
  seo: string;
  value: number;
};
export type FurnishedOption = {
  title: string;
  seo: string;
  id: "yes" | "no" | "partial";
};
export type FurnishedParameter = {
  title: string;
  seo: string;
  options: Array<FurnishedOption>;
};
export type Loft = {
  title: string;
  seo: string;
  value: string;
};
export type GasOption = {
  title: string;
  id: "individual" | "gasPipeline";
};
export type GasParameter = {
  title: string;
  options: Array<GasOption>;
};
export type GullyOption = {
  title: string;
  seo: string;
  id:
    | "publicSewer"
    | "wholeBuildingSewer"
    | "septicTank"
    | "sump"
    | "drainageChannel";
};
export type GullyParameter = {
  title: string;
  seo: string;
  options: Array<GullyOption>;
};
export type HeatingOption = {
  title: string;
  id:
    | "localGas"
    | "localSolidFuels"
    | "localElectric"
    | "centralGas"
    | "centralSolidFuels"
    | "centralElectric"
    | "centralDistrict"
    | "other"
    | "underfloor";
};
export type HeatingParameter = {
  title: string;
  options: Array<HeatingOption>;
};
export type ObjectAge = {
  title: string;
  value: number;
};
export type MaterialOption = {
  title: string;
  seo: string;
  id: "wood" | "brick" | "stone" | "assembled" | "panel" | "skeleton" | "mixed";
};
export type MaterialParameter = {
  title: string;
  seo: string;
  options: Array<MaterialOption>;
};
export type ObjectLocationOption = {
  title: string;
  id:
    | "cityCenter"
    | "quietPart"
    | "busyPart"
    | "outskirts"
    | "housingEstate"
    | "semiIsolated"
    | "isolated";
};
export type ObjectLocationParameter = {
  title: string;
  options: Array<ObjectLocationOption>;
};
export type PersonalOption = {
  title: string;
  id: "yes" | "no";
};
export type PersonalParameter = {
  title: string;
  options: Array<PersonalOption>;
};
export type ProtectionOption = {
  title: string;
  id: "protectionZone" | "nationalPark" | "protectedLandscapeArea";
};
export type ProtectionParameter = {
  title: string;
  options: Array<ProtectionOption>;
};
export type ReadyDate = {
  title: string;
  value: string;
};
export type ReconstructionYear = {
  title: string;
  value: number;
};
export type RoadTypeOption = {
  title: string;
  id:
    | "concrete"
    | "paved"
    | "asphalt"
    | "unpaved"
    | "stabilized"
    | "gravel"
    | "dirtRoad"
    | "noRoad";
};
export type RoadTypeParameter = {
  title: string;
  options: Array<RoadTypeOption>;
};
export type SurroundingTypeOption = {
  title: string;
  id:
    | "residential"
    | "businessResidential"
    | "business"
    | "commercial"
    | "industrial"
    | "rural"
    | "recreational"
    | "unusedRecreational";
};
export type SurroundingTypeParameter = {
  title: string;
  options: Array<SurroundingTypeOption>;
};
export type TelecommunicationOption = {
  title: string;
  id:
    | "telephone"
    | "internet"
    | "satellite"
    | "cableTV"
    | "cableDistribution"
    | "other";
};
export type TelecommunicationParameter = {
  title: string;
  options: Array<TelecommunicationOption>;
};
export type TransportOption = {
  title: string;
  id: "train" | "highway" | "road" | "publicTransport" | "bus";
};
export type TransportParameter = {
  title: string;
  options: Array<TransportOption>;
};
export type WaterOption = {
  title: string;
  id: "localSource" | "districtWaterSupply" | "well";
};
export type WaterParameter = {
  title: string;
  options: Array<WaterOption>;
};
export type Lift = {
  title: string;
  value: string;
};
export type WheelchairAccessible = {
  title: string;
  value: string;
};
export type GardenArea = {
  title: string;
  value: string;
};
export type DetailParameters = {
  offerFunction?: OfferFunctionParameter | null;
  propertyType?: PropertyTypeParameter | null;
  roomCount?: RoomCountParameter | null;
  disposition?: DispositionParameter | null;
  houseType?: HouseTypeParameter | null;
  balcony?: Balcony | null;
  pool?: Pool | null;
  buildingCondition?: BuildingConditionParameter | null;
  buildingType?: BuildingTypeParameter | null;
  cellar?: Cellar | null;
  estateArea?: EstateArea | null;
  floorNumber?: FloorNumber | null;
  garage?: Garage | null;
  loggia?: Loggia | null;
  objectType?: ObjectTypeParameter | null;
  ownership?: OwnershipParameter | null;
  parkingLots?: ParkingLots | null;
  terrace?: Terrace | null;
  usableArea?: UsableArea | null;
  acceptanceYear?: AcceptanceYear | null;
  lowEnergy?: LowEnergy | null;
  priceNegotiation?: PriceNegotiation | null;
  energyEfficiencyRating?: EnergyEfficiencyParameter | null;
  finishDate?: FinishDate | null;
  firstTourDate?: FirstTourDate | null;
  firstTourDateTo?: FirstTourDateTo | null;
  flatType?: FlatTypeParameter | null;
  floorArea?: FloorArea | null;
  floors?: Floors | null;
  furnished?: FurnishedParameter | null;
  loft?: Loft | null;
  gas?: GasParameter | null;
  gully?: GullyParameter | null;
  heating?: HeatingParameter | null;
  objectAge?: ObjectAge | null;
  material?: MaterialParameter | null;
  objectLocation?: ObjectLocationParameter | null;
  personal?: PersonalParameter | null;
  protection?: ProtectionParameter | null;
  readyDate?: ReadyDate | null;
  reconstructionYear?: ReconstructionYear | null;
  roadType?: RoadTypeParameter | null;
  surroundingType?: SurroundingTypeParameter | null;
  telecommunication?: TelecommunicationParameter | null;
  transport?: TransportParameter | null;
  water?: WaterParameter | null;
  lift?: Lift | null;
  wheelchairAccessible?: WheelchairAccessible | null;
  gardenArea?: GardenArea | null;
};
export type DetailData = {
  id: number;
  offerTypeId: "sale" | "rent" | "auctions" | "coliving";
  title: string;
  description: string;
  street?: LocationDetail | null;
  village?: LocationDetail | null;
  villagePart?: LocationDetail | null;
  district?: LocationDetail | null;
  region?: LocationDetail | null;
  geoCoordinates: GeoCoordinates;
  dispositionId?: number | null;
  seo: string;
  nearby?: Array<Nearby> | null;
  priceMedian?: PriceResponse | null;
  rentalPrice?: PriceResponse | null;
  monthlyFeePrice?: PriceResponse | null;
  depositPrice?: PriceResponse | null;
  priceUnit?:
    | (
        | "perRealEstate"
        | "perMonth"
        | "perSqM"
        | "perSqMPerMonth"
        | "perSqMPerYear"
        | "perYear"
        | "perDay"
        | "perHour"
        | "perSqMPerDay"
        | "perSqMPerHour"
      )
    | null;
  isNoCommission?: boolean;
  priceNote?: string | null;
  absoluteUrl: string;
  publishedAt?: string | null;
  adminUrl: string;
  status:
    | "DRAFT"
    | "ACTIVE"
    | "EXPIRED"
    | "CANCELED"
    | "IMPORTED"
    | "IMPORT_ACTIVE"
    | "IMPORT_EXPIRED"
    | "IMPORT_IGNORED"
    | "DELETED"
    | "IMPORT_DELETED";
  owner: Owner;
  showScamWarning: boolean;
  isLocked: boolean;
  isContacted: boolean;
  photos?: Array<Photo> | null;
  matterportUrl?: MatterportUrl | null;
  priceParameters?: Array<PriceFormatedResponse> | null;
  parameters: DetailParameters;
};
export type DetailResponse = {
  success: boolean;
  data: DetailData;
};
export type RawDetailLocality = {
  id: number;
  value: string;
};
export type RawDetailData = {
  offerId?: number;
  emailAddress?: string | null;
  phone?: string | null;
  disposition?:
    | (
        | "onePlusKk"
        | "onePlusOne"
        | "twoPlusKk"
        | "twoPlusOne"
        | "threePlusKk"
        | "threePlusOne"
        | "fourPlusKk"
        | "fourPlusOne"
        | "fivePlusOne"
        | "fivePlusKk"
        | "sixAndMore"
        | "atypical"
        | "commercial"
        | "housing"
        | "field"
        | "forests"
        | "meadows"
        | "gardens"
        | "otherLand"
        | "offices"
        | "warehouses"
        | "production"
        | "commercialSpace"
        | "accommodation"
        | "restaurant"
        | "agricultural"
        | "otherCommercial"
        | "cottage"
        | "monumentOther"
        | "other"
        | "familyHouse"
        | "rentalHouse"
        | "villa"
        | "garage"
        | "turnKey"
        | "chalet"
        | "agriculturalFarm"
        | "ponds"
        | "room"
        | "orchardsVineyards"
        | "virtualOffice"
        | "wineCellar"
        | "atticSpace"
        | "garageParking"
        | "mobileHome"
        | "multiGenerationalHouse"
        | "medicalPractice"
        | "apartments"
      )
    | null;
  offerType?: "sale" | "rent" | "auctions" | "coliving";
  propertyType?: "flat" | "house" | "land" | "commercial" | "other";
  colivingType?: ("fullRoom" | "sharedRoom") | null;
  price?: number | null;
  depositPrice?: number | null;
  monthlyFeesPrice?: number | null;
  priceUnit?:
    | (
        | "perRealEstate"
        | "perMonth"
        | "perSqM"
        | "perSqMPerMonth"
        | "perSqMPerYear"
        | "perYear"
        | "perDay"
        | "perHour"
        | "perSqMPerDay"
        | "perSqMPerHour"
      )
    | null;
  priceNote?: string | null;
  availability?: "asap" | "date";
  firstTourDate?: string | null;
  firstTourDateTo?: string | null;
  furnishing?: "yes" | "no" | "partial";
  usableArea?: number;
  estateArea?: number | null;
  floorCount?: number | null;
  roomCount?:
    | (
        | "oneRoom"
        | "twoRooms"
        | "threeRooms"
        | "fourRooms"
        | "fivePlusRooms"
        | "atypical"
      )
    | null;
  floorLevel?: number | null;
  conveniences?: Array<
    | "balcony"
    | "loggia"
    | "terrace"
    | "washingMachine"
    | "fridge"
    | "dishwasher"
    | "cellar"
    | "garden"
    | "lift"
  >;
  houseConveniences?: Array<
    "cellar" | "garage" | "pool" | "parking" | "wheelchairAccessible"
  >;
  ownership?: ("personal" | "cooperative" | "municipal") | null;
  buildingType?: ("groundFloor" | "multiStorey") | null;
  buildingCondition?:
    | (
        | "veryGood"
        | "good"
        | "bad"
        | "inConstruction"
        | "project"
        | "newBuild"
        | "demolition"
        | "preReconstruction"
        | "postReconstruction"
        | "inReconstruction"
      )
    | null;
  material?:
    | (
        | "wood"
        | "brick"
        | "stone"
        | "assembled"
        | "panel"
        | "skeleton"
        | "mixed"
      )
    | null;
  flatType?: ("maisonette" | "loft" | "attic") | null;
  objectType?: ("row" | "corner" | "inBlock" | "detached") | null;
  locationType?:
    | (
        | "cityCenter"
        | "quietPart"
        | "busyPart"
        | "outskirts"
        | "housingEstate"
        | "semiIsolated"
        | "isolated"
      )
    | null;
  surroundingType?: Array<
    | "residential"
    | "businessResidential"
    | "business"
    | "commercial"
    | "industrial"
    | "rural"
    | "recreational"
    | "unusedRecreational"
  >;
  protectedArea?:
    | ("protectionZone" | "nationalPark" | "protectedLandscapeArea")
    | null;
  energyEfficiencyRating?: ("a" | "b" | "c" | "d" | "e" | "f" | "g") | null;
  typeInsert?: ("privateLandlord" | "realEstateBroker") | null;
  description?: string;
  videoTourUrl?: string | null;
  matterportUrl?: string | null;
  typePriceInsert?: ("start" | "standard" | "vip") | null;
  priceCommission?: boolean | null;
  builtUpArea?: number | null;
  specificDate?: string | null;
  village?: RawDetailLocality | null;
  street?: RawDetailLocality | null;
  streetNumber?: RawDetailLocality | null;
};
export type RawDetailResponse = {
  success: boolean;
  data: RawDetailData;
};
export type Pagination = {
  total: number;
  totalPages: number;
  perPage: number;
  currentPage: number;
};
export type Location = {
  id: number;
  title: string;
};
export type Offer = {
  id: number;
  title: string;
  area: number;
  description: string;
  disposition?:
    | (
        | "onePlusKk"
        | "onePlusOne"
        | "twoPlusKk"
        | "twoPlusOne"
        | "threePlusKk"
        | "threePlusOne"
        | "fourPlusKk"
        | "fourPlusOne"
        | "fivePlusOne"
        | "fivePlusKk"
        | "sixAndMore"
        | "atypical"
        | null
      )
    | null;
  houseType?:
    | (
        | "cottage"
        | "monumentOther"
        | "familyHouse"
        | "villa"
        | "turnKey"
        | "chalet"
        | "agriculturalFarm"
        | "multiGeneration"
        | null
      )
    | null;
  geoCoordinates: GeoCoordinates;
  photos: Array<Photo>;
  rentalPrice?: PriceResponse | null;
  isNoCommission: boolean;
  depositPrice?: PriceResponse | null;
  monthlyFeesPrice?: PriceResponse | null;
  priceUnit?:
    | (
        | "perRealEstate"
        | "perMonth"
        | "perSqM"
        | "perSqMPerMonth"
        | "perSqMPerYear"
        | "perYear"
        | "perDay"
        | "perHour"
        | "perSqMPerDay"
        | "perSqMPerHour"
      )
    | null;
  published: string;
  seo: string;
  street?: Location | null;
  village: Location;
  villagePart?: Location | null;
  convenience: Array<
    | "balcony"
    | "loggia"
    | "terrace"
    | "washingMachine"
    | "fridge"
    | "dishwasher"
    | "cellar"
    | "garden"
    | "lift"
  >;
  houseConvenience: Array<
    "cellar" | "garage" | "pool" | "parking" | "wheelchairAccessible"
  >;
  floorLevel?: number | null;
  availableFrom?: string | null;
  priceNote?: string | null;
  offerType: "sale" | "rent" | "auctions" | "coliving";
  propertyType: "flat" | "house" | "land" | "commercial" | "other";
  adminUrl?: string | null;
  showScamWarn: boolean;
  isContacted: boolean;
  isTop: boolean;
  absoluteUrl: string;
};
export type Mark = {
  geoCoordinates: GeoCoordinates;
  offerIds: Array<number>;
};
export type FindData = {
  offers: Array<Offer>;
  markers: Array<Mark>;
};
export type FindResponse = {
  success: boolean;
  extraData: Pagination;
  data: FindData;
};
export type LatestResponse = {
  offers: Array<Offer>;
};
export type LatestRequest = {
  propertyType: "flat" | "house" | "land" | "commercial" | "other";
  offerType: "sale" | "rent" | "auctions" | "coliving";
};
export type OfferRelated = {
  id: number;
  title: string;
  area: number;
  description: string;
  disposition?:
    | (
        | "onePlusKk"
        | "onePlusOne"
        | "twoPlusKk"
        | "twoPlusOne"
        | "threePlusKk"
        | "threePlusOne"
        | "fourPlusKk"
        | "fourPlusOne"
        | "fivePlusOne"
        | "fivePlusKk"
        | "sixAndMore"
        | "atypical"
        | null
      )
    | null;
  houseType?:
    | (
        | "cottage"
        | "monumentOther"
        | "familyHouse"
        | "villa"
        | "turnKey"
        | "chalet"
        | "agriculturalFarm"
        | "multiGeneration"
        | null
      )
    | null;
  photos: Array<Photo>;
  rentalPrice?: PriceResponse | null;
  commissionPrice?: boolean | null;
  depositPrice?: PriceResponse | null;
  monthlyFeesPrice?: PriceResponse | null;
  published: string;
  seo: string;
  street?: Location | null;
  village: Location;
  villagePart?: Location | null;
  convenience: Array<
    | "balcony"
    | "loggia"
    | "terrace"
    | "washingMachine"
    | "fridge"
    | "dishwasher"
    | "cellar"
    | "garden"
    | "lift"
  >;
  houseConvenience: Array<
    "cellar" | "garage" | "pool" | "parking" | "wheelchairAccessible"
  >;
  floorLevel?: number | null;
  availableFrom?: string | null;
  priceNote?: string | null;
  offerType: "sale" | "rent" | "auctions" | "coliving";
  propertyType: "flat" | "house" | "land" | "commercial" | "other";
};
export type RelatedData = {
  offers: Array<OfferRelated>;
};
export type RelatedResponse = {
  success: boolean;
  data: RelatedData;
};
export type StatusChangeResponse = {
  success: boolean;
};
export type StatusChangeRequest = {
  offerId: number;
  status: "activate" | "deactivate" | "delete";
};
export type PhoneCheckData = {
  verified: boolean;
};
export type PhoneCheckResponse = {
  success: boolean;
  data: PhoneCheckData;
};
export type PhoneResendData = {
  id: string;
  nextSmsAllowedTime?: number;
};
export type PhoneResendResponse = {
  success: boolean;
  data: PhoneResendData;
};
export type PhoneResendRequest = {
  phone?: Phone;
  sentSmsId: string;
};
export type PhoneSendData = {
  id: string;
  nextSmsAllowedTime?: number;
};
export type PhoneSendResponse = {
  success: boolean;
  data: PhoneSendData;
};
export type PhoneSendRequest = {
  phone: Phone;
};
export const {
  useGetRobotsTxtQuery,
  useLazyGetRobotsTxtQuery,
  useGetV1HealthQuery,
  useLazyGetV1HealthQuery,
  usePostV1LandlordUpdateMutation,
  useGetV1MakeDataStoresByDataIdQuery,
  useLazyGetV1MakeDataStoresByDataIdQuery,
  usePostV1OfferCountMutation,
  usePostV1OfferCreateMutation,
  useGetV1OfferDataQuery,
  useLazyGetV1OfferDataQuery,
  useGetV1OfferDetailQuery,
  useLazyGetV1OfferDetailQuery,
  useGetV1OfferDetailRawQuery,
  useLazyGetV1OfferDetailRawQuery,
  usePostV1OfferFindMutation,
  useGetV1OfferLatestQuery,
  useLazyGetV1OfferLatestQuery,
  useGetV1OfferRelatedQuery,
  useLazyGetV1OfferRelatedQuery,
  usePutV1OfferStatusChangeMutation,
  useGetV1VerifyPhoneCheckQuery,
  useLazyGetV1VerifyPhoneCheckQuery,
  usePostV1VerifyPhoneResendMutation,
  usePostV1VerifyPhoneSendMutation,
} = injectedRtkApi;
