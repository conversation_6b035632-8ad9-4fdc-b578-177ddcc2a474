// yarn cypress run --headless --spec "cypress/e2e/checkPayment.cy.ts"

import { faker } from "@faker-js/faker";

import { urls } from "../constants/ulrs";
import * as payment from "../pages/payment";
import * as core from "../pages/core";

const basicUser = {
  login: `${faker.word.noun().toLowerCase()}-${Math.round(
    Date.now() / 1000000,
  )}@ulovdomov.cz`,
  password: "testujeme",
};

describe("Payment option slector check", () => {
  before(() => {
    cy.apiRegister(basicUser);
  });

  beforeEach(() => {
    cy.intercept("POST", "**/fe-api/payment/go-pay-process").as("goPayProcess");
    cy.apiLogin(basicUser);
    cy.visit(urls.PREMIUM.WHY);
    core.waitForPageLoaded();
    payment.goToPayForPremium();
    core.waitForPageLoaded();
  });

  it("Check GoPay Payment ", () => {
    payment.selectPayment("goPay");
    cy.wait("@goPayProcess")
      .its("response")
      .then((resp) => {
        expect(resp?.statusCode).equal(200);
      });
  });

  it("Check CreditCard Payment ", () => {
    payment.selectPayment("byCard");
    cy.wait("@goPayProcess")
      .its("response")
      .then((resp) => {
        expect(resp?.statusCode).equal(200);
      });
  });

  it("Check Bank Transfer Payment ", () => {
    payment.selectPayment("moneyTransfer");
    cy.wait("@goPayProcess")
      .its("response")
      .then((resp) => {
        expect(resp?.statusCode).equal(200);
      });
  });
});
