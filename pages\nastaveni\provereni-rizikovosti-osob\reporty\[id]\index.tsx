import { NextPage } from "next";

import ReportDetail from "scenes/CreditCheckNew/reportDetail";
import useRouter from "hooks/useRouter";
const Index: NextPage = () => {
  const router = useRouter();

  let paymentStatus: "success" | "error" | undefined;
  if (router.query?.platba === "zaplaceno") {
    paymentStatus = "success";
  } else if (router.query?.platba === "nezaplaceno") {
    paymentStatus = "error";
  }

  return <ReportDetail paymentStatus={paymentStatus} />;
};

export default Index;
