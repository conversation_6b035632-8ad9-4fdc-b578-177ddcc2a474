import { NextPage } from "next";

import useGlobalState from "hooks/useGlobalState";
import withAuth from "hooks/withAuth";
import PremiumServiceWrapper from "scenes/PremiumService";
import TopBoard from "scenes/TopBoard";

const Index: NextPage = () => {
  const { featureToggles } = useGlobalState();
  if (!featureToggles?.activeTopBoards.length) {
    return <PremiumServiceWrapper scrollHook="top" />;
  }

  return <TopBoard />;
};

export default withAuth(Index);
