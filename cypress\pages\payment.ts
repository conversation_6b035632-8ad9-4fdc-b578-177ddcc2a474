import { waitForPageLoaded } from "./core";

const el = {
  byCard: "Platební karta",
  moneyTransfer: "Rychlý bankovní převod",
  goPay: "GoPay",
  mPayment: "m-platbank",
  submitPayment: "submitPaymetMethod",
  activePremiumButton: "activatePremiumAccount",
  startPacket: "startPacketValue",
  vipPacket: "vipPacketValue",
  paymentH1: "paymentH1",
  paymentBox: "paymentBox",
  promoCodeButton: "userPromoCodeButton",
  promoCodeModal: "promoCodeModal",
  promoCodeInput: "promoCodeInput",
  promoCodeSubmit: "promoCodeSubmitButton",
  confirmModal: "alertModal",
  submitModal: "alertModal.button",
};

type paymentType = keyof typeof el;

export function selectPayment(paymentType: paymentType) {
  cy.getByTestId(el[paymentType]).scrollIntoView().click({ force: true });
  cy.getByTestId(el.submitPayment).click();
}

export function goToPayForPremium() {
  cy.getByTestId(el.activePremiumButton).click();
}

export function selectStandartPacketPayment() {
  cy.getByTestId(el.startPacket).should("be.visible");
  cy.getByTestId(el.vipPacket).should("be.visible");
  cy.intercept("GET", "**/wallet/list-packages").as("wallet");
  cy.getByTestId("standardPacketValue")
    .should("be.visible")
    .find("a")
    .click({ force: true });
  waitForPageLoaded();
  cy.wait("@wallet")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.equal(200);
    });
}

export function userPromoCode(promoCode: string) {
  cy.getByTestId(el.paymentH1).should("be.visible");
  cy.getByTestId(el.paymentBox).should("be.visible");
  cy.getByTestId(el.promoCodeButton).click({ force: true });
  cy.intercept("POST", "**/payment/promo-code").as("promoCodeValidate");
  cy.getByTestId(el.promoCodeModal).within(() => {
    cy.getByTestId(el.promoCodeInput).type(promoCode);
    cy.getByTestId(el.promoCodeSubmit).click();
  });
  cy.wait("@promoCodeValidate")
    .its("response")
    .then((resp) => {
      expect(resp?.statusCode).to.equal(200);
    });
  cy.getByTestId(el.confirmModal).within(() => {
    cy.getByTestId(el.submitModal).click();
  });
  cy.getByTestId(el.confirmModal).should("not.exist");
}
