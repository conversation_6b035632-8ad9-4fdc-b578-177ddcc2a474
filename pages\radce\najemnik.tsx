import { NextPage, GetServerSideProps } from "next";
import {
  StoryData,
  getStoryblokApi,
  useStoryblokState,
  StoryblokComponent,
} from "@storyblok/react";
import { captureException } from "@sentry/nextjs";

const Page: NextPage<{ story: StoryData; preview: boolean }> = ({
  story: initialStory,
  preview,
}) => {
  const story = useStoryblokState(initialStory, {}, preview);

  return <StoryblokComponent blok={story.content} preview={preview} />;
};
export default Page;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const storyblokApi = getStoryblokApi();
  const apiParams = {
    version: context.preview ? "draft" : undefined,
    token: context.preview
      ? process.env.NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN
      : undefined,
    resolve_relations: ["root_card.root", "intro_header.root"],
  };
  const res = await storyblokApi
    .get("cdn/stories/radce/najemnik", apiParams)
    .catch((error) => {
      captureException(error);
    });
  if (res && "data" in res) {
    return {
      props: {
        story: res.data.story,
        key: res.data.story.id,
        preview: context.preview || null,
      },
    };
  }

  return {
    notFound: true,
  };
};
