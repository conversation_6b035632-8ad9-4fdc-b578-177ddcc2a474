import { Flex, Text } from "@chakra-ui/react";
import <PERSON><PERSON> from "joi";
import { useEffect } from "react";

import { Section } from "./Section";
import { useFilterErrors, useFilterState } from "../context";
import { IconCoins } from "@ud/icons";
import useForm, { OnFormSubmitType } from "hooks/useForm";
import { SelectInputItemType, SelectInp } from "../SelectInp";

const priceMinItems: Array<SelectInputItemType> = [
  { label: "nezáleží", value: "" },
  { label: "6 000 Kč", value: "6000" },
  { label: "10 000 Kč", value: "10000" },
  { label: "12 000 Kč", value: "12000" },
  { label: "14 000 Kč", value: "14000" },
];

const priceMaxItems: Array<SelectInputItemType> = [
  { label: "10 000 Kč", value: "10000" },
  { label: "12 000 Kč", value: "12000" },
  { label: "16 000 Kč", value: "16000" },
  { label: "18 000 Kč", value: "18000" },
  { label: "nezáleží", value: "" },
];

const priceMinItemsSale: Array<SelectInputItemType> = [
  { label: "nezáleží", value: "" },
  { label: "500 000 Kč", value: "500000" },
  { label: "1 000 000 Kč", value: "1000000" },
  { label: "1 500 000 Kč", value: "1500000" },
  { label: "2 000 000 Kč", value: "2000000" },
];

const priceMaxItemsSale: Array<SelectInputItemType> = [
  { label: "1 000 000 Kč", value: "1000000" },
  { label: "1 500 000 Kč", value: "1500000" },
  { label: "2 000 000 Kč", value: "2000000" },
  { label: "3 000 000 Kč", value: "3000000" },
  { label: "nezáleží", value: "" },
];

type FormValues = {
  priceMin: string;
  priceMax: string;
};

export const Price: React.FC = () => {
  const {
    state: { price, offerType },
    setState,
  } = useFilterState();

  const { setErrorsState } = useFilterErrors();

  const FormSchema = Joi.object({
    priceMin: Joi.string()
      .allow("")
      .custom((value, helpers) => {
        if (watchMin && watchMax) {
          const min = parseInt(watchMin);
          const max = parseInt(watchMax);
          if (max < min) {
            return helpers.error("number.max");
          }
        }

        return value;
      }, "custom validation"),
    priceMax: Joi.string().allow(""),
  });

  const { registerWithError, handleSubmit, watch, errors, setValue } =
    useForm<FormValues>(FormSchema, {
      defaultValues: {
        priceMin: price.min?.toString() || "",
        priceMax: price.max?.toString() || "",
      },
      keepDataOnSuccess: true,
    });

  const watchMin = watch("priceMin");
  const watchMax = watch("priceMax");

  const handleData: OnFormSubmitType<FormValues> = ({ priceMin, priceMax }) => {
    setState((current) => ({
      ...current,
      price: {
        min: priceMin ? parseInt(priceMin) : undefined,
        max: priceMax ? parseInt(priceMax) : undefined,
      },
    }));
  };

  const handler = () => {
    handleSubmit(handleData, (error) => {
      setErrorsState((current) => [...current, error]);
    })();
  };

  useEffect(() => {
    setState((current) => ({
      ...current,
      price: {
        min: watchMin ? parseInt(watchMin) : undefined,
        max: watchMax ? parseInt(watchMax) : undefined,
      },
    }));
  }, [watchMin, watchMax, setState]);

  useEffect(() => {
    if (Object.keys(errors).length === 0) {
      // Vyčistite chyby pre tento komponent z errorsState
      setErrorsState((current) =>
        current.filter((errorObj) => !errorObj.priceMin && !errorObj.priceMax),
      );
    }
  }, [errors, setErrorsState]);

  return (
    <Section icon={<IconCoins boxSize="16px" />} title="Cena za nemovitost">
      <form autoComplete="off" onBlur={handler} onSubmit={handler}>
        <Flex justify="space-between">
          <Text mt="7px" mr={["5px", "10px"]}>
            od
          </Text>
          <SelectInp
            setValue={(option) => setValue("priceMin", option)}
            options={offerType === "sale" ? priceMinItemsSale : priceMinItems}
            {...registerWithError("priceMin")}
            type="number"
            unit="Kč"
            error={errors.priceMin}
          />
          <Text ml={["10px", "24px"]} mr={["5px", "10px"]} mt="7px">
            do
          </Text>
          <SelectInp
            setValue={(option) => setValue("priceMax", option)}
            options={offerType === "sale" ? priceMaxItemsSale : priceMaxItems}
            {...registerWithError("priceMax")}
            type="number"
            unit="Kč"
            error={errors.priceMax}
          />
        </Flex>
      </form>
    </Section>
  );
};
