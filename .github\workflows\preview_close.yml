name: K8S Staging PR - Close

on:
  pull_request:
    types: [closed]

env:
  PROJECT: ud-fe

concurrency:
  group: preview-${{ github.event.number }}
  cancel-in-progress: true

jobs:
  close:
    timeout-minutes: 5
    runs-on: [self-hosted, k8s-stage-fast]
    name: <PERSON><PERSON><PERSON><PERSON> prostředí - zav<PERSON><PERSON>í
    steps:
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Set up kubectl
        uses: matootie/dokube@v1.4.0
        with:
          personalAccessToken: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          clusterName: ulovdomov-dev
          namespace: ${{ env.PROJECT }}

      - uses: azure/setup-helm@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Delete release
        run: helm uninstall ${{ env.PROJECT }}-${{ github.event.number }} -n ${{ env.PROJECT }}

      - name: Delete Docker image
        run: |
          doctl registry repository delete-tag -f ud-fe-stage ${{ github.event.number }}

      - uses: strumwolf/delete-deployment-environment@v2
        name: Remove GitHub deployments
        if: github.event.action == 'closed'
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          environment: "${{ env.PROJECT }}-${{ github.event.number }}"
          onlyRemoveDeployments: true

      - uses: strumwolf/delete-deployment-environment@v2
        name: Remove GitHub deployments
        if: github.event.action == 'closed'
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          environment: "${{ env.PROJECT }}-${{ github.event.number }}-docs"
          onlyRemoveDeployments: true
