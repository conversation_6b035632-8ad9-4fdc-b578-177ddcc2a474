import { useEffect } from "react";
import { apiPlugin, storyblokInit } from "@storyblok/react";
import Head from "next/head";
import { ChakraProvider } from "@chakra-ui/react";
import { AppProps } from "next/app";
import axios from "axios";
import { Provider } from "react-redux";
import dynamic from "next/dynamic";
import { DefaultSeo } from "next-seo";
import Script from "next/script";

import "i18n";

import GlobalContextProvider from "contexts/GlobalState";
import theme from "src/themes/appTheme";
import TrackingProvider from "contexts/Tracking";
import DevTools from "components/DevTools";
import { store } from "src/store";
import { components } from "src/cms/components";
import FlagsmithProvider from "contexts/Flagsmith";
import SEO from "config/seoConfig";
import ExitWatchdogProvider from "contexts/ExitWatchdog";
import GlobalInfoProvider from "contexts/GlobalInfo";
import { CustomAppPage } from "src/types/types";
import Modals from "modals/ModalsProvider";
import InfoNavProvider from "contexts/InfoNav";
import UlovdomovLayout from "components/Layout";
import useRouter from "hooks/useRouter";
import LoginRedirectHandler from "components/LoginRedirectHandler";

const DevEnv = dynamic(() => import("components/DevEnv"), {
  ssr: false,
});

axios.defaults.withCredentials = true;

storyblokInit({
  accessToken: process.env.NEXT_PUBLIC_STORYBLOK_PUBLIC_TOKEN,
  apiOptions: {
    cache: { type: "memory" },
  },
  use: [apiPlugin],
  components,
});

type CustomAppProps = Omit<AppProps, "Component"> & {
  Component: CustomAppPage;
};

const MyApp = ({ Component, pageProps }: CustomAppProps) => {
  const { navigateTo } = useRouter();

  useEffect(() => {
    localStorage.removeItem("chakra-ui-color-mode");
    window.googletag = window.googletag || { cmd: [] };
  }, []);

  return (
    <>
      <Head>
        <meta charSet="utf-8" />
        <meta name="author" content="UlovDomov.cz, s.r.o." />
        <meta name="keywords" content="pronájem, byt, bytu, spolubydleni" />
        <meta
          name="viewport"
          content="width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0, maximum-scale=5"
        />
        <link rel="shortcut icon" href="/favicon.ico?v=2" />
        <link rel="apple-touch-icon" href="/favicon.png" />
        <link rel="icon" type="image/png" href="/favicon.png" />
        <meta name="theme-color" content="#FFFFFF" />
        <meta name="msapplication-navbutton-color" content="#FFFFFF" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta
          name="apple-mobile-web-app-status-bar-style"
          content="black-translucent"
        />
        <link rel="apple-touch-icon" sizes="76x76" href="/favicon-76.png" />
        <link rel="apple-touch-icon" sizes="120x120" href="/favicon-120.png" />
        <link rel="apple-touch-icon" sizes="152x152" href="/favicon-152.png" />
        <link
          rel="icon"
          type="image/png"
          sizes="16x16"
          href="/favicon-16x16.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="32x32"
          href="/favicon-32x32.png"
        />
        <link rel="mask-icon" href="/favicon.svg?v=1" color="#e23f35" />
        <meta
          name="google-site-verification"
          content="v7KrQtYtN9sHlX3nE-TqirBGgLy6i0w2BLMIk5CEyCE"
        />
      </Head>
      <DefaultSeo {...SEO} />

      <noscript>
        <p>
          Bohužel to ale vypádá, že nemáte
          <a href="http://www.enable-javascript.com/cz/">zapnutý JavaScript</a>
          ve vašem webovém prohlížeči. Bez něj to nepojede ;o(
        </p>
      </noscript>

      <Provider store={store}>
        <GlobalContextProvider>
          <FlagsmithProvider>
            <ChakraProvider resetCSS theme={theme}>
              <TrackingProvider>
                <GlobalInfoProvider>
                  <ExitWatchdogProvider>
                    <DevTools>
                      <Modals>
                        <LoginRedirectHandler navigateTo={navigateTo} />

                        <InfoNavProvider>
                          <UlovdomovLayout
                            hideFooter={Component.hasHiddenFooter}
                          >
                            <Component {...pageProps} />
                          </UlovdomovLayout>
                          <div id="portalRoot" />
                          <DevEnv />
                        </InfoNavProvider>
                      </Modals>
                    </DevTools>
                  </ExitWatchdogProvider>
                </GlobalInfoProvider>
              </TrackingProvider>
            </ChakraProvider>
          </FlagsmithProvider>
        </GlobalContextProvider>
      </Provider>
      <Script
        id="clarityID"
        type="text/javascript"
        dangerouslySetInnerHTML={{
          __html: `(function(c,l,a,r,i,t,y){ c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)}; t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i; y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y); })(window, document, "clarity", "script", "pqojt3of80");`,
        }}
      />
      <Script
        id="daktelaId"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `// Set webchat configuration
            var daktelaGuiConfig = {
            "server":"https://ulovdomov.daktela.com/",
            "accessToken":"s9r31rn54qp445sqoqsq111q72432445"
            };
            // Create async script element
            var daktelaWeb = null;
            var daktelaScriptEl = document.createElement("script");
            (function (attrs) { Object.keys(attrs).forEach(function (key) { daktelaScriptEl.setAttribute(key, attrs[key]); }); })
            ({"src":daktelaGuiConfig.server+"external/web/web.js", "type":"text/javascript", "async":true, "charset":"utf-8"});
            daktelaScriptEl.onload = function() { if (!daktelaGui) return; daktelaWeb = new daktelaGui(); daktelaWeb.init(daktelaGuiConfig); };
            document.getElementsByTagName("head")[0].appendChild(daktelaScriptEl);`,
        }}
      />
    </>
  );
};

export default MyApp;
