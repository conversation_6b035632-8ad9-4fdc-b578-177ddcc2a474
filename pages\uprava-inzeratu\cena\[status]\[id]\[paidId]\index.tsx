import { GetServerSideProps } from "next/types";

import InsertOfferWrapper from "components/InsertOfferWrapper";
import PriceForm from "scenes/InsertOffer/form/Price";
import { CustomAppPage, EditOfferPaid } from "src/types/types";

const Price: CustomAppPage<EditOfferPaid> = (props) => (
  <InsertOfferWrapper title="Údaje o ceně a dostupnosti" {...props}>
    <PriceForm buttonNextText="Další" />
  </InsertOfferWrapper>
);

Price.hasHiddenFooter = true;

export default Price;

export const getServerSideProps: GetServerSideProps = async ({ query }) => ({
  props: {
    id: query.id as string,
    status: query.status as string,
    paidId: query.paidId as string,
  },
});
