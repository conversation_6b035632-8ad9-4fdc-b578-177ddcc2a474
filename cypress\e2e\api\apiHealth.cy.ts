import { getUdApiUrl } from "../../helper/helper";

describe("UD API Health", () => {
  it("Check If Health Is OK", () => {
    cy.request({
      method: "GET",
      url: `${getUdApiUrl()}/health`,
    })
      .then((resp) => {
        expect(resp.status).to.eq(200);
        expect(resp.body.data).to.have.property("ok");
      })
      .then((resp) => {
        expect(resp.body.data.ok).to.eq(true);
      });
  });
});
