import { Link as CharkaLink, LinkProps } from "@chakra-ui/react";
import NextLink from "next/link";
import React from "react";

import { TrackEventProps, useTracking } from "contexts/Tracking";
import { IDataTestProps } from "utils/css";

interface ISearchLinkProps extends LinkProps, IDataTestProps {
  trackEventPayload?: TrackEventProps;
  showUnderline?: boolean;
  _disabled?: { opacity?: number; cursor?: string };
}

const SearchLink = React.forwardRef<HTMLAnchorElement, ISearchLinkProps>(
  (
    { children, trackEventPayload, onClick, dataTest, _disabled, ...props },
    ref,
  ) => {
    const { trackEvent } = useTracking();

    return (
      <CharkaLink
        as={NextLink}
        prefetch={false}
        shallow
        ref={ref}
        data-test={dataTest}
        _focus={{ boxShadow: "none" }}
        onClick={(e) => {
          if (_disabled) {
            e.preventDefault();

            return;
          }
          onClick && onClick(e);
          trackEventPayload && trackEvent(trackEventPayload);
        }}
        href={props.href}
        sx={_disabled}
        {...props}
      >
        {children}
      </CharkaLink>
    );
  },
);

export default SearchLink;
