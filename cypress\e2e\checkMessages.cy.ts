// yarn cypress run --headless --spec "cypress/e2e/checkMessages.cy.ts"

import { faker } from "@faker-js/faker";

import * as api from "../pages/apiHelp";
import * as offer from "../pages/offer";
import * as mess from "../pages/messages";
import { urls } from "../constants/ulrs";
import { waitForPageLoaded } from "../pages/core";
import * as payment from "../pages/payment";

const promoCode = "theonecode";

const seller = {
  login: "<EMAIL>",
  password: "testujeme",
  name: "tester",
  surname: "testovic",
  message: faker.lorem.sentence(),
};

const buyer = {
  login: "<EMAIL>",
  password: "testujeme",
  phone: "607011090",
  message: faker.lorem.sentence(),
  message2: faker.lorem.sentence(),
  name: faker.person.firstName(),
  surname: faker.person.lastName(),
};

let offerId: any, accessToken: any;

// mergovali jsme kvůli konfliktům - je třeba ještě potřeba dod<PERSON>
describe.skip("Messenger", () => {
  before(() => {
    // cy.apiRegister(buyer);
    // cy.apiRegister(seller);
  });

  after(() => {
    api.deleteOffer(offerId);
  });

  it("test", () => {
    cy.apiLogin(seller);
    api.createOffer(seller);
    cy.then(() => {
      offerId = localStorage.getItem("offerId");
      cy.visit(urls.MY_OFFERS.URL);
      offer.publishOffer(offerId);
      cy.url().should("include", `${offerId}`);
      payment.selectStandartPacketPayment();

      payment.userPromoCode(promoCode);
      cy.getByTestId("m-platba").click();
      cy.getByTestId("submitPaymetMethod").click();
      waitForPageLoaded();

      // musime ještě zaplatit
      cy.refreshSeassion();
      api.apiLoginForTests(buyer);
      cy.then(() => {
        accessToken = localStorage.getItem("test-accessToken");
        cy.log(accessToken);
        api.sendMessage(accessToken, offerId, buyer.phone, buyer.message);

        cy.apiLogin(seller);
        // kontrola notifikace
        cy.visit(urls.SCHRANKA);
        mess.checkSpecificMessage(buyer.message);
        mess.checkNameOfBuyer();
        mess.typeAndSendMessage(seller.message);
        mess.checkSpecificMessage(seller.message);

        api.deleteOffer(offerId);
      });
    });
  });
});
