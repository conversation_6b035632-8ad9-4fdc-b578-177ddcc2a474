export const landLordCard = {
  first_name: "Tester",
  last_name: "<PERSON><PERSON><PERSON>",
  contact_phone: "+420777777777",
  user_landlord_type_id: 1,
  company_name: "realEstateee",
};

export const rentFlatRealEstateBroker = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: ["lift", "garden"],
  description: "test offer",
  disposition: "twoPlusKk",
  energyEfficiencyRating: "c",
  floorLevel: 1,
  furnishing: "no",
  usableArea: 123,
  monthlyFeesPrice: 5000,
  price: 15000,
  streetNumber: 8356200,
  flatType: "loft",
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  offerType: "rent",
  priceCommission: true,
  surroundingType: ["residential"],
  floorCount: 2,
  priceNote: "test",
  propertyType: "flat",
  typeInsert: "realEstateBroker",
  depositPrice: 30000,
};

export const rentFlatRealEstateBrokerEdited = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: ["lift", "garden"],
  description: "test offer",
  disposition: "threePlusKk",
  energyEfficiencyRating: "e",
  floorLevel: 1,
  furnishing: "no",
  usableArea: 1234,
  monthlyFeesPrice: 10000,
  price: 15000,
  streetNumber: 8356200,
  flatType: "loft",
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  offerType: "rent",
  priceCommission: true,
  surroundingType: ["residential"],
  floorCount: 2,
  priceNote: "test",
  propertyType: "flat",
  typeInsert: "realEstateBroker",
  depositPrice: 30000,
};

export const rentHouseRealEstateBroker = {
  availability: "asap",
  buildingType: "multiStorey",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "test offer",
  disposition: "familyHouse",
  energyEfficiencyRating: "a",
  estateArea: 134,
  furnishing: "no",
  usableArea: 1234,
  monthlyFeesPrice: 8000,
  price: 14000,
  streetNumber: 8356200,
  objectType: "row",
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  protectedArea: "protectedLandscapeArea",
  offerType: "rent",
  priceCommission: true,
  roomCount: "twoRooms",
  surroundingType: ["residential"],
  builtUpArea: 1234,
  priceNote: "test",
  propertyType: "house",
  typeInsert: "realEstateBroker",
  depositPrice: 15000,
};

export const rentFlatLandlord = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: ["lift", "garden"],
  description: "test offer",
  disposition: "twoPlusKk",
  energyEfficiencyRating: "c",
  floorLevel: 1,
  furnishing: "no",
  usableArea: 123,
  monthlyFeesPrice: 5000,
  price: 15000,
  streetNumber: 8356200,
  flatType: "loft",
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  ownership: "personal",
  offerType: "rent",
  priceCommission: true,
  surroundingType: ["residential"],
  floorCount: 2,
  priceNote: "test",
  propertyType: "flat",
  typeInsert: "privateLandlord",
  depositPrice: 30000,
};

export const rentHouseLandlord = {
  availability: "asap",
  buildingType: "multiStorey",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "test offer",
  disposition: "familyHouse",
  energyEfficiencyRating: "a",
  estateArea: 134,
  furnishing: "no",
  usableArea: 1234,
  monthlyFeesPrice: 8000,
  price: 14000,
  streetNumber: 8356200,
  objectType: "row",
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  ownership: "personal",
  protectedArea: "protectedLandscapeArea",
  offerType: "rent",
  priceCommission: true,
  roomCount: "twoRooms",
  surroundingType: ["residential"],
  builtUpArea: 1234,
  priceNote: "test",
  propertyType: "house",
  typeInsert: "privateLandlord",
  depositPrice: 15000,
};

export const sellFlatRealEstateBroker = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "afdS",
  disposition: "onePlusKk",
  energyEfficiencyRating: "a",
  floorLevel: 1,
  furnishing: "no",
  usableArea: 123,
  price: 1234543,
  street: 7377,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  offerType: "sale",
  priceCommission: true,
  surroundingType: ["residential"],
  propertyType: "flat",
  typeInsert: "realEstateBroker",
};

export const sellHouseRealEstateBroker = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "dafdas",
  disposition: "familyHouse",
  energyEfficiencyRating: "a",
  estateArea: 1230,
  furnishing: "no",
  usableArea: 123,
  price: 12345213,
  street: 7680,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  offerType: "sale",
  priceCommission: true,
  roomCount: "twoRooms",
  surroundingType: ["residential"],
  builtUpArea: 1234,
  propertyType: "house",
  typeInsert: "realEstateBroker",
};

export const sellFlatLandlord = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "afdS",
  disposition: "onePlusKk",
  energyEfficiencyRating: "a",
  floorLevel: 1,
  furnishing: "no",
  usableArea: 123,
  price: 1234543,
  street: 7377,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  ownership: "personal",
  offerType: "sale",
  priceCommission: true,
  surroundingType: ["residential"],
  propertyType: "flat",
  typeInsert: "privateLandlord",
};

export const sellHouseLandlord = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "dafdas",
  disposition: "familyHouse",
  energyEfficiencyRating: "a",
  estateArea: 1230,
  furnishing: "no",
  usableArea: 123,
  price: 12345213,
  street: 7680,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  ownership: "personal",
  offerType: "sale",
  priceCommission: true,
  roomCount: "twoRooms",
  surroundingType: ["residential"],
  builtUpArea: 1234,
  propertyType: "house",
  typeInsert: "privateLandlord",
};

export const colivingFlatFullRoomRealEstate = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "ewfa",
  energyEfficiencyRating: "a",
  floorLevel: 1,
  furnishing: "no",
  usableArea: 123,
  price: 12342,
  street: 39465,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  offerType: "coliving",
  priceCommission: true,
  surroundingType: ["residential"],
  colivingType: "fullRoom",
  propertyType: "flat",
  typeInsert: "realEstateBroker",
};

export const colivingFlatFullRoomLandlord = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "ewfa",
  energyEfficiencyRating: "a",
  floorLevel: 1,
  furnishing: "no",
  usableArea: 123,
  price: 12342,
  street: 39465,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  ownership: "personal",
  offerType: "coliving",
  priceCommission: true,
  surroundingType: ["residential"],
  colivingType: "fullRoom",
  propertyType: "flat",
  typeInsert: "privateLandlord",
};

export const colivingFlatSharedRoomRealEstate = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "efasd",
  energyEfficiencyRating: "a",
  floorLevel: 1,
  furnishing: "no",
  usableArea: 123,
  price: 1234,
  street: 25986,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  offerType: "coliving",
  priceCommission: true,
  surroundingType: ["residential"],
  colivingType: "sharedRoom",
  propertyType: "flat",
  typeInsert: "realEstateBroker",
};

export const colivingFlatSharedRoomLandlord = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "efasd",
  energyEfficiencyRating: "a",
  floorLevel: 1,
  furnishing: "no",
  usableArea: 123,
  price: 1234,
  street: 25986,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  ownership: "personal",
  offerType: "coliving",
  priceCommission: false,
  surroundingType: ["residential"],
  colivingType: "sharedRoom",
  propertyType: "flat",
  typeInsert: "privateLandlord",
};

export const colivingHouseFullRoomRealEstate = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "asf",
  energyEfficiencyRating: "a",
  estateArea: 1231,
  furnishing: "no",
  usableArea: 1234,
  price: 12443,
  street: 41834,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  offerType: "coliving",
  priceCommission: true,
  surroundingType: ["residential"],
  colivingType: "fullRoom",
  propertyType: "house",
  typeInsert: "realEstateBroker",
};

export const colivingHouseFullRoomLandlord = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "asf",
  energyEfficiencyRating: "a",
  estateArea: 1231,
  furnishing: "no",
  usableArea: 1234,
  price: 12443,
  street: 41834,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  ownership: "personal",
  offerType: "coliving",
  priceCommission: false,
  surroundingType: ["residential"],
  colivingType: "fullRoom",
  propertyType: "house",
  typeInsert: "privateLandlord",
};

export const colivingHouseSharedRoomRealEstate = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "asf",
  energyEfficiencyRating: "a",
  estateArea: 1231,
  furnishing: "no",
  usableArea: 1234,
  price: 12443,
  street: 41834,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  offerType: "coliving",
  priceCommission: true,
  surroundingType: ["residential"],
  colivingType: "sharedRoom",
  propertyType: "house",
  typeInsert: "realEstateBroker",
};

export const colivingHouseSharedRoomLandlord = {
  availability: "asap",
  buildingType: "groundFloor",
  buildingCondition: "veryGood",
  conveniences: [],
  description: "asf",
  energyEfficiencyRating: "a",
  estateArea: 1231,
  furnishing: "no",
  usableArea: 1234,
  price: 12443,
  street: 41834,
  houseConveniences: [],
  locationType: "cityCenter",
  material: "brick",
  ownership: "personal",
  offerType: "coliving",
  priceCommission: false,
  surroundingType: ["residential"],
  colivingType: "sharedRoom",
  propertyType: "house",
  typeInsert: "privateLandlord",
};
